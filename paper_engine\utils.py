from .models import ScoreTracker

def calculate_points(test_submission):
    """Calculate points based on test performance, including section-wise evaluation."""
    cutoffs = {"total": 50, "sections": [15, 17, 18]}  # Example cutoffs
    margin = test_submission.total_score - cutoffs["total"]

    section_scores = test_submission.section_scores  # Dictionary with section names and scores
    section_results = {
        section: score >= cutoff
        for section, score, cutoff in zip(section_scores.keys(), section_scores.values(), cutoffs["sections"])
    }

    # Determine points based on section performance
    passed_sections = sum(section_results.values())
    
    if passed_sections == len(cutoffs["sections"]):
        points = margin * 3
    elif passed_sections == 2:
        points = margin * 1
    elif passed_sections == 1:
        points = margin * -1
    else:
        points = margin * -3

    if points < 0:
        points = 0

    return points, section_results

from datetime import datetime
def apply_consistency_bonus(user, points, test_submission):
    """Apply consistency multiplier."""
    tracker, _ = ScoreTracker.objects.get_or_create(user=user)
    
    # Ensure both values are of type `date`
    test_date = test_submission.date.date() if isinstance(test_submission.date, datetime) else test_submission.date
    last_test_date = tracker.last_test_date
    
    if last_test_date:
        days_since_last_test = (test_date - last_test_date).days
    else:
        days_since_last_test = 7  # Default to 7 days if no previous test

    multiplier = 25  # Example multiplier
    return points * multiplier * days_since_last_test

