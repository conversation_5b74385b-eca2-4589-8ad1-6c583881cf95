from rest_framework.permissions import BasePermission
from rest_framework.exceptions import PermissionDenied
from .models import ContributorProfile
from customrcare.models import CustomrcareProfile


class IsContributorUser(BasePermission):
    def has_permission(self, request, view):
        user = request.user

        if user.is_authenticated:
            try:
                contributor = ContributorProfile.objects.get(user=user)
                if contributor.role == "contributor":
                    return True
                raise PermissionDenied(
                    detail="You do not have permission for this resource."
                )
            except ContributorProfile.DoesNotExist:
                raise PermissionDenied(detail="Contributor profile not found.")
        raise PermissionDenied(detail="Authentication required.")

class IsCustomerCareOrReadOnly(BasePermission):
    """
    Custom permission to only allow customercare users to edit it.
    Read-only permissions are allowed to any request.
    """

    def has_permission(self, request, view):
        if request.method in ('GET'):
            return True
        return CustomrcareProfile.objects.filter(user=request.user).exists()