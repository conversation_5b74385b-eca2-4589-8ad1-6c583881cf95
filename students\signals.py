# from django.contrib.auth.signals import user_logged_in
# from django.dispatch import receiver
# from django.utils.timezone import now
# from datetime import timedelta
# from .models import Student

# @receiver(user_logged_in)
# def update_student_streaks(sender, request, user, **kwargs):
#     """User login hone pe streaks update karega."""
#     today = now().date()
    
#     try:
#         student = user.student_profile  # related_name="student_profile" se access kar rahe hain
#     except Student.DoesNotExist:
#         return

#     if student.last_login_date:
#         last_login_date = student.last_login_date
        
#         if last_login_date == today:
#             return  # Already logged in today

#         elif last_login_date == today - timedelta(days=1):
#             student.streats += 1  # Consecutive login, streak badhao
#         else:
#             student.streats = 1  # Streak break ho gayi, reset to 1
#     else:
#         student.streats = 1  # First login

#     student.last_login_date = today
#     student.save()
