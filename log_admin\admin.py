from django.contrib import admin
from django.utils.html import format_html
from django.urls import reverse
from django.utils.safestring import mark_safe
from django.db.models import Count, Avg
from django.utils import timezone
from datetime import timedelta
import json

from .models import (
    LogConfig, PerformanceLog, ErrorLog, UserActivity,
    APIAccessLog, DatabaseQueryLog, AuthenticationLog,
    SecurityIncident, SystemHealthLog
)

# Register your models here.


@admin.register(LogConfig)
class LogConfigAdmin(admin.ModelAdmin):
    """Enhanced admin interface for logging configuration"""

    list_display = [
        'name', 'is_active_display', 'level', 'feature_summary',
        'retention_summary', 'alert_status', 'updated_at', 'updated_by'
    ]

    list_filter = [
        'is_active', 'level', 'enable_performance_logging', 'enable_error_logging',
        'enable_activity_logging', 'enable_api_logging', 'enable_auth_logging',
        'enable_security_logging', 'enable_email_alerts', 'created_at'
    ]

    search_fields = ['name', 'description', 'alert_email_addresses']

    readonly_fields = [
        'created_at', 'updated_at', 'created_by', 'updated_by',
        'alert_emails_display', 'config_summary'
    ]

    fieldsets = (
        ('Basic Information', {
            'fields': ('name', 'description', 'is_active')
        }),
        ('Logging Level', {
            'fields': ('level',),
            'description': 'Set the minimum logging level to capture'
        }),
        ('Output Configuration', {
            'fields': (
                'enable_file_logging', 'enable_console_logging', 'enable_database_logging'
            ),
            'description': 'Configure where logs should be written'
        }),
        ('Feature Toggles', {
            'fields': (
                'enable_performance_logging', 'enable_error_logging', 'enable_activity_logging',
                'enable_api_logging', 'enable_db_logging', 'enable_auth_logging',
                'enable_security_logging', 'enable_health_logging'
            ),
            'description': 'Enable or disable specific logging features'
        }),
        ('Performance Settings', {
            'fields': ('max_log_entries', 'batch_size', 'flush_interval'),
            'description': 'Configure logging performance parameters'
        }),
        ('Retention Policies', {
            'fields': ('log_retention_days', 'error_retention_days', 'security_retention_days'),
            'description': 'Set how long different types of logs are retained'
        }),
        ('Alert Configuration', {
            'fields': (
                'enable_email_alerts', 'alert_email_addresses', 'alert_emails_display',
                'critical_error_threshold', 'alert_cooldown_minutes'
            ),
            'description': 'Configure email alerts for critical issues'
        }),
        ('Advanced Settings', {
            'fields': (
                'enable_log_compression', 'enable_log_rotation', 'max_log_file_size_mb',
                'enable_sampling', 'sampling_rate'
            ),
            'description': 'Advanced logging configuration options',
            'classes': ('collapse',)
        }),
        ('Metadata', {
            'fields': ('created_at', 'updated_at', 'created_by', 'updated_by', 'config_summary'),
            'classes': ('collapse',)
        })
    )

    actions = ['activate_configuration', 'test_configuration', 'duplicate_configuration']

    def is_active_display(self, obj):
        """Display active status with visual indicator"""
        if obj.is_active:
            return format_html('<span style="color: green; font-weight: bold;">✓ Active</span>')
        return format_html('<span style="color: gray;">○ Inactive</span>')
    is_active_display.short_description = 'Status'
    is_active_display.admin_order_field = 'is_active'

    def feature_summary(self, obj):
        """Display summary of enabled features"""
        features = []
        if obj.enable_performance_logging:
            features.append('Perf')
        if obj.enable_error_logging:
            features.append('Error')
        if obj.enable_activity_logging:
            features.append('Activity')
        if obj.enable_api_logging:
            features.append('API')
        if obj.enable_auth_logging:
            features.append('Auth')
        if obj.enable_security_logging:
            features.append('Security')

        return ', '.join(features) if features else 'None'
    feature_summary.short_description = 'Enabled Features'

    def retention_summary(self, obj):
        """Display retention policy summary"""
        return f"General: {obj.log_retention_days}d, Error: {obj.error_retention_days}d, Security: {obj.security_retention_days}d"
    retention_summary.short_description = 'Retention Policy'

    def alert_status(self, obj):
        """Display alert configuration status"""
        if obj.enable_email_alerts:
            email_count = len(obj.get_alert_emails())
            return format_html(
                '<span style="color: green;">✓ Enabled ({} emails)</span>',
                email_count
            )
        return format_html('<span style="color: gray;">○ Disabled</span>')
    alert_status.short_description = 'Alerts'

    def alert_emails_display(self, obj):
        """Display formatted alert email addresses"""
        emails = obj.get_alert_emails()
        if emails:
            return format_html('<br>'.join(emails))
        return "No alert emails configured"
    alert_emails_display.short_description = 'Alert Email Addresses'

    def config_summary(self, obj):
        """Display comprehensive configuration summary"""
        summary = f"""
        <strong>Configuration Summary:</strong><br>
        Level: {obj.level}<br>
        Active Features: {self.feature_summary(obj)}<br>
        Retention: {self.retention_summary(obj)}<br>
        Max Entries: {obj.max_log_entries:,}<br>
        Batch Size: {obj.batch_size}<br>
        Flush Interval: {obj.flush_interval}s<br>
        """

        if obj.enable_sampling:
            summary += f"Sampling: {obj.sampling_rate * 100:.1f}%<br>"

        if obj.enable_email_alerts:
            summary += f"Alert Threshold: {obj.critical_error_threshold} errors<br>"
            summary += f"Alert Cooldown: {obj.alert_cooldown_minutes} minutes<br>"

        return format_html(summary)
    config_summary.short_description = 'Configuration Summary'

    def activate_configuration(self, request, queryset):
        """Activate selected configuration"""
        if queryset.count() != 1:
            self.message_user(request, "Please select exactly one configuration to activate.", level='ERROR')
            return

        config = queryset.first()

        # Deactivate all other configurations
        LogConfig.objects.filter(is_active=True).update(is_active=False)

        # Activate selected configuration
        config.is_active = True
        config.updated_by = request.user
        config.save()

        # Clear cache
        from django.core.cache import cache
        cache.delete('log_config')
        cache.delete('active_log_config')

        self.message_user(request, f'Configuration "{config.name}" has been activated.')
    activate_configuration.short_description = "Activate selected configuration"

    def test_configuration(self, request, queryset):
        """Test selected configurations"""
        for config in queryset:
            # Generate test log entries
            from .utils import LoggingUtils

            LoggingUtils.log_user_activity(
                user=request.user,
                activity_type='ADMIN_ACTION',
                action='Configuration test',
                description=f'Tested logging configuration: {config.name}',
                request=request,
                metadata={'config_id': config.id, 'test': True}
            )

        self.message_user(request, f'Test logs generated for {queryset.count()} configuration(s).')
    test_configuration.short_description = "Test selected configurations"

    def duplicate_configuration(self, request, queryset):
        """Duplicate selected configurations"""
        for config in queryset:
            # Create a copy
            config.pk = None
            config.name = f"{config.name} (Copy)"
            config.is_active = False
            config.created_by = request.user
            config.updated_by = request.user
            config.save()

        self.message_user(request, f'Duplicated {queryset.count()} configuration(s).')
    duplicate_configuration.short_description = "Duplicate selected configurations"

    def save_model(self, request, obj, form, change):
        """Override save to track user changes"""
        if not change:  # Creating new object
            obj.created_by = request.user
        obj.updated_by = request.user
        super().save_model(request, obj, form, change)


@admin.register(PerformanceLog)
class PerformanceLogAdmin(admin.ModelAdmin):
    list_display = [
        'path', 'method', 'duration_display', 'status_code',
        'user', 'db_queries_count', 'memory_usage_display', 'created_at'
    ]
    list_filter = [
        'method', 'status_code', 'created_at',
    ]
    search_fields = ['path', 'user__username', 'ip_address']
    readonly_fields = [
        'path', 'method', 'duration', 'memory_usage', 'cpu_usage',
        'db_queries_count', 'db_queries_time', 'user', 'user_agent',
        'ip_address', 'response_size', 'status_code', 'created_at'
    ]
    date_hierarchy = 'created_at'
    ordering = ['-created_at']
    list_per_page = 50

    def duration_display(self, obj):
        if obj.duration > 2.0:
            return format_html(
                '<span style="color: red; font-weight: bold;">{:.3f}s</span>',
                obj.duration
            )
        elif obj.duration > 1.0:
            return format_html(
                '<span style="color: orange;">{:.3f}s</span>',
                obj.duration
            )
        return f"{obj.duration:.3f}s"
    duration_display.short_description = 'Duration'
    duration_display.admin_order_field = 'duration'

    def memory_usage_display(self, obj):
        if obj.memory_usage:
            return f"{obj.memory_usage:.2f} MB"
        return "-"
    memory_usage_display.short_description = 'Memory'

    def get_queryset(self, request):
        return super().get_queryset(request).select_related('user')


@admin.register(ErrorLog)
class ErrorLogAdmin(admin.ModelAdmin):
    list_display = [
        'view_name', 'error_type', 'severity_display', 'user',
        'resolved_display', 'timestamp'
    ]
    list_filter = [
        'error_type', 'severity', 'resolved', 'timestamp',
        'status_code'
    ]
    search_fields = [
        'view_name', 'error_message', 'user__username',
        'ip_address', 'path'
    ]
    readonly_fields = [
        'view_name', 'error_type', 'error_message', 'stack_trace',
        'request_data_display', 'response_data_display', 'user',
        'ip_address', 'user_agent', 'path', 'method', 'status_code',
        'timestamp'
    ]
    fields = [
        'view_name', 'error_type', 'severity', 'error_message',
        'stack_trace', 'request_data_display', 'response_data_display',
        'user', 'ip_address', 'user_agent', 'path', 'method',
        'status_code', 'resolved', 'resolved_by', 'resolved_at',
        'resolution_notes', 'timestamp'
    ]
    date_hierarchy = 'timestamp'
    ordering = ['-timestamp']
    list_per_page = 50
    actions = ['mark_resolved', 'mark_unresolved']

    def severity_display(self, obj):
        colors = {
            'LOW': 'green',
            'MEDIUM': 'orange',
            'HIGH': 'red',
            'CRITICAL': 'darkred'
        }
        return format_html(
            '<span style="color: {}; font-weight: bold;">{}</span>',
            colors.get(obj.severity, 'black'),
            obj.severity
        )
    severity_display.short_description = 'Severity'
    severity_display.admin_order_field = 'severity'

    def resolved_display(self, obj):
        if obj.resolved:
            return format_html(
                '<span style="color: green;">✓ Resolved</span>'
            )
        return format_html(
            '<span style="color: red;">✗ Unresolved</span>'
        )
    resolved_display.short_description = 'Status'
    resolved_display.admin_order_field = 'resolved'

    def request_data_display(self, obj):
        if obj.request_data:
            return format_html(
                '<pre style="max-height: 200px; overflow-y: auto;">{}</pre>',
                json.dumps(obj.request_data, indent=2)
            )
        return "-"
    request_data_display.short_description = 'Request Data'

    def response_data_display(self, obj):
        if obj.response_data:
            return format_html(
                '<pre style="max-height: 200px; overflow-y: auto;">{}</pre>',
                json.dumps(obj.response_data, indent=2)
            )
        return "-"
    response_data_display.short_description = 'Response Data'

    def mark_resolved(self, request, queryset):
        queryset.update(
            resolved=True,
            resolved_by=request.user,
            resolved_at=timezone.now()
        )
        self.message_user(request, f"Marked {queryset.count()} errors as resolved.")
    mark_resolved.short_description = "Mark selected errors as resolved"

    def mark_unresolved(self, request, queryset):
        queryset.update(
            resolved=False,
            resolved_by=None,
            resolved_at=None
        )
        self.message_user(request, f"Marked {queryset.count()} errors as unresolved.")
    mark_unresolved.short_description = "Mark selected errors as unresolved"

    def get_queryset(self, request):
        return super().get_queryset(request).select_related('user', 'resolved_by')


@admin.register(UserActivity)
class UserActivityAdmin(admin.ModelAdmin):
    list_display = [
        'user', 'activity_type', 'action', 'success_display',
        'ip_address', 'timestamp'
    ]
    list_filter = [
        'activity_type', 'success', 'timestamp'
    ]
    search_fields = [
        'user__username', 'action', 'description',
        'ip_address', 'path'
    ]
    readonly_fields = [
        'user', 'activity_type', 'action', 'description',
        'ip_address', 'user_agent', 'session_key', 'path',
        'method', 'success', 'metadata_display', 'timestamp'
    ]
    date_hierarchy = 'timestamp'
    ordering = ['-timestamp']
    list_per_page = 50

    def success_display(self, obj):
        if obj.success:
            return format_html('<span style="color: green;">✓</span>')
        return format_html('<span style="color: red;">✗</span>')
    success_display.short_description = 'Success'
    success_display.admin_order_field = 'success'

    def metadata_display(self, obj):
        if obj.metadata:
            return format_html(
                '<pre style="max-height: 150px; overflow-y: auto;">{}</pre>',
                json.dumps(obj.metadata, indent=2)
            )
        return "-"
    metadata_display.short_description = 'Metadata'

    def get_queryset(self, request):
        return super().get_queryset(request).select_related('user')


@admin.register(APIAccessLog)
class APIAccessLogAdmin(admin.ModelAdmin):
    list_display = [
        'endpoint', 'method', 'response_status_display', 'user',
        'response_time_display', 'rate_limited_display', 'timestamp'
    ]
    list_filter = [
        'method', 'response_status', 'rate_limited',
        'cached_response', 'timestamp'
    ]
    search_fields = [
        'endpoint', 'user__username', 'ip_address', 'api_key'
    ]
    readonly_fields = [
        'endpoint', 'method', 'user', 'ip_address', 'user_agent',
        'request_headers_display', 'request_body_display',
        'response_status', 'response_size', 'response_time',
        'api_key', 'rate_limited', 'cached_response', 'timestamp'
    ]
    date_hierarchy = 'timestamp'
    ordering = ['-timestamp']
    list_per_page = 50

    def response_status_display(self, obj):
        if 200 <= obj.response_status < 300:
            color = 'green'
        elif 300 <= obj.response_status < 400:
            color = 'blue'
        elif 400 <= obj.response_status < 500:
            color = 'orange'
        else:
            color = 'red'

        return format_html(
            '<span style="color: {}; font-weight: bold;">{}</span>',
            color, obj.response_status
        )
    response_status_display.short_description = 'Status'
    response_status_display.admin_order_field = 'response_status'

    def response_time_display(self, obj):
        if obj.response_time > 2.0:
            return format_html(
                '<span style="color: red; font-weight: bold;">{:.3f}s</span>',
                obj.response_time
            )
        elif obj.response_time > 1.0:
            return format_html(
                '<span style="color: orange;">{:.3f}s</span>',
                obj.response_time
            )
        return f"{obj.response_time:.3f}s"
    response_time_display.short_description = 'Response Time'
    response_time_display.admin_order_field = 'response_time'

    def rate_limited_display(self, obj):
        if obj.rate_limited:
            return format_html('<span style="color: red;">⚠ Limited</span>')
        return format_html('<span style="color: green;">✓ OK</span>')
    rate_limited_display.short_description = 'Rate Limit'
    rate_limited_display.admin_order_field = 'rate_limited'

    def request_headers_display(self, obj):
        if obj.request_headers:
            return format_html(
                '<pre style="max-height: 150px; overflow-y: auto;">{}</pre>',
                json.dumps(obj.request_headers, indent=2)
            )
        return "-"
    request_headers_display.short_description = 'Request Headers'

    def request_body_display(self, obj):
        if obj.request_body:
            return format_html(
                '<pre style="max-height: 150px; overflow-y: auto;">{}</pre>',
                json.dumps(obj.request_body, indent=2)
            )
        return "-"
    request_body_display.short_description = 'Request Body'

    def get_queryset(self, request):
        return super().get_queryset(request).select_related('user')


@admin.register(DatabaseQueryLog)
class DatabaseQueryLogAdmin(admin.ModelAdmin):
    list_display = [
        'query_type', 'table_name', 'execution_time_display',
        'rows_affected', 'slow_query_display', 'user', 'timestamp'
    ]
    list_filter = [
        'query_type', 'slow_query', 'timestamp'
    ]
    search_fields = [
        'table_name', 'view_name', 'user__username', 'query_hash'
    ]
    readonly_fields = [
        'query_type', 'table_name', 'query_hash', 'execution_time',
        'rows_affected', 'query_plan', 'user', 'view_name',
        'slow_query', 'timestamp'
    ]
    date_hierarchy = 'timestamp'
    ordering = ['-timestamp']
    list_per_page = 50

    def execution_time_display(self, obj):
        if obj.execution_time > 1.0:
            return format_html(
                '<span style="color: red; font-weight: bold;">{:.3f}s</span>',
                obj.execution_time
            )
        elif obj.execution_time > 0.5:
            return format_html(
                '<span style="color: orange;">{:.3f}s</span>',
                obj.execution_time
            )
        return f"{obj.execution_time:.3f}s"
    execution_time_display.short_description = 'Execution Time'
    execution_time_display.admin_order_field = 'execution_time'

    def slow_query_display(self, obj):
        if obj.slow_query:
            return format_html('<span style="color: red;">🐌 Slow</span>')
        return format_html('<span style="color: green;">⚡ Fast</span>')
    slow_query_display.short_description = 'Speed'
    slow_query_display.admin_order_field = 'slow_query'

    def get_queryset(self, request):
        return super().get_queryset(request).select_related('user')


@admin.register(AuthenticationLog)
class AuthenticationLogAdmin(admin.ModelAdmin):
    list_display = [
        'username_display', 'event_type', 'success_display',
        'ip_address', 'timestamp'
    ]
    list_filter = [
        'event_type', 'success', 'timestamp'
    ]
    search_fields = [
        'user__username', 'username_attempted', 'ip_address',
        'failure_reason'
    ]
    readonly_fields = [
        'user', 'username_attempted', 'event_type', 'ip_address',
        'user_agent', 'session_key', 'success', 'failure_reason',
        'additional_data_display', 'timestamp'
    ]
    date_hierarchy = 'timestamp'
    ordering = ['-timestamp']
    list_per_page = 50

    def username_display(self, obj):
        if obj.user:
            return obj.user.username
        return obj.username_attempted or "Unknown"
    username_display.short_description = 'Username'

    def success_display(self, obj):
        if obj.success:
            return format_html('<span style="color: green;">✓ Success</span>')
        return format_html('<span style="color: red;">✗ Failed</span>')
    success_display.short_description = 'Result'
    success_display.admin_order_field = 'success'

    def additional_data_display(self, obj):
        if obj.additional_data:
            return format_html(
                '<pre style="max-height: 150px; overflow-y: auto;">{}</pre>',
                json.dumps(obj.additional_data, indent=2)
            )
        return "-"
    additional_data_display.short_description = 'Additional Data'

    def get_queryset(self, request):
        return super().get_queryset(request).select_related('user')


@admin.register(SecurityIncident)
class SecurityIncidentAdmin(admin.ModelAdmin):
    list_display = [
        'incident_type', 'severity_display', 'ip_address',
        'blocked_display', 'resolved_display', 'timestamp'
    ]
    list_filter = [
        'incident_type', 'severity', 'blocked', 'resolved', 'timestamp'
    ]
    search_fields = [
        'description', 'ip_address', 'user__username', 'path'
    ]
    readonly_fields = [
        'incident_type', 'description', 'ip_address', 'user_agent',
        'user', 'path', 'request_data_display', 'blocked', 'timestamp'
    ]
    fields = [
        'incident_type', 'severity', 'description', 'ip_address',
        'user_agent', 'user', 'path', 'request_data_display',
        'blocked', 'resolved', 'resolved_by', 'resolved_at',
        'resolution_notes', 'timestamp'
    ]
    date_hierarchy = 'timestamp'
    ordering = ['-timestamp']
    list_per_page = 50
    actions = ['mark_resolved', 'mark_blocked']

    def severity_display(self, obj):
        colors = {
            'LOW': 'green',
            'MEDIUM': 'orange',
            'HIGH': 'red',
            'CRITICAL': 'darkred'
        }
        return format_html(
            '<span style="color: {}; font-weight: bold;">{}</span>',
            colors.get(obj.severity, 'black'),
            obj.severity
        )
    severity_display.short_description = 'Severity'
    severity_display.admin_order_field = 'severity'

    def blocked_display(self, obj):
        if obj.blocked:
            return format_html('<span style="color: red;">🚫 Blocked</span>')
        return format_html('<span style="color: green;">✓ Allowed</span>')
    blocked_display.short_description = 'Status'
    blocked_display.admin_order_field = 'blocked'

    def resolved_display(self, obj):
        if obj.resolved:
            return format_html('<span style="color: green;">✓ Resolved</span>')
        return format_html('<span style="color: red;">⚠ Open</span>')
    resolved_display.short_description = 'Resolution'
    resolved_display.admin_order_field = 'resolved'

    def request_data_display(self, obj):
        if obj.request_data:
            return format_html(
                '<pre style="max-height: 150px; overflow-y: auto;">{}</pre>',
                json.dumps(obj.request_data, indent=2)
            )
        return "-"
    request_data_display.short_description = 'Request Data'

    def mark_resolved(self, request, queryset):
        queryset.update(
            resolved=True,
            resolved_by=request.user,
            resolved_at=timezone.now()
        )
        self.message_user(request, f"Marked {queryset.count()} incidents as resolved.")
    mark_resolved.short_description = "Mark selected incidents as resolved"

    def mark_blocked(self, request, queryset):
        queryset.update(blocked=True)
        self.message_user(request, f"Marked {queryset.count()} incidents as blocked.")
    mark_blocked.short_description = "Mark selected incidents as blocked"

    def get_queryset(self, request):
        return super().get_queryset(request).select_related('user', 'resolved_by')


@admin.register(SystemHealthLog)
class SystemHealthLogAdmin(admin.ModelAdmin):
    list_display = [
        'metric_type', 'value_display', 'unit', 'status_display', 'timestamp'
    ]
    list_filter = [
        'metric_type', 'status', 'timestamp'
    ]
    search_fields = ['metric_type']
    readonly_fields = [
        'metric_type', 'value', 'unit', 'threshold_warning',
        'threshold_critical', 'status', 'additional_info_display', 'timestamp'
    ]
    date_hierarchy = 'timestamp'
    ordering = ['-timestamp']
    list_per_page = 50

    def value_display(self, obj):
        if obj.status == 'CRITICAL':
            return format_html(
                '<span style="color: red; font-weight: bold;">{}</span>',
                obj.value
            )
        elif obj.status == 'WARNING':
            return format_html(
                '<span style="color: orange; font-weight: bold;">{}</span>',
                obj.value
            )
        return str(obj.value)
    value_display.short_description = 'Value'
    value_display.admin_order_field = 'value'

    def status_display(self, obj):
        colors = {
            'OK': 'green',
            'WARNING': 'orange',
            'CRITICAL': 'red'
        }
        return format_html(
            '<span style="color: {}; font-weight: bold;">{}</span>',
            colors.get(obj.status, 'black'),
            obj.status
        )
    status_display.short_description = 'Status'
    status_display.admin_order_field = 'status'

    def additional_info_display(self, obj):
        if obj.additional_info:
            return format_html(
                '<pre style="max-height: 150px; overflow-y: auto;">{}</pre>',
                json.dumps(obj.additional_info, indent=2)
            )
        return "-"
    additional_info_display.short_description = 'Additional Info'