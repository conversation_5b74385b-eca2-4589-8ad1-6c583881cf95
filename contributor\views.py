from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework import status
from rest_framework_simplejwt.tokens import RefreshToken
from rest_framework.permissions import IsAuthenticated

from blogs.models import BlogPost
from contributor.models import ContributorPoints, ContributorProfile
from questions.models import (
    MasterOption,
    MasterQuestion,
    PreviousYearQuestion,
    Question,
)
from .serializers import (
    ContributorProfileSerializer,
    ContributorRegistrationSerializer,
    LoginSerializer,
)

from .permissions import IsContributorUser, IsCustomerCareOrReadOnly
from django.utils.timezone import now, timedelta
from rest_framework.status import HTTP_200_OK, HTTP_400_BAD_REQUEST
from django.shortcuts import get_object_or_404
    
from rest_framework import generics
from .models import Banner, PageCounter
from .serializers import BannerSerializer
from rest_framework.decorators import api_view
import json
from django.contrib.contenttypes.models import ContentType
from django.db.models import Count
from .throttles import LoginRateThrottle, RegisterRateThrottle
from rest_framework.decorators import api_view, throttle_classes

@throttle_classes([RegisterRateThrottle])
class RegisterView(APIView):

    def post(self, request):
        # breakpoint()
        serializer = ContributorRegistrationSerializer(data=request.data)
        if serializer.is_valid():
            user = serializer.save()
            refresh = RefreshToken.for_user(user)
            return Response(
                {
                    "profile": serializer.data,
                    "refresh": str(refresh),
                    "access": str(refresh.access_token),
                    "message": "User registered successfully",
                },
                status=status.HTTP_201_CREATED,
            )
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

    def get(self, request, slug=None):
        """Retrieve a contributor profile or list all profiles."""
        if slug:
            profile = get_object_or_404(ContributorProfile, slug=slug)
            serializer = ContributorProfileSerializer(profile)
            return Response(serializer.data, status=status.HTTP_200_OK)
        profiles = ContributorProfile.objects.all()[::-1]
        serializer = ContributorProfileSerializer(profiles, many=True)
        return Response(serializer.data, status=status.HTTP_200_OK)

    def put(self, request, slug):
        """Update a contributor profile."""
        profile = get_object_or_404(ContributorProfile, slug=slug)
        serializer = ContributorRegistrationSerializer(
            profile, data=request.data, partial=True
        )

        if serializer.is_valid():
            serializer.save()
            return Response(serializer.data, status=status.HTTP_200_OK)

        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

    def delete(self, request, slug):
        """Delete a contributor profile."""
        profile = get_object_or_404(ContributorProfile, slug=slug)
        profile.user.delete()  # Deletes the related user account
        profile.delete()
        return Response(
            {"message": "Profile deleted successfully"},
            status=status.HTTP_204_NO_CONTENT,
        )

from log_admin.models import UserActivity
@throttle_classes([LoginRateThrottle])
class LoginView(APIView):
    def post(self, request):
        serializer = LoginSerializer(data=request.data)
        if serializer.is_valid():
            user = serializer.validated_data["user"]
            role = serializer.validated_data["role"]
            contributor_profile = user.contributor_profile
            refresh = RefreshToken.for_user(user)
            if role == "contributor":
                contributor_profile = getattr(user, 'contributor_profile', None)
                UserActivity.objects.create(user=user, action="Contributor Login", metadata={"path": request.path})

                return Response(
                    {
                        "profile": {
                            "username": user.username,
                            "email": user.email,
                            "first_name": user.first_name,
                            "last_name": user.last_name,
                        },
                        "contributor_profile_id": contributor_profile.id,
                        "role": role,
                        "refresh": str(refresh),
                        "access": str(refresh.access_token),
                    },
                    status=status.HTTP_200_OK,
                )
        return Response(serializer.errors, status=status.HTTP_401_UNAUTHORIZED)


class LogoutView(APIView):
    permission_classes = (IsContributorUser,)

    def post(self, request):
        try:
            refresh_token = request.data["refresh"]
            token = RefreshToken(refresh_token)
            token.blacklist()
            return Response(
                {"message": "Logged out successfully"},
                status=status.HTTP_204_NO_CONTENT,
            )
        except Exception as e:
            return Response({"error": str(e)}, status=status.HTTP_400_BAD_REQUEST)


class TokenRefreshView(APIView):
    def post(self, request):
        refresh_token = request.data.get("refresh")
        if not refresh_token:
            return Response(
                {"error": "Refresh token is required"},
                status=status.HTTP_400_BAD_REQUEST,
            )
        try:
            refresh = RefreshToken(refresh_token)
            return Response(
                {
                    "access": str(refresh.access_token),
                },
                status=status.HTTP_200_OK,
            )
        except Exception as e:
            return Response({"error": str(e)}, status=status.HTTP_401_UNAUTHORIZED)


class ContributorDashboardAPIView(APIView):
    permission_classes = (IsAuthenticated,)

    def get(self, request):
        try:
            # Get the logged-in contributor
            contributor = ContributorProfile.objects.get(user=request.user)

            # Current date and time
            today = now().date()
            month_start = today.replace(day=1)
            week_start = today - timedelta(days=today.weekday())
            previous_month_start = (month_start - timedelta(days=1)).replace(day=1)
            previous_month_end = month_start - timedelta(days=1)
            third_month_start = (previous_month_start - timedelta(days=1)).replace(
                day=1
            )
            third_month_end = previous_month_start - timedelta(days=1)

            # Fetch all related data created by the contributor
            all_questions = Question.objects.filter(author=contributor)
            master_questions = MasterQuestion.objects.filter(author=contributor)
            master_options = MasterOption.objects.filter(author=contributor)
            blogs = BlogPost.objects.filter(author=contributor)
            previous_questions = PreviousYearQuestion.objects.filter(
                question__author=contributor
            )

            # Function to get counts for each status
            def get_status_counts(queryset):
                return {
                    "created": queryset.count(),
                    "approved": queryset.filter(approval_status="approved").count(),
                    "pending": queryset.filter(approval_status="pending").count(),
                    "rejected": queryset.filter(approval_status="rejected").count(),
                }

            # Summaries for all data types
            data_summary = {
                "questions": {
                    "total": get_status_counts(all_questions),
                },
                "master_questions": {
                    "total": get_status_counts(master_questions),
                },
                "master_options": {
                    "total": get_status_counts(master_options),
                },
                "blogs": {
                    "total": get_status_counts(blogs),
                },
                "previous_questions": {
                    "total": get_status_counts(previous_questions),
                },
            }

            # Filters for each time period
            def get_period_data(queryset, period_start, period_end=None):
                if period_end:
                    return queryset.filter(
                        created_at__date__gte=period_start,
                        created_at__date__lte=period_end,
                    )
                return queryset.filter(created_at__date__gte=period_start)

            current_month_data = {
                "questions": {
                    "daily": get_status_counts(
                        all_questions.filter(created_at__date=today)
                    ),
                    "weekly": get_status_counts(
                        get_period_data(all_questions, week_start)
                    ),
                    "monthly": get_status_counts(
                        get_period_data(all_questions, month_start)
                    ),
                },
                "master_questions": {
                    "daily": get_status_counts(
                        master_questions.filter(created_at__date=today)
                    ),
                    "weekly": get_status_counts(
                        get_period_data(master_questions, week_start)
                    ),
                    "monthly": get_status_counts(
                        get_period_data(master_questions, month_start)
                    ),
                },
                "blogs": {
                    "daily": get_status_counts(blogs.filter(created_at__date=today)),
                    "weekly": get_status_counts(get_period_data(blogs, week_start)),
                    "monthly": get_status_counts(get_period_data(blogs, month_start)),
                },
                "master_options": {
                    "daily": get_status_counts(
                        master_options.filter(created_at__date=today)
                    ),
                    "weekly": get_status_counts(
                        get_period_data(master_options, week_start)
                    ),
                    "monthly": get_status_counts(
                        get_period_data(master_options, month_start)
                    ),
                },
                "previous_questions": {
                    "daily": get_status_counts(
                        previous_questions.filter(created_at__date=today)
                    ),
                    "weekly": get_status_counts(
                        get_period_data(previous_questions, week_start)
                    ),
                    "monthly": get_status_counts(
                        get_period_data(previous_questions, month_start)
                    ),
                },
            }

            previous_month_data = {
                "questions": {
                    "monthly": get_status_counts(
                        get_period_data(
                            all_questions, previous_month_start, previous_month_end
                        )
                    ),
                },
                "master_questions": {
                    "monthly": get_status_counts(
                        get_period_data(
                            master_questions, previous_month_start, previous_month_end
                        )
                    ),
                },
                "blogs": {
                    "monthly": get_status_counts(
                        get_period_data(blogs, previous_month_start, previous_month_end)
                    ),
                },
                "master_options": {
                    "monthly": get_status_counts(
                        get_period_data(
                            master_options, previous_month_start, previous_month_end
                        )
                    ),
                },
                "previous_questions": {
                    "monthly": get_status_counts(
                        get_period_data(
                            previous_questions, previous_month_start, previous_month_end
                        )
                    ),
                },
            }

            third_month_data = {
                "questions": {
                    "monthly": get_status_counts(
                        get_period_data(
                            all_questions, third_month_start, third_month_end
                        )
                    ),
                },
                "master_questions": {
                    "monthly": get_status_counts(
                        get_period_data(
                            master_questions, third_month_start, third_month_end
                        )
                    ),
                },
                "blogs": {
                    "monthly": get_status_counts(
                        get_period_data(blogs, third_month_start, third_month_end)
                    ),
                },
                "master_options": {
                    "monthly": get_status_counts(
                        get_period_data(
                            master_options, third_month_start, third_month_end
                        )
                    ),
                },
                "previous_questions": {
                    "monthly": get_status_counts(
                        get_period_data(
                            previous_questions, third_month_start, third_month_end
                        )
                    ),
                },
            }

            # Points calculation helper
            def calculate_points(queryset, multiplier):
                return queryset.count() * multiplier

            # Get points multipliers from the database
            contributor_points = ContributorPoints.objects.last()
            if contributor_points:

                # Current month points
                current_month_points = {
                    "normal_questions": calculate_points(
                        get_period_data(all_questions, month_start),
                        contributor_points.normal_questions,
                    ),
                    "master_questions": calculate_points(
                        get_period_data(master_questions, month_start),
                        contributor_points.master_questions,
                    ),
                    "master_options": calculate_points(
                        get_period_data(master_options, month_start),
                        contributor_points.master_options,
                    ),
                    "blogs": calculate_points(
                        get_period_data(blogs, month_start),
                        contributor_points.blogs,
                    ),
                    "previous_questions": calculate_points(
                        get_period_data(previous_questions, month_start),
                        contributor_points.previous_questions,
                    ),
                }
                current_month_points["total_points"] = sum(current_month_points.values())

                # Previous month points
                previous_month_points = {
                    "normal_questions": calculate_points(
                        get_period_data(
                            all_questions, previous_month_start, previous_month_end
                        ),
                        contributor_points.normal_questions,
                    ),
                    "master_questions": calculate_points(
                        get_period_data(
                            master_questions, previous_month_start, previous_month_end
                        ),
                        contributor_points.master_questions,
                    ),
                    "master_options": calculate_points(
                        get_period_data(
                            master_options, previous_month_start, previous_month_end
                        ),
                        contributor_points.master_options,
                    ),
                    "blogs": calculate_points(
                        get_period_data(blogs, previous_month_start, previous_month_end),
                        contributor_points.blogs,
                    ),
                    "previous_questions": calculate_points(
                        get_period_data(
                            previous_questions, previous_month_start, previous_month_end
                        ),
                        contributor_points.previous_questions,
                    ),
                }
                previous_month_points["total_points"] = sum(previous_month_points.values())

                # Third month points
                third_month_points = {
                    "normal_questions": calculate_points(
                        get_period_data(all_questions, third_month_start, third_month_end),
                        contributor_points.normal_questions,
                    ),
                    "master_questions": calculate_points(
                        get_period_data(
                            master_questions, third_month_start, third_month_end
                        ),
                        contributor_points.master_questions,
                    ),
                    "master_options": calculate_points(
                        get_period_data(master_options, third_month_start, third_month_end),
                        contributor_points.master_options,
                    ),
                    "blogs": calculate_points(
                        get_period_data(blogs, third_month_start, third_month_end),
                        contributor_points.blogs,
                    ),
                    "previous_questions": calculate_points(
                        get_period_data(
                            previous_questions, third_month_start, third_month_end
                        ),
                        contributor_points.previous_questions,
                    ),
                }
                third_month_points["total_points"] = sum(third_month_points.values())

                # Prepare response
                response_data = {
                    "contributor": contributor.user.username,
                    "questions_summary": data_summary,
                    "current_month_data": current_month_data,
                    "previous_month_data": previous_month_data,
                    "third_month_data": third_month_data,
                    "current_month_points": current_month_points,
                    "previous_month_points": previous_month_points,
                    "third_month_points": third_month_points,
                }
            else:
                response_data = {
                    "contributor": contributor.user.username,
                    "questions_summary": data_summary,
                    "current_month_data": current_month_data,
                    "previous_month_data": previous_month_data,
                    "third_month_data": third_month_data,
                }

            return Response(response_data, status=HTTP_200_OK)

        except ContributorProfile.DoesNotExist:
            return Response(
                {"error": "Contributor profile not found."},
                status=HTTP_400_BAD_REQUEST,
            )
        except Exception as e:
            return Response(
                {"error": f"An unexpected error occurred: {str(e)}"},
                status=HTTP_400_BAD_REQUEST,
            )
        


class BannerListCreateView(generics.ListCreateAPIView):
    queryset = Banner.objects.all().order_by("-created_at")
    serializer_class = BannerSerializer
    permission_classes = [IsCustomerCareOrReadOnly]
class BannerRetrieveUpdateDestroyView(generics.RetrieveUpdateDestroyAPIView):
    queryset = Banner.objects.all().order_by("-created_at")
    serializer_class = BannerSerializer
    permission_classes = [IsCustomerCareOrReadOnly]


@api_view(["POST"])
def PageVisitors(request):
    if request.method == 'POST':
        data = json.loads(request.body) 
        for key, value in data.items():
            if key == "/":
                key = "/home"
            try:
                value = int(value)
            except ValueError:
                continue  # Skip invalid
            PageCounter.objects.create(url=key, count=value)
        return Response({"msg": "Data received successfully."})


@api_view(['GET'])
def get_all_model_counts(request):
    model_counts = {}

    # Django me registered sare models ke liye loop chalega
    for content_type in ContentType.objects.all():
        model_class = content_type.model_class()
        if model_class:  # Check if model exists
            try:
                # Use uppercase model class name instead of lowercase content_type.model
                model_counts[model_class.__name__] = model_class.objects.count()
            except Exception as e:
                # Handle database errors gracefully (e.g., missing tables)
                print(f"Could not count {model_class.__name__}: {e}")
                model_counts[model_class.__name__] = 0

    return Response(model_counts)
