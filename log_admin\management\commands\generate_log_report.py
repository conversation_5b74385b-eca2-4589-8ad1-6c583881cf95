"""
Management command to generate comprehensive log reports
"""

import json
import csv
from datetime import datetime, timedelta
from django.core.management.base import BaseCommand
from django.utils import timezone
from django.conf import settings
from log_admin.utils import LogAnalytics
from log_admin.models import (
    PerformanceLog, ErrorLog, UserActivity, APIAccessLog,
    DatabaseQueryLog, AuthenticationLog, SecurityIncident
)
import os


class Command(BaseCommand):
    help = 'Generate comprehensive log reports'
    
    def add_arguments(self, parser):
        parser.add_argument(
            '--days',
            type=int,
            default=7,
            help='Number of days to include in report (default: 7)',
        )
        parser.add_argument(
            '--format',
            choices=['json', 'csv', 'txt'],
            default='json',
            help='Output format (default: json)',
        )
        parser.add_argument(
            '--output',
            type=str,
            help='Output file path (default: auto-generated)',
        )
        parser.add_argument(
            '--include',
            nargs='+',
            choices=['errors', 'performance', 'activities', 'security', 'all'],
            default=['all'],
            help='Report sections to include',
        )
    
    def handle(self, *args, **options):
        days = options['days']
        output_format = options['format']
        output_path = options['output']
        include_sections = options['include']
        
        self.stdout.write(
            f"Generating {days}-day log report in {output_format} format..."
        )
        
        try:
            # Generate report data
            report_data = self.generate_report(days, include_sections)
            
            # Determine output path
            if not output_path:
                timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
                filename = f"log_report_{timestamp}.{output_format}"
                output_path = os.path.join(settings.BASE_DIR, 'log_reports', filename)
                
                # Create directory if it doesn't exist
                os.makedirs(os.path.dirname(output_path), exist_ok=True)
            
            # Write report
            if output_format == 'json':
                self.write_json_report(report_data, output_path)
            elif output_format == 'csv':
                self.write_csv_report(report_data, output_path)
            elif output_format == 'txt':
                self.write_text_report(report_data, output_path)
            
            self.stdout.write(
                self.style.SUCCESS(f'Report generated successfully: {output_path}')
            )
            
        except Exception as e:
            self.stdout.write(
                self.style.ERROR(f'Report generation failed: {str(e)}')
            )
            raise
    
    def generate_report(self, days, include_sections):
        """Generate comprehensive report data"""
        end_date = timezone.now()
        start_date = end_date - timedelta(days=days)
        
        report = {
            'metadata': {
                'generated_at': end_date.isoformat(),
                'period_start': start_date.isoformat(),
                'period_end': end_date.isoformat(),
                'period_days': days,
                'sections_included': include_sections
            }
        }
        
        if 'all' in include_sections or 'errors' in include_sections:
            report['error_summary'] = LogAnalytics.get_error_summary(days)
            report['error_details'] = self.get_error_details(start_date, end_date)
        
        if 'all' in include_sections or 'performance' in include_sections:
            report['performance_summary'] = LogAnalytics.get_performance_summary(days)
            report['performance_details'] = self.get_performance_details(start_date, end_date)
        
        if 'all' in include_sections or 'activities' in include_sections:
            report['activity_summary'] = LogAnalytics.get_user_activity_summary(days)
            report['activity_details'] = self.get_activity_details(start_date, end_date)
        
        if 'all' in include_sections or 'security' in include_sections:
            report['security_summary'] = LogAnalytics.get_security_summary(days)
            report['security_details'] = self.get_security_details(start_date, end_date)
        
        return report
    
    def get_error_details(self, start_date, end_date):
        """Get detailed error information"""
        errors = ErrorLog.objects.filter(
            timestamp__gte=start_date,
            timestamp__lte=end_date
        ).order_by('-timestamp')[:100]  # Limit to 100 most recent
        
        return [
            {
                'timestamp': error.timestamp.isoformat(),
                'view_name': error.view_name,
                'error_type': error.error_type,
                'severity': error.severity,
                'message': error.error_message[:200],  # Truncate long messages
                'user': error.user.username if error.user else None,
                'path': error.path,
                'resolved': error.resolved
            }
            for error in errors
        ]
    
    def get_performance_details(self, start_date, end_date):
        """Get detailed performance information"""
        # Get slowest requests
        slow_requests = PerformanceLog.objects.filter(
            created_at__gte=start_date,
            created_at__lte=end_date
        ).order_by('-duration')[:50]
        
        return [
            {
                'timestamp': perf.created_at.isoformat(),
                'path': perf.path,
                'method': perf.method,
                'duration': perf.duration,
                'status_code': perf.status_code,
                'db_queries': perf.db_queries_count,
                'user': perf.user.username if perf.user else None
            }
            for perf in slow_requests
        ]
    
    def get_activity_details(self, start_date, end_date):
        """Get detailed user activity information"""
        activities = UserActivity.objects.filter(
            timestamp__gte=start_date,
            timestamp__lte=end_date
        ).order_by('-timestamp')[:100]
        
        return [
            {
                'timestamp': activity.timestamp.isoformat(),
                'user': activity.user.username,
                'activity_type': activity.activity_type,
                'action': activity.action,
                'success': activity.success,
                'ip_address': activity.ip_address
            }
            for activity in activities
        ]
    
    def get_security_details(self, start_date, end_date):
        """Get detailed security incident information"""
        incidents = SecurityIncident.objects.filter(
            timestamp__gte=start_date,
            timestamp__lte=end_date
        ).order_by('-timestamp')
        
        return [
            {
                'timestamp': incident.timestamp.isoformat(),
                'incident_type': incident.incident_type,
                'severity': incident.severity,
                'description': incident.description,
                'ip_address': incident.ip_address,
                'blocked': incident.blocked,
                'resolved': incident.resolved
            }
            for incident in incidents
        ]
    
    def write_json_report(self, report_data, output_path):
        """Write report in JSON format"""
        with open(output_path, 'w') as f:
            json.dump(report_data, f, indent=2, default=str)
    
    def write_csv_report(self, report_data, output_path):
        """Write report in CSV format"""
        # Create a simplified CSV with key metrics
        with open(output_path, 'w', newline='') as f:
            writer = csv.writer(f)
            
            # Write metadata
            writer.writerow(['Log Report'])
            writer.writerow(['Generated:', report_data['metadata']['generated_at']])
            writer.writerow(['Period:', f"{report_data['metadata']['period_days']} days"])
            writer.writerow([])
            
            # Write summaries
            if 'error_summary' in report_data:
                writer.writerow(['Error Summary'])
                writer.writerow(['Total Errors:', report_data['error_summary']['total_errors']])
                writer.writerow(['Critical Errors:', report_data['error_summary']['critical_errors']])
                writer.writerow(['Unresolved:', report_data['error_summary']['unresolved']])
                writer.writerow([])
            
            if 'performance_summary' in report_data:
                writer.writerow(['Performance Summary'])
                writer.writerow(['Total Requests:', report_data['performance_summary']['total_requests']])
                writer.writerow(['Avg Response Time:', report_data['performance_summary']['avg_response_time']])
                writer.writerow(['Slow Requests:', report_data['performance_summary']['slow_requests']])
                writer.writerow([])
    
    def write_text_report(self, report_data, output_path):
        """Write report in text format"""
        with open(output_path, 'w') as f:
            f.write("LOG REPORT\n")
            f.write("=" * 50 + "\n\n")
            
            # Metadata
            metadata = report_data['metadata']
            f.write(f"Generated: {metadata['generated_at']}\n")
            f.write(f"Period: {metadata['period_days']} days\n")
            f.write(f"From: {metadata['period_start']}\n")
            f.write(f"To: {metadata['period_end']}\n\n")
            
            # Error Summary
            if 'error_summary' in report_data:
                f.write("ERROR SUMMARY\n")
                f.write("-" * 20 + "\n")
                summary = report_data['error_summary']
                f.write(f"Total Errors: {summary['total_errors']}\n")
                f.write(f"Critical Errors: {summary['critical_errors']}\n")
                f.write(f"Unresolved: {summary['unresolved']}\n\n")
            
            # Performance Summary
            if 'performance_summary' in report_data:
                f.write("PERFORMANCE SUMMARY\n")
                f.write("-" * 20 + "\n")
                summary = report_data['performance_summary']
                f.write(f"Total Requests: {summary['total_requests']}\n")
                f.write(f"Average Response Time: {summary['avg_response_time']:.3f}s\n")
                f.write(f"Slow Requests (>2s): {summary['slow_requests']}\n\n")
            
            # Add more sections as needed...
