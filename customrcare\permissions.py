from rest_framework.permissions import BasePermission
from rest_framework.exceptions import PermissionDenied
from rest_framework.response import Response
from rest_framework import status
from .models import CustomrcareProfile
from students.models import Student


class IsCustomrcareUserOnly(BasePermission):
    def has_permission(self, request, view):
        user = request.user

        if user.is_authenticated:
            try:
                customr_care = CustomrcareProfile.objects.get(user=user)
                if customr_care.role == "customrcare":
                    return True
                else:
                    raise PermissionDenied(
                        detail="You do not have permission for this resource."
                    )
            except CustomrcareProfile.DoesNotExist:
                raise PermissionDenied(detail="Customrcare profile not found.")
        raise PermissionDenied(detail="Authentication required.")

class IsCustomrcareUser(BasePermission):
    def has_permission(self, request, view):
        user = request.user

        if user.is_authenticated:
            try:
                if Student.objects.filter(user=user).exists():
                    return True
                else:
                    customr_care = CustomrcareProfile.objects.get(user=user)
                    if customr_care.role == "customrcare":
                        return True
                raise PermissionDenied(
                    detail="You do not have permission for this resource."
                )
            except CustomrcareProfile.DoesNotExist:
                raise PermissionDenied(detail="Customrcare profile not found.")
        raise PermissionDenied(detail="Authentication required.")
    
class IsCustomrcareUserNotPost(BasePermission):
    def has_permission(self, request, view):
        # Allow all POST requests
        if request.method == "POST":
            return True

        user = request.user

        if not user.is_authenticated:
            raise PermissionDenied(detail="Authentication required.")

        # Allow if user is a Student
        if Student.objects.filter(user=user).exists():
            return True

        # Allow if user is customrcare
        try:
            customrcare = CustomrcareProfile.objects.get(user=user)
            if customrcare.role == "customrcare":
                return True
        except CustomrcareProfile.DoesNotExist:
            pass

        raise PermissionDenied("You do not have permission for this resource.")