from rest_framework.response import Response
from rest_framework.views import APIView
from rest_framework import status
from django.utils.crypto import get_random_string
from django.core.cache import cache
from django.conf import settings
from django.core.mail import send_mail
import threading
from .serializers import PasswordResetRequestSerializer
from .serializers import OTPVerificationSerializer
from django.contrib.sessions.models import Session
from django.core.exceptions import ObjectDoesNotExist


class PasswordResetRequestAPIView(APIView):
    def post(self, request):
        serializer = PasswordResetRequestSerializer(data=request.data)
        if serializer.is_valid():
            email = serializer.validated_data["email"]
            otp = get_random_string(length=6, allowed_chars="0123456789")

            # Store OTP in cache for 10 minutes
            cache.set(email, otp, timeout=600)
           
            cache.set("latest_email", email, timeout=600)
            # Send OTP via email
            subject = "OTP for Password Reset"
            message = f"Your OTP is {otp}"
            from_email = settings.EMAIL_HOST_USER
            recipient_list = [email]

            # Send email asynchronously
            email_thread = threading.Thread(
                target=send_mail, args=(subject, message, from_email, recipient_list)
            )
            email_thread.start()

            return Response(
                {"message": "OTP sent to your email."}, status=status.HTTP_200_OK
            )
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)


class OTPVerificationAPIView(APIView):
    def post(self, request):
        serializer = OTPVerificationSerializer(data=request.data)
        if serializer.is_valid():
            serializer.save()
            return Response(
                {"message": "Password reset successful."}, status=status.HTTP_200_OK
            )
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)


class GetSessionIDView(APIView):
    def get(self, request, *args, **kwargs):
        """
        Retrieve the current session ID.
        """
        # Get the current session ID
        session_id = request.session.session_key

        if session_id:
            return Response(
                {
                    "status": "success",
                    "session_id": session_id,
                },
                status=status.HTTP_200_OK,
            )
        else:
            return Response(
                {
                    "status": "error",
                    "message": "No session found for the user.",
                },
                status=status.HTTP_404_NOT_FOUND,
            )


class GetSessionDataView(APIView):
    def get(self, request, session_id, *args, **kwargs):
        """
        Retrieve session data based on session ID.
        """
        try:
            # Session object ko fetch karein
            session = Session.objects.get(session_key=session_id)
            # Session data decode karein
            session_data = session.get_decoded()

            # JSON response bhejein
            return Response(
                {
                    "status": "success",
                    "session_id": session_id,
                    "session_data": session_data,
                },
                status=status.HTTP_200_OK,
            )

        except ObjectDoesNotExist:
            return Response(
                {
                    "status": "error",
                    "message": "Session not found!",
                },
                status=status.HTTP_404_NOT_FOUND,
            )

class ResendOTPAPIView(APIView):
    def post(self, request):
        # Cache se email retrieve karein
        email = cache.get("latest_email")

        if not email:
            return Response(
                {"error": "No recent OTP request found. Please request OTP first."},
                status=status.HTTP_400_BAD_REQUEST,
            )

        otp = get_random_string(length=6, allowed_chars="0123456789")

        # Store new OTP in cache for 10 minutes
        cache.set(email, otp, timeout=600)

        # Send OTP via email
        subject = "Resend OTP for Password Reset"
        message = f"Your new OTP is {otp}"
        from_email = settings.EMAIL_HOST_USER
        recipient_list = [email]

        # Send email asynchronously
        email_thread = threading.Thread(
            target=send_mail, args=(subject, message, from_email, recipient_list)
        )
        email_thread.start()

        return Response(
            {"message": "New OTP sent to your email."}, status=status.HTTP_200_OK
        )

