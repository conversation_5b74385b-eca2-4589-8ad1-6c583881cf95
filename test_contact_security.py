#!/usr/bin/env python3
"""
Contact Security Testing Script
This script tests the security measures implemented for the contact management system.
"""

import os
import sys
import django
import json

# Add the project directory to Python path
sys.path.insert(0, '/Users/<USER>/Documents/code/shash_b')

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'shashtrarth.settings')
django.setup()

from django.contrib.auth.models import User
from students.models import Student
from customrcare.models import CustomrcareProfile
from contributor.models import ContributorProfile
from contacts.models import UserContact, Contact, ContactRelationship
from rest_framework.test import APIClient
from rest_framework_simplejwt.tokens import RefreshToken
import uuid


def create_test_users():
    """Create test users for different roles"""
    
    # Create student user
    student_user = User.objects.create_user(
        username=f'student_{uuid.uuid4().hex[:8]}',
        email=f'student_{uuid.uuid4().hex[:8]}@example.com',
        password='testpass123'
    )
    student = Student.objects.create(
        user=student_user,
        phone=f'98765{uuid.uuid4().hex[:5]}'
    )
    
    # Create another student user
    student2_user = User.objects.create_user(
        username=f'student2_{uuid.uuid4().hex[:8]}',
        email=f'student2_{uuid.uuid4().hex[:8]}@example.com',
        password='testpass123'
    )
    student2 = Student.objects.create(
        user=student2_user,
        phone=f'98765{uuid.uuid4().hex[:5]}'
    )
    
    # Create customer care user
    cc_user = User.objects.create_user(
        username=f'cc_{uuid.uuid4().hex[:8]}',
        email=f'cc_{uuid.uuid4().hex[:8]}@example.com',
        password='testpass123'
    )
    cc_profile = CustomrcareProfile.objects.create(
        user=cc_user,
        contact=9876543210
    )
    
    # Create contributor user
    contrib_user = User.objects.create_user(
        username=f'contrib_{uuid.uuid4().hex[:8]}',
        email=f'contrib_{uuid.uuid4().hex[:8]}@example.com',
        password='testpass123'
    )
    contrib_profile = ContributorProfile.objects.create(
        user=contrib_user
    )
    
    # Create admin user
    admin_user = User.objects.create_superuser(
        username=f'admin_{uuid.uuid4().hex[:8]}',
        email=f'admin_{uuid.uuid4().hex[:8]}@example.com',
        password='testpass123'
    )
    
    return {
        'student1': {'user': student_user, 'profile': student},
        'student2': {'user': student2_user, 'profile': student2},
        'customer_care': {'user': cc_user, 'profile': cc_profile},
        'contributor': {'user': contrib_user, 'profile': contrib_profile},
        'admin': {'user': admin_user, 'profile': None}
    }


def get_jwt_token(user):
    """Get JWT token for a user"""
    refresh = RefreshToken.for_user(user)
    return str(refresh.access_token)


def test_student_access_isolation():
    """Test that students can only access their own contacts"""
    print("\n=== Testing Student Access Isolation ===")
    
    users = create_test_users()
    student1 = users['student1']
    student2 = users['student2']
    
    # Create contacts for student1
    contact1 = UserContact.objects.create(
        user=student1['user'],
        name='Student1 Contact',
        contact_number='9876543210'
    )
    
    # Create contacts for student2
    contact2 = UserContact.objects.create(
        user=student2['user'],
        name='Student2 Contact',
        contact_number='9876543211'
    )
    
    # Test student1 accessing their own contacts
    client = APIClient()
    token1 = get_jwt_token(student1['user'])
    client.credentials(HTTP_AUTHORIZATION=f'Bearer {token1}')
    
    response = client.get('/api/contacts/my-contacts/')
    print(f"✓ Student1 accessing own contacts: {response.status_code}")
    if response.status_code == 200:
        data = response.json()
        if 'results' in data:
            contact_count = len(data['results'])
            print(f"  - Found {contact_count} contacts (should be 1)")
            if contact_count == 1 and data['results'][0]['name'] == 'Student1 Contact':
                print("  ✓ Correct contact returned")
            else:
                print("  ✗ Incorrect contacts returned")
    
    # Test student2 accessing their own contacts
    token2 = get_jwt_token(student2['user'])
    client.credentials(HTTP_AUTHORIZATION=f'Bearer {token2}')
    
    response = client.get('/api/contacts/my-contacts/')
    print(f"✓ Student2 accessing own contacts: {response.status_code}")
    if response.status_code == 200:
        data = response.json()
        if 'results' in data:
            contact_count = len(data['results'])
            print(f"  - Found {contact_count} contacts (should be 1)")
            if contact_count == 1 and data['results'][0]['name'] == 'Student2 Contact':
                print("  ✓ Correct contact returned")
            else:
                print("  ✗ Incorrect contacts returned")


def test_role_based_access():
    """Test that different roles have appropriate access"""
    print("\n=== Testing Role-Based Access Control ===")
    
    users = create_test_users()
    
    # Test customer care access
    print("\n--- Customer Care Access ---")
    client = APIClient()
    cc_token = get_jwt_token(users['customer_care']['user'])
    client.credentials(HTTP_AUTHORIZATION=f'Bearer {cc_token}')
    
    response = client.get('/api/contacts/my-contacts/')
    print(f"Customer care accessing contacts: {response.status_code}")
    if response.status_code == 403:
        print("✓ Customer care correctly denied access")
    else:
        print("✗ Customer care should be denied access")
    
    # Test contributor access
    print("\n--- Contributor Access ---")
    contrib_token = get_jwt_token(users['contributor']['user'])
    client.credentials(HTTP_AUTHORIZATION=f'Bearer {contrib_token}')
    
    response = client.get('/api/contacts/my-contacts/')
    print(f"Contributor accessing contacts: {response.status_code}")
    if response.status_code == 403:
        print("✓ Contributor correctly denied access")
    else:
        print("✗ Contributor should be denied access")
    
    # Test admin access
    print("\n--- Admin Access ---")
    admin_token = get_jwt_token(users['admin']['user'])
    client.credentials(HTTP_AUTHORIZATION=f'Bearer {admin_token}')
    
    response = client.get('/api/contacts/my-contacts/')
    print(f"Admin accessing contacts: {response.status_code}")
    if response.status_code == 200:
        print("✓ Admin correctly granted access")
    else:
        print("✗ Admin should have access")
    
    # Test admin-only endpoints
    response = client.get('/api/contacts/admin/contacts/')
    print(f"Admin accessing admin endpoint: {response.status_code}")
    if response.status_code == 200:
        print("✓ Admin correctly granted access to admin endpoint")
    else:
        print("✗ Admin should have access to admin endpoint")


def test_admin_only_endpoints():
    """Test that admin-only endpoints are properly protected"""
    print("\n=== Testing Admin-Only Endpoints ===")
    
    users = create_test_users()
    
    # Test student access to admin endpoints
    print("\n--- Student Access to Admin Endpoints ---")
    client = APIClient()
    student_token = get_jwt_token(users['student1']['user'])
    client.credentials(HTTP_AUTHORIZATION=f'Bearer {student_token}')
    
    response = client.get('/api/contacts/admin/contacts/')
    print(f"Student accessing admin contacts: {response.status_code}")
    if response.status_code == 403:
        print("✓ Student correctly denied access to admin endpoint")
    else:
        print("✗ Student should be denied access to admin endpoint")
    
    response = client.get('/api/contacts/admin/stats/')
    print(f"Student accessing admin stats: {response.status_code}")
    if response.status_code == 403:
        print("✓ Student correctly denied access to admin stats")
    else:
        print("✗ Student should be denied access to admin stats")


def test_contact_upload_restrictions():
    """Test that only students can upload contacts"""
    print("\n=== Testing Contact Upload Restrictions ===")
    
    users = create_test_users()
    
    contact_data = {
        'contacts': [
            {'name': 'Test Contact', 'contact': '9876543210'}
        ]
    }
    
    # Test customer care upload
    print("\n--- Customer Care Upload ---")
    client = APIClient()
    cc_token = get_jwt_token(users['customer_care']['user'])
    client.credentials(HTTP_AUTHORIZATION=f'Bearer {cc_token}')
    
    response = client.post('/api/contacts/upload/', contact_data, format='json')
    print(f"Customer care uploading contacts: {response.status_code}")
    if response.status_code == 403:
        print("✓ Customer care correctly denied upload access")
    else:
        print("✗ Customer care should be denied upload access")
    
    # Test contributor upload
    print("\n--- Contributor Upload ---")
    contrib_token = get_jwt_token(users['contributor']['user'])
    client.credentials(HTTP_AUTHORIZATION=f'Bearer {contrib_token}')
    
    response = client.post('/api/contacts/upload/', contact_data, format='json')
    print(f"Contributor uploading contacts: {response.status_code}")
    if response.status_code == 403:
        print("✓ Contributor correctly denied upload access")
    else:
        print("✗ Contributor should be denied upload access")
    
    # Test student upload
    print("\n--- Student Upload ---")
    student_token = get_jwt_token(users['student1']['user'])
    client.credentials(HTTP_AUTHORIZATION=f'Bearer {student_token}')
    
    response = client.post('/api/contacts/upload/', contact_data, format='json')
    print(f"Student uploading contacts: {response.status_code}")
    if response.status_code == 201:
        print("✓ Student correctly granted upload access")
    else:
        print("✗ Student should have upload access")


def main():
    """Main test function"""
    print("=" * 60)
    print("CONTACT SECURITY TESTING")
    print("=" * 60)
    
    try:
        test_student_access_isolation()
        test_role_based_access()
        test_admin_only_endpoints()
        test_contact_upload_restrictions()
        
        print("\n" + "=" * 60)
        print("✓ ALL SECURITY TESTS COMPLETED")
        print("=" * 60)
        
    except Exception as e:
        print(f"\n✗ Test failed with error: {str(e)}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
