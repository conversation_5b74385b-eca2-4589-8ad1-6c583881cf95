from django.urls import path, include
from rest_framework.routers import DefaultRouter
from .views import (
    PackageListCreateView,
    SubscriptionListCreateView,
    SubscriptionDetailView,
    PackageEditDeleteView,
    VerifySubscriptionPaymentView, CouponViewSet, ValidateCouponAPI, GiftCardViewSet, redeem_gift_card, GetRazorpayConfigView,
    get_user_invoices, invoice_html_view
)
from .views_new import (
    NewSubscriptionCreateView,
    NewPaymentVerificationView,
    SubscriptionStatusView,
    PackageAccessView
)
router = DefaultRouter()
router.register(r'giftcards', GiftCardViewSet)
router.register(r'coupons', CouponViewSet, basename='coupon')

urlpatterns = [
    path("", PackageListCreateView.as_view(), name="package-list"),
    path(
        "subscriptions/", SubscriptionListCreateView.as_view(), name="subscription-list"
    ),
    path(
        "subscriptions/search/",
        SubscriptionDetailView.as_view(),
        name="subscription-search",
    ),
    path("package_detail/<pk>", PackageEditDeleteView.as_view(), name="package_detail"),
    path("package-detail/<pk>", PackageEditDeleteView.as_view(), name="package-detail"),
    path("verify_payment/", VerifySubscriptionPaymentView.as_view(), name="verify_payment"),
    path('validate-coupon/', ValidateCouponAPI.as_view(), name='validate-coupon'),
    path('', include(router.urls)),
    path("giftcard/redeem/", redeem_gift_card),
    path("razorpay-config/", GetRazorpayConfigView.as_view(), name="razorpay-config"),
    path("user-invoices/", get_user_invoices, name="user-invoices" ),
    path("invoice/<str:invoice_number>/", invoice_html_view, name="invoice-detail-html"),

    # New clean API endpoints
    path("v2/create-subscription/", NewSubscriptionCreateView.as_view(), name="create-subscription-v2"),
    path("v2/verify-payment/", NewPaymentVerificationView.as_view(), name="verify-payment-v2"),
    path("v2/subscription-status/<int:student_id>/", SubscriptionStatusView.as_view(), name="subscription-status-v2"),
    path("v2/check-access/", PackageAccessView.as_view(), name="check-access-v2"),
]
