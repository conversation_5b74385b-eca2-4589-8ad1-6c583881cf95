"""
Comprehensive test suite for all models in the application.
"""
from django.test import TestCase
from django.contrib.auth.models import User
from django.core.exceptions import ValidationError
from django.db import IntegrityError
from django.utils import timezone
from datetime import timedelta

# Import all models
from contributor.models import Banner, ContributorProfile, PageCounter
from customrcare.models import CustomrcareProfile, Ticket, FrontendError, TemporaryImage
from questions.models import (
    Course, SubCourse, Tier, Paper, Section, Module, Subject, Topic, SubTopic,
    MasterQuestion, MasterOption, Question, Option, PreviousYearQuestion
)
from paper_engine.models import TestPattern, TestPaper, SubmitTest, PracticeRecord, ScoreTracker
from packages_and_subscriptions.models import Package, Subscription, Coupon, GiftCard
from students.models import Student
from blogs.models import BlogPost


class BannerModelTest(TestCase):
    """Test Banner model functionality."""
    
    def setUp(self):
        self.banner_data = {
            'banner_name': 'Test Banner',
            'banner_image': 'test_image.jpg'
        }
    
    def test_banner_str_method(self):
        """Test Banner model __str__ method uses correct field."""
        banner = Banner.objects.create(**self.banner_data)
        self.assertEqual(str(banner), 'Test Banner')
        self.assertEqual(str(banner), banner.banner_name)
    
    def test_banner_slug_generation(self):
        """Test Banner slug is generated automatically."""
        banner = Banner.objects.create(**self.banner_data)
        self.assertIsNotNone(banner.slug)
        self.assertTrue(banner.slug)


class CustomrcareProfileModelTest(TestCase):
    """Test CustomrcareProfile model functionality."""
    
    def setUp(self):
        self.user = User.objects.create_user(
            username='testuser',
            email='<EMAIL>',
            password='testpass123'
        )
    
    def test_customrcare_profile_creation_with_valid_fields(self):
        """Test CustomrcareProfile creation with valid fields."""
        profile = CustomrcareProfile.objects.create(
            user=self.user,
            contact=1234567890,
            role='customrcare'
        )
        self.assertEqual(profile.user, self.user)
        self.assertEqual(profile.contact, 1234567890)
        self.assertEqual(profile.role, 'customrcare')
    
    def test_customrcare_profile_str_method(self):
        """Test CustomrcareProfile __str__ method."""
        profile = CustomrcareProfile.objects.create(
            user=self.user,
            contact=1234567890
        )
        expected_str = f"{self.user.username} - customrcare - active"
        self.assertEqual(str(profile), expected_str)


class CourseModelTest(TestCase):
    """Test Course model functionality."""
    
    def test_course_creation(self):
        """Test Course model creation."""
        course = Course.objects.create(
            name='Test Course',
            description='Test Description'
        )
        self.assertEqual(course.name, 'Test Course')
        self.assertEqual(str(course), 'Test Course')
    
    def test_course_slug_generation(self):
        """Test Course slug generation."""
        course = Course.objects.create(name='Test Course')
        self.assertIsNotNone(course.slug)


class SubCourseModelTest(TestCase):
    """Test SubCourse model functionality."""
    
    def setUp(self):
        self.course = Course.objects.create(name='Test Course')
    
    def test_subcourse_creation_with_course(self):
        """Test SubCourse creation with course field."""
        subcourse = SubCourse.objects.create(
            course=self.course,
            name='Test SubCourse'
        )
        self.assertEqual(subcourse.course, self.course)
        self.assertEqual(subcourse.name, 'Test SubCourse')
    
    def test_subcourse_str_method(self):
        """Test SubCourse __str__ method."""
        subcourse = SubCourse.objects.create(
            course=self.course,
            name='Test SubCourse'
        )
        expected_str = f"Test SubCourse ({self.course.name})"
        self.assertEqual(str(subcourse), expected_str)


class TestPatternModelTest(TestCase):
    """Test TestPattern model functionality."""
    
    def setUp(self):
        self.user = User.objects.create_user(
            username='contributor',
            password='testpass123'
        )
        self.contributor = ContributorProfile.objects.create(
            user=self.user,
            role='contributor'
        )
    
    def test_test_pattern_creation(self):
        """Test TestPattern model creation."""
        pattern = TestPattern.objects.create(
            name='Test Pattern',
            contributor=self.contributor,
            sections=[
                {
                    'name': 'Section 1',
                    'number_of_questions': 10,
                    'subject_ids': [1, 2]
                }
            ]
        )
        self.assertEqual(pattern.name, 'Test Pattern')
        self.assertEqual(pattern.contributor, self.contributor)
    
    def test_test_pattern_total_questions(self):
        """Test TestPattern total_questions method."""
        pattern = TestPattern.objects.create(
            name='Test Pattern',
            contributor=self.contributor,
            sections=[
                {'number_of_questions': 10},
                {'number_of_questions': 15}
            ]
        )
        self.assertEqual(pattern.total_questions(), 25)


class PracticeRecordModelTest(TestCase):
    """Test PracticeRecord model functionality."""
    
    def setUp(self):
        self.user = User.objects.create_user(
            username='testuser',
            password='testpass123'
        )
    
    def test_practice_record_creation(self):
        """Test PracticeRecord creation."""
        record = PracticeRecord.objects.create(
            user=self.user,
            questions_practiced=10
        )
        self.assertEqual(record.user, self.user)
        self.assertEqual(record.questions_practiced, 10)
    
    def test_daily_practice_calculation(self):
        """Test daily practice calculation."""
        PracticeRecord.objects.create(
            user=self.user,
            questions_practiced=5
        )
        PracticeRecord.objects.create(
            user=self.user,
            questions_practiced=3
        )
        daily_practice = PracticeRecord.get_daily_practice(self.user)
        self.assertEqual(daily_practice, 8)


class ScoreTrackerModelTest(TestCase):
    """Test ScoreTracker model functionality."""
    
    def setUp(self):
        self.user = User.objects.create_user(
            username='testuser',
            password='testpass123'
        )
    
    def test_score_tracker_creation(self):
        """Test ScoreTracker creation."""
        tracker = ScoreTracker.objects.create(
            user=self.user,
            points=100
        )
        self.assertEqual(tracker.user, self.user)
        self.assertEqual(tracker.points, 100)


class PackageModelTest(TestCase):
    """Test Package model functionality."""
    
    def test_package_creation(self):
        """Test Package creation."""
        package = Package.objects.create(
            name='Test Package',
            price=100.00,
            discount_price=80.00,
            duration_months=12
        )
        self.assertEqual(package.name, 'Test Package')
        self.assertEqual(str(package), 'Test Package')


class StudentModelTest(TestCase):
    """Test Student model functionality."""
    
    def setUp(self):
        self.user = User.objects.create_user(
            username='student',
            email='<EMAIL>',
            password='testpass123'
        )
    
    def test_student_creation(self):
        """Test Student creation."""
        student = Student.objects.create(
            user=self.user,
            phone='1234567890'
        )
        self.assertEqual(student.user, self.user)
        self.assertEqual(student.phone, '1234567890')
