from django_cron import CronJobBase, Schedule
from django.utils import timezone
from datetime import timedelta
from .models import FrontendError

class DeleteOldFrontendErrorsCronJob(CronJobBase):
    RUN_EVERY_MINS = 60  # Run every hour

    schedule = Schedule(run_every_mins=RUN_EVERY_MINS)
    code = 'customrcare.delete_old_frontend_errors'  # Unique code for the cron job

    def do(self):
        # Define the period after which data should be deleted (e.g., 48 hours)
        period = timezone.now() - timedelta(hours=48)
        FrontendError.objects.filter(created_at__lt=period).delete()