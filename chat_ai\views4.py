from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework import status
from django.apps import apps
from django.urls import reverse, NoReverseMatch
from django.conf import settings
import google.generativeai as genai
import ast
import re

# Gemini setup
genai.configure(api_key='AIzaSyD2y8WM03f5fBDIo2uPfO-r8FiKkYuO7hQ')
gemini_model = genai.GenerativeModel('gemini-2.0-flash')

# Set your base domain here
BASE_DOMAIN = "http://127.0.0.1:8000/api/"

# Map models to URL paths (customize this to fit your slugs)
MODEL_URL_MAPPING = {
    'Course': lambda obj: f"/courses/{obj.get('slug', '')}/",
    'BlogPost': lambda obj: f"/blogs/{obj.get('slug', '')}/",
    'SubCourse': lambda obj: f"/subcourses/{obj.get('slug', '')}/",
    # Add more as needed...
}

def get_all_model_metadata():
    metadata = {}
    for model in apps.get_models():
        model_name = model.__name__
        field_names = [field.name for field in model._meta.fields]
        keywords = [model_name.lower(), model_name.lower() + 's'] + [f.lower() for f in field_names]
        metadata[model_name] = {
            'model': model,
            'fields': field_names,
            'keywords': keywords
        }
    return metadata

def find_best_matching_model(query, metadata):
    query_lower = query.lower()
    scores = []

    for name, meta in metadata.items():
        score = 0
        if name.lower() in query_lower or (name.lower() + 's') in query_lower:
            score += 5
        score += sum(1 for kw in meta['keywords'] if kw in query_lower)
        if score > 0:
            scores.append((score, meta))

    if scores:
        scores.sort(key=lambda x: x[0], reverse=True)
        return scores[0][1]
    return None

def get_detail_url(model_name, instance):
    try:
        pk_field = instance._meta.pk.name
        return BASE_DOMAIN + reverse(f'{model_name.lower()}-detail', kwargs={pk_field: getattr(instance, pk_field)})
    except NoReverseMatch:
        return None
    
def sanitize_explanation(text):
    text = re.sub(r'\b\d{10}\b', '[hidden]', text)  # hide 10-digit numbers
    return text
    
class AIChatbotAPIView(APIView):
    def post(self, request):
        query = request.data.get('query', '')
        if not query:
            return Response({'error': 'Query not provided'}, status=status.HTTP_400_BAD_REQUEST)

        model_metadata = get_all_model_metadata()
        all_model_names = list(model_metadata.keys())

        # Let Gemini decide the model
        model_detection_prompt = f"""
        The user asked: "{query}"

        Here are the models available: {all_model_names}

        Which model (just the name) best fits this query?
        Only return the model name, nothing else.
        """
        model_name_response = gemini_model.generate_content(model_detection_prompt).text.strip()

        matched_model = model_metadata.get(model_name_response)
        if not matched_model:
            return Response({'error': f'Could not match query to model "{model_name_response}"'}, status=status.HTTP_404_NOT_FOUND)

        model_class = matched_model['model']
        fields = matched_model['fields']

        # Check if it's a count-based query
        if any(word in query.lower() for word in ['kitne', 'how many', 'total']):
            count = model_class.objects.count()
            return Response({
                'model': model_class.__name__,
                'count': count,
                'explanation': f"Total **{count} {model_class.__name__}s** found in the system! ✅"
            })

        # Else: Try filtering and showing results
        filter_prompt = f"""
        You are a Django ORM expert.

        User's query: "{query}"
        Model: {model_class.__name__}
        Available fields: {fields}

        Based on the user's query, generate a valid Python dictionary for Django ORM filtering.
        Example: {{'difficulty': 2, 'status': 'active'}}

        Only return the dictionary. Do NOT include any explanation or extra text.
        """
        filter_response = gemini_model.generate_content(filter_prompt)

        try:
            filters = ast.literal_eval(filter_response.text.strip())
        except Exception as e:
            filters = {}

        try:
            data_qs = model_class.objects.filter(**filters)[:5]
        except:
            data_qs = model_class.objects.all()[:5]

        EXCLUDED_FIELDS = {
            'phone', 'user', 'student_id', 'address', 'state', 'gender',
            'created_date', 'updated_date', 'is_active', 'is_deleted',
            'is_verified', 'is_subscribed', 'is_approved', 'is_published',
            'image_url', 'qr_code', 'id', 'created_at', 'updated_at'
        }

        safe_fields = [field for field in fields if field not in EXCLUDED_FIELDS]
        data_list = list(data_qs.values(*safe_fields))

        # Generate links if available
        url_generator = MODEL_URL_MAPPING.get(model_class.__name__)
        if url_generator:
            for obj in data_list:
                obj['url'] = BASE_DOMAIN + url_generator(obj)

        # Friendly explanation prompt
        explanation_prompt = f"""
        Talk to the user like a helpful friend. They asked:

        '{query}'

        Here's the data we found in the database:
        {data_list}

        Please explain this in a friendly tone. Mention important fields and also provide the link (if available in 'url' field) so the user can check it out directly.
        """
        gemini_response = gemini_model.generate_content(explanation_prompt)
        explanation = sanitize_explanation(gemini_response.text)

        return Response({
            'model': model_class.__name__,
            'filters_applied': filters,
            'data': data_list,
            'explanation': explanation,
        })