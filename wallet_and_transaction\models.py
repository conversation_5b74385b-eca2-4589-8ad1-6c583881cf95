from django.db import models
from django.contrib.auth.models import User
from django.utils import timezone


# Create your models here.

class Wallet(models.Model):
    user = models.OneToOneField(User, on_delete=models.CASCADE)
    balance = models.PositiveIntegerField(default=10)  # Points balance
    upi_id = models.CharField(max_length=100, null=True, blank=True)  
    razorpay_order_id = models.CharField(max_length=100,  null=True, blank=True, default=None)
    razorpay_payment_id = models.CharField(max_length=100,  null=True, blank=True, default=None)
    razorpay_signature = models.CharField(max_length=100,  null=True, blank=True, default=None)

    def __str__(self):
        return f"{self.user.username}'s Wallet"

   


class Transaction(models.Model):
    wallet = models.ForeignKey(Wallet, on_delete=models.CASCADE)
    amount = models.PositiveIntegerField()
    is_credit = models.BooleanField(default=False)
    is_debit = models.<PERSON>oleanField(default=False)
    note = models.CharField(max_length=100)
    created_at = models.DateTimeField(default=timezone.now)
    status= models.CharField(max_length=50, default="Pending")
    transaction_id = models.CharField(max_length=100, default="Null")
    
    class Meta:
        indexes = [
            models.Index(fields=["wallet", "status"]),
        ]

    def __str__(self):
        # type_str = "Credit" if self.is_credit else "Debit"
        return f"{self.amount} points for {self.wallet.user.username}"

class WithdrawalRequest(models.Model):
    TRANSACTION_STATUS = [
    ('Pending', 'Pending'),
    ('Success', 'Success'),
    ('Failed', 'Failed'),
]
    wallet = models.ForeignKey(Wallet, on_delete=models.CASCADE)
    amount = models.PositiveIntegerField()
    upi_id = models.CharField(max_length=100)
    status = models.CharField(max_length=20, choices=TRANSACTION_STATUS, default='Pending')
    created_at = models.DateTimeField(default=timezone.now)
    approved_at = models.DateTimeField(null=True, blank=True)
    transaction = models.OneToOneField(Transaction, null=True, blank=True, on_delete=models.SET_NULL)

    def __str__(self):
        return f"{self.wallet.user.username} - {self.amount} - {self.status}"