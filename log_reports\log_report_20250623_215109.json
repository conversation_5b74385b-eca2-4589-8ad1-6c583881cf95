{"metadata": {"generated_at": "2025-06-23T16:21:09.956882+00:00", "period_start": "2025-06-22T16:21:09.956882+00:00", "period_end": "2025-06-23T16:21:09.956882+00:00", "period_days": 1, "sections_included": ["all"]}, "error_summary": {"total_errors": 2, "by_type": [{"error_type": "BUSINESS_LOGIC", "count": 1}, {"error_type": "VALIDATION", "count": 1}], "by_severity": [{"severity": "LOW", "count": 1}, {"severity": "MEDIUM", "count": 1}], "unresolved": 2, "critical_errors": 0}, "error_details": [{"timestamp": "2025-06-23T16:19:34.405300+00:00", "view_name": "test_view", "error_type": "BUSINESS_LOGIC", "severity": "MEDIUM", "message": "Test error message", "user": "testuser2", "path": "", "resolved": false}, {"timestamp": "2025-06-23T16:19:34.243859+00:00", "view_name": "test_view", "error_type": "VALIDATION", "severity": "LOW", "message": "Test error", "user": "testuser", "path": "", "resolved": false}], "performance_summary": {"total_requests": 1, "avg_response_time": 0.5, "max_response_time": 0.5, "min_response_time": 0.5, "slow_requests": 0, "avg_db_queries": 0.0, "by_status_code": [{"status_code": 200, "count": 1}]}, "performance_details": [{"timestamp": "2025-06-23T16:19:34.242276+00:00", "path": "/test/path/", "method": "GET", "duration": 0.5, "status_code": 200, "db_queries": 0, "user": "testuser"}], "activity_summary": {"total_activities": 6, "unique_users": 5, "by_type": [{"activity_type": "API_CALL", "count": 1}, {"activity_type": "LOGIN", "count": 1}, {"activity_type": "OTHER", "count": 4}], "successful_activities": 6, "failed_activities": 0}, "activity_details": [{"timestamp": "2025-06-23T16:19:34.400831+00:00", "user": "testuser2", "activity_type": "API_CALL", "action": "Test API call", "success": true, "ip_address": null}, {"timestamp": "2025-06-23T16:19:34.245827+00:00", "user": "testuser", "activity_type": "LOGIN", "action": "User logged in", "success": true, "ip_address": "127.0.0.1"}, {"timestamp": "2025-06-23T14:49:28.273577+00:00", "user": "<PERSON><PERSON><PERSON>", "activity_type": "OTHER", "action": "Contributor <PERSON><PERSON>", "success": true, "ip_address": null}, {"timestamp": "2025-06-23T10:28:25.283082+00:00", "user": "nayan", "activity_type": "OTHER", "action": "Student Login", "success": true, "ip_address": null}, {"timestamp": "2025-06-22T20:46:03.453611+00:00", "user": "nayan", "activity_type": "OTHER", "action": "Student Login", "success": true, "ip_address": null}, {"timestamp": "2025-06-22T20:42:56.267666+00:00", "user": "testlogin", "activity_type": "OTHER", "action": "Student Login", "success": true, "ip_address": null}], "security_summary": {"total_incidents": 1, "by_type": [{"incident_type": "BRUTE_FORCE", "count": 1}], "by_severity": [{"severity": "MEDIUM", "count": 1}], "unresolved": 1, "critical_incidents": 0, "blocked_incidents": 0}, "security_details": [{"timestamp": "2025-06-23T16:19:34.251806+00:00", "incident_type": "BRUTE_FORCE", "severity": "MEDIUM", "description": "Test security incident", "ip_address": "127.0.0.1", "blocked": false, "resolved": false}]}