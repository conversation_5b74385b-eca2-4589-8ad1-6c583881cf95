from django.urls import path
from .views import (TestPatternAPIView, GeneratePaper,SubmitTestView, get_distinct_sections_with_rank, increment_section_rank,StudentExamRecommendation
                    ,StartTestView, ResumeTestView)


urlpatterns = [
    path("", TestPatternAPIView.as_view(), name="test-pattern-list-create"),
    path("list/", TestPatternAPIView.as_view(), name="test-pattern-list"),  # Alias for compatibility
    path(
        "<int:pk>/",
        TestPatternAPIView.as_view(),
        name="test-pattern-detail",
    ),
    path("generate-paper/", GeneratePaper.as_view(), name="generate-paper"),
    path("generate-paper/<int:student_id>/", GeneratePaper.as_view(), name="generate-paper"),
    path("submit-test/<int:paper_id>/", SubmitTestView.as_view(), name="submit-test"),
    path('sections-with-rank/', get_distinct_sections_with_rank, name='get_sections_with_rank'),
    path('increment-section-rank/', increment_section_rank, name='increment_section_rank'),
    path('student-exam-recommendation/<int:student_id>/', StudentExamRecommendation.as_view(), name='student_exam_recommendation'),    
    path("start-test/<int:paper_id>/", StartTestView.as_view()),
    path("resume-test/<int:paper_id>/", ResumeTestView.as_view())

]
