"""
Logging utilities and decorators for comprehensive application logging
"""

import time
import functools
import traceback
import json
import logging
from django.core.cache import cache
from django.contrib.auth.models import User
from django.utils import timezone
from django.db import connection
from .models import (
    PerformanceLog, ErrorLog, UserActivity, APIAccessLog, 
    DatabaseQueryLog, AuthenticationLog, SecurityIncident, LogConfig
)

logger = logging.getLogger(__name__)


class LoggingUtils:
    """Utility class for logging operations"""
    
    @staticmethod
    def get_log_config():
        """Get logging configuration with caching"""
        config = cache.get('log_config')
        if not config:
            try:
                config = LogConfig.objects.first()
                if not config:
                    config = LogConfig.objects.create()
                cache.set('log_config', config, timeout=300)  # Cache for 5 minutes
            except Exception:
                # Fallback configuration
                config = type('LogConfig', (), {
                    'enable_performance_logging': True,
                    'enable_error_logging': True,
                    'enable_activity_logging': True,
                    'enable_api_logging': True,
                    'enable_db_logging': False,
                    'enable_auth_logging': True,
                })()
        return config
    
    @staticmethod
    def get_client_ip(request):
        """Get client IP address from request"""
        x_forwarded_for = request.META.get('HTTP_X_FORWARDED_FOR')
        if x_forwarded_for:
            ip = x_forwarded_for.split(',')[0].strip()
        else:
            ip = request.META.get('REMOTE_ADDR')
        return ip
    
    @staticmethod
    def log_user_activity(user, activity_type, action, description="", metadata=None, request=None):
        """Log user activity manually"""
        try:
            config = LoggingUtils.get_log_config()
            if not config.enable_activity_logging:
                return
            
            activity_data = {
                'user': user,
                'activity_type': activity_type,
                'action': action,
                'description': description,
                'success': True,
                'metadata': metadata or {},
            }
            
            if request:
                activity_data.update({
                    'ip_address': LoggingUtils.get_client_ip(request),
                    'user_agent': request.META.get('HTTP_USER_AGENT', ''),
                    'session_key': getattr(request.session, 'session_key', ''),
                    'path': request.path,
                    'method': request.method,
                })
            
            UserActivity.objects.create(**activity_data)
        except Exception as e:
            logger.error(f"Error logging user activity: {e}")
    
    @staticmethod
    def log_authentication_event(event_type, user=None, username_attempted="", 
                                success=True, failure_reason="", request=None, additional_data=None):
        """Log authentication events"""
        try:
            config = LoggingUtils.get_log_config()
            if not config.enable_auth_logging:
                return
            
            auth_data = {
                'user': user,
                'username_attempted': username_attempted,
                'event_type': event_type,
                'success': success,
                'failure_reason': failure_reason,
                'additional_data': additional_data or {},
            }
            
            if request:
                auth_data.update({
                    'ip_address': LoggingUtils.get_client_ip(request),
                    'user_agent': request.META.get('HTTP_USER_AGENT', ''),
                    'session_key': getattr(request.session, 'session_key', ''),
                })
            
            AuthenticationLog.objects.create(**auth_data)
        except Exception as e:
            logger.error(f"Error logging authentication event: {e}")
    
    @staticmethod
    def log_error_manual(error_type, error_message, view_name="", severity="MEDIUM", 
                        user=None, request=None, additional_data=None):
        """Manually log errors"""
        try:
            config = LoggingUtils.get_log_config()
            if not config.enable_error_logging:
                return
            
            error_data = {
                'view_name': view_name,
                'error_type': error_type,
                'severity': severity,
                'error_message': error_message,
                'stack_trace': traceback.format_exc(),
                'user': user,
                'request_data': additional_data or {},
            }
            
            if request:
                error_data.update({
                    'ip_address': LoggingUtils.get_client_ip(request),
                    'user_agent': request.META.get('HTTP_USER_AGENT', ''),
                    'path': request.path,
                    'method': request.method,
                })
            
            ErrorLog.objects.create(**error_data)
        except Exception as e:
            logger.error(f"Error logging error manually: {e}")


def log_performance(include_db_queries=False, include_memory=False):
    """
    Decorator to log function/method performance
    
    Usage:
    @log_performance(include_db_queries=True, include_memory=True)
    def my_function():
        pass
    """
    def decorator(func):
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            config = LoggingUtils.get_log_config()
            if not config.enable_performance_logging:
                return func(*args, **kwargs)
            
            start_time = time.time()
            start_queries = len(connection.queries) if include_db_queries else 0
            
            try:
                result = func(*args, **kwargs)
                duration = time.time() - start_time
                
                # Log performance
                log_data = {
                    'path': f"{func.__module__}.{func.__name__}",
                    'method': 'FUNCTION',
                    'duration': duration,
                }
                
                if include_db_queries:
                    db_queries_count = len(connection.queries) - start_queries
                    db_queries_time = sum(float(q['time']) for q in connection.queries[start_queries:])
                    log_data.update({
                        'db_queries_count': db_queries_count,
                        'db_queries_time': db_queries_time,
                    })
                
                # Try to get user from args (for view functions)
                user = None
                request = None
                for arg in args:
                    if hasattr(arg, 'user'):
                        user = arg.user if arg.user.is_authenticated else None
                        request = arg
                        break
                
                log_data['user'] = user
                
                PerformanceLog.objects.create(**log_data)
                return result
                
            except Exception as e:
                duration = time.time() - start_time
                
                # Log the error
                LoggingUtils.log_error_manual(
                    error_type='BUSINESS_LOGIC',
                    error_message=str(e),
                    view_name=f"{func.__module__}.{func.__name__}",
                    severity='HIGH'
                )
                
                raise
        
        return wrapper
    return decorator


def log_user_action(activity_type, action_description=""):
    """
    Decorator to log user actions
    
    Usage:
    @log_user_action('API_CALL', 'User retrieved profile data')
    def get_profile(request):
        pass
    """
    def decorator(func):
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            config = LoggingUtils.get_log_config()
            if not config.enable_activity_logging:
                return func(*args, **kwargs)
            
            # Try to get request and user from args
            request = None
            user = None
            for arg in args:
                if hasattr(arg, 'user') and hasattr(arg, 'method'):
                    request = arg
                    user = arg.user if arg.user.is_authenticated else None
                    break
            
            try:
                result = func(*args, **kwargs)
                
                # Log successful action
                if user:
                    action = action_description or f"Executed {func.__name__}"
                    LoggingUtils.log_user_activity(
                        user=user,
                        activity_type=activity_type,
                        action=action,
                        description=f"Function: {func.__module__}.{func.__name__}",
                        request=request,
                        metadata={'function': func.__name__, 'success': True}
                    )
                
                return result
                
            except Exception as e:
                # Log failed action
                if user:
                    action = action_description or f"Failed to execute {func.__name__}"
                    LoggingUtils.log_user_activity(
                        user=user,
                        activity_type=activity_type,
                        action=action,
                        description=f"Function: {func.__module__}.{func.__name__} - Error: {str(e)}",
                        request=request,
                        metadata={'function': func.__name__, 'success': False, 'error': str(e)}
                    )
                
                raise
        
        return wrapper
    return decorator


def log_authentication(event_type):
    """
    Decorator to log authentication events
    
    Usage:
    @log_authentication('LOGIN_SUCCESS')
    def login_view(request):
        pass
    """
    def decorator(func):
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            config = LoggingUtils.get_log_config()
            if not config.enable_auth_logging:
                return func(*args, **kwargs)
            
            # Try to get request from args
            request = None
            for arg in args:
                if hasattr(arg, 'method') and hasattr(arg, 'META'):
                    request = arg
                    break
            
            try:
                result = func(*args, **kwargs)
                
                # Determine success based on result or response status
                success = True
                user = None
                
                if hasattr(result, 'status_code'):
                    success = 200 <= result.status_code < 400
                
                if request and hasattr(request, 'user') and request.user.is_authenticated:
                    user = request.user
                
                LoggingUtils.log_authentication_event(
                    event_type=event_type,
                    user=user,
                    success=success,
                    request=request,
                    additional_data={'function': func.__name__}
                )
                
                return result
                
            except Exception as e:
                # Log failed authentication
                LoggingUtils.log_authentication_event(
                    event_type=event_type,
                    success=False,
                    failure_reason=str(e),
                    request=request,
                    additional_data={'function': func.__name__, 'error': str(e)}
                )
                
                raise
        
        return wrapper
    return decorator


def log_errors(error_type="UNKNOWN", severity="MEDIUM"):
    """
    Decorator to automatically log errors
    
    Usage:
    @log_errors(error_type='VALIDATION', severity='LOW')
    def validate_data(request):
        pass
    """
    def decorator(func):
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            config = LoggingUtils.get_log_config()
            if not config.enable_error_logging:
                return func(*args, **kwargs)
            
            try:
                return func(*args, **kwargs)
            except Exception as e:
                # Try to get request and user from args
                request = None
                user = None
                for arg in args:
                    if hasattr(arg, 'user') and hasattr(arg, 'method'):
                        request = arg
                        user = arg.user if arg.user.is_authenticated else None
                        break
                
                LoggingUtils.log_error_manual(
                    error_type=error_type,
                    error_message=str(e),
                    view_name=f"{func.__module__}.{func.__name__}",
                    severity=severity,
                    user=user,
                    request=request,
                    additional_data={'function': func.__name__}
                )
                
                raise
        
        return wrapper
    return decorator


class LogAnalytics:
    """Analytics and reporting utilities for logs"""

    @staticmethod
    def get_error_summary(days=7):
        """Get error summary for the last N days"""
        from django.utils import timezone
        from datetime import timedelta
        from django.db.models import Count

        end_date = timezone.now()
        start_date = end_date - timedelta(days=days)

        errors = ErrorLog.objects.filter(
            timestamp__gte=start_date,
            timestamp__lte=end_date
        )

        summary = {
            'total_errors': errors.count(),
            'by_type': list(errors.values('error_type').annotate(count=Count('id'))),
            'by_severity': list(errors.values('severity').annotate(count=Count('id'))),
            'unresolved': errors.filter(resolved=False).count(),
            'critical_errors': errors.filter(severity='CRITICAL').count(),
        }

        return summary

    @staticmethod
    def get_performance_summary(days=7):
        """Get performance summary for the last N days"""
        from django.utils import timezone
        from datetime import timedelta
        from django.db.models import Avg, Max, Min, Count

        end_date = timezone.now()
        start_date = end_date - timedelta(days=days)

        perf_logs = PerformanceLog.objects.filter(
            created_at__gte=start_date,
            created_at__lte=end_date
        )

        summary = {
            'total_requests': perf_logs.count(),
            'avg_response_time': perf_logs.aggregate(Avg('duration'))['duration__avg'],
            'max_response_time': perf_logs.aggregate(Max('duration'))['duration__max'],
            'min_response_time': perf_logs.aggregate(Min('duration'))['duration__min'],
            'slow_requests': perf_logs.filter(duration__gt=2.0).count(),
            'avg_db_queries': perf_logs.aggregate(Avg('db_queries_count'))['db_queries_count__avg'],
            'by_status_code': list(perf_logs.values('status_code').annotate(count=Count('id'))),
        }

        return summary

    @staticmethod
    def get_user_activity_summary(days=7):
        """Get user activity summary for the last N days"""
        from django.utils import timezone
        from datetime import timedelta
        from django.db.models import Count

        end_date = timezone.now()
        start_date = end_date - timedelta(days=days)

        activities = UserActivity.objects.filter(
            timestamp__gte=start_date,
            timestamp__lte=end_date
        )

        summary = {
            'total_activities': activities.count(),
            'unique_users': activities.values('user').distinct().count(),
            'by_type': list(activities.values('activity_type').annotate(count=Count('id'))),
            'successful_activities': activities.filter(success=True).count(),
            'failed_activities': activities.filter(success=False).count(),
        }

        return summary

    @staticmethod
    def get_security_summary(days=7):
        """Get security incidents summary for the last N days"""
        from django.utils import timezone
        from datetime import timedelta
        from django.db.models import Count

        end_date = timezone.now()
        start_date = end_date - timedelta(days=days)

        incidents = SecurityIncident.objects.filter(
            timestamp__gte=start_date,
            timestamp__lte=end_date
        )

        summary = {
            'total_incidents': incidents.count(),
            'by_type': list(incidents.values('incident_type').annotate(count=Count('id'))),
            'by_severity': list(incidents.values('severity').annotate(count=Count('id'))),
            'unresolved': incidents.filter(resolved=False).count(),
            'critical_incidents': incidents.filter(severity='CRITICAL').count(),
            'blocked_incidents': incidents.filter(blocked=True).count(),
        }

        return summary


class LogCleanup:
    """Utilities for log cleanup and maintenance"""

    @staticmethod
    def cleanup_old_logs():
        """Clean up old logs based on configuration"""
        try:
            config = LoggingUtils.get_log_config()
            retention_days = config.log_retention_days

            from django.utils import timezone
            from datetime import timedelta

            cutoff_date = timezone.now() - timedelta(days=retention_days)

            # Clean up each log type
            deleted_counts = {}

            # Performance logs
            count = PerformanceLog.objects.filter(created_at__lt=cutoff_date).count()
            PerformanceLog.objects.filter(created_at__lt=cutoff_date).delete()
            deleted_counts['performance'] = count

            # Error logs (keep critical errors longer)
            count = ErrorLog.objects.filter(
                timestamp__lt=cutoff_date,
                severity__in=['LOW', 'MEDIUM']
            ).count()
            ErrorLog.objects.filter(
                timestamp__lt=cutoff_date,
                severity__in=['LOW', 'MEDIUM']
            ).delete()
            deleted_counts['errors'] = count

            # User activities
            count = UserActivity.objects.filter(timestamp__lt=cutoff_date).count()
            UserActivity.objects.filter(timestamp__lt=cutoff_date).delete()
            deleted_counts['activities'] = count

            # API access logs
            count = APIAccessLog.objects.filter(timestamp__lt=cutoff_date).count()
            APIAccessLog.objects.filter(timestamp__lt=cutoff_date).delete()
            deleted_counts['api_access'] = count

            # Database query logs
            count = DatabaseQueryLog.objects.filter(timestamp__lt=cutoff_date).count()
            DatabaseQueryLog.objects.filter(timestamp__lt=cutoff_date).delete()
            deleted_counts['db_queries'] = count

            # Authentication logs
            count = AuthenticationLog.objects.filter(timestamp__lt=cutoff_date).count()
            AuthenticationLog.objects.filter(timestamp__lt=cutoff_date).delete()
            deleted_counts['auth'] = count

            logger.info(f"Log cleanup completed: {deleted_counts}")
            return deleted_counts

        except Exception as e:
            logger.error(f"Error during log cleanup: {e}")
            return {}
