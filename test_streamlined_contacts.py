#!/usr/bin/env python3
"""
Comprehensive test for the streamlined contact management system
"""

import os
import sys
import django
import json
import uuid

# Add the project directory to Python path
sys.path.insert(0, '/Users/<USER>/Documents/code/shash_b')

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'shashtrarth.settings')
django.setup()

from django.contrib.auth.models import User
from students.models import Student
from contacts.models import Contact, normalize_phone_number
from rest_framework.test import APIClient
from rest_framework_simplejwt.tokens import RefreshToken
from django.utils import timezone


def create_test_users():
    """Create test users for testing"""

    # Generate unique phone numbers
    unique_id = uuid.uuid4().hex[:6]

    # Create student 1
    user1 = User.objects.create_user(
        username=f'student1_{unique_id}',
        email=f'student1_{unique_id}@example.com',
        password='testpass123'
    )
    student1 = Student.objects.create(
        user=user1,
        phone=f'987654{unique_id[:4]}'
    )

    # Create student 2 (will be in student1's contacts)
    user2 = User.objects.create_user(
        username=f'student2_{unique_id}',
        email=f'student2_{unique_id}@example.com',
        password='testpass123'
    )
    student2 = Student.objects.create(
        user=user2,
        phone=f'987655{unique_id[:4]}'
    )

    return {
        'student1': {'user': user1, 'profile': student1},
        'student2': {'user': user2, 'profile': student2}
    }


def test_phone_normalization():
    """Test phone number normalization"""
    print("\n=== Testing Phone Number Normalization ===")
    
    test_cases = [
        ('+************', '9876543210'),
        ('91-9876543210', '9876543210'),
        ('9876543210', '9876543210'),
        ('+91 9876 543 210', '9876543210'),
        ('************', '9876543210'),
        ('invalid', 'invalid'),  # Should return original
        ('123', '123'),  # Too short, should return original
    ]
    
    for input_phone, expected in test_cases:
        result = normalize_phone_number(input_phone)
        status = "✓" if result == expected else "✗"
        print(f"{status} {input_phone} -> {result} (expected: {expected})")


def test_contact_sync():
    """Test contact sync functionality"""
    print("\n=== Testing Contact Sync ===")
    
    users = create_test_users()
    user1 = users['student1']['user']
    user2 = users['student2']['user']
    
    # Test data - student1 syncing contacts including student2
    student2_phone = users['student2']['profile'].phone
    contacts_data = [
        {'name': 'John Doe', 'contact': student2_phone},  # This is student2
        {'name': 'Jane Smith', 'contact': '9876543212'},  # Unregistered
        {'name': 'Bob Johnson', 'contact': '9876543213'},  # Unregistered
        {'name': 'Alice Brown', 'contact': '+91-9876543214'},  # Unregistered (with formatting)
    ]
    
    # Sync contacts for user1
    result = Contact.sync_contacts_for_user(user1, contacts_data)
    
    print(f"✓ Sync result: {result}")
    print(f"  - Created: {result['created']}")
    print(f"  - Updated: {result['updated']}")
    print(f"  - Matched: {result['matched']}")
    print(f"  - Errors: {len(result['errors'])}")
    
    # Verify contacts were created
    user1_contacts = Contact.objects.filter(user=user1)
    print(f"✓ Total contacts for user1: {user1_contacts.count()}")
    
    # Check matched contacts
    matched_contacts = Contact.objects.filter(user=user1, is_matched=True)
    print(f"✓ Matched contacts: {matched_contacts.count()}")
    
    for contact in matched_contacts:
        print(f"  - {contact.name} ({contact.contact_number}) -> {contact.related_user.username}")
    
    return users, user1_contacts


def test_api_endpoints():
    """Test API endpoints"""
    print("\n=== Testing API Endpoints ===")
    
    users = create_test_users()
    user1 = users['student1']['user']
    
    # Set up API client
    client = APIClient()
    token = str(RefreshToken.for_user(user1).access_token)
    client.credentials(HTTP_AUTHORIZATION=f'Bearer {token}')
    
    # Test contact sync endpoint
    student2_phone = users['student2']['profile'].phone
    sync_data = {
        'contacts': [
            {'name': 'API Contact 1', 'contact': student2_phone},  # student2
            {'name': 'API Contact 2', 'contact': '9876543215'},  # unregistered
            {'name': 'API Contact 3', 'contact': '9876543216'},  # unregistered
        ]
    }
    
    response = client.post('/api/contacts/sync/', sync_data, format='json')
    print(f"✓ Contact sync API: {response.status_code}")
    if response.status_code == 201:
        data = response.json()
        print(f"  - Success: {data['success']}")
        print(f"  - Created: {data['data']['created']}")
        print(f"  - Matched: {data['data']['matched']}")
    else:
        print(f"  - Error: {response.json()}")
    
    # Test get user contacts
    response = client.get('/api/contacts/my-contacts/')
    print(f"✓ Get user contacts: {response.status_code}")
    if response.status_code == 200:
        data = response.json()
        if 'results' in data:
            print(f"  - Found {len(data['results'])} contacts")
        else:
            print(f"  - Found {len(data['data'])} contacts")
    
    # Test matched contacts endpoint
    response = client.get('/api/contacts/matched/')
    print(f"✓ Get matched contacts: {response.status_code}")
    if response.status_code == 200:
        data = response.json()
        if 'results' in data:
            matched_count = len(data['results'])
        else:
            matched_count = len(data['data'])
        print(f"  - Found {matched_count} matched contacts")
    
    # Test contact stats
    response = client.get('/api/contacts/stats/')
    print(f"✓ Get contact stats: {response.status_code}")
    if response.status_code == 200:
        data = response.json()
        stats = data['data']
        print(f"  - Total synced: {stats['total_synced']}")
        print(f"  - Total matched: {stats['total_matched']}")
        print(f"  - Match rate: {stats['match_rate']}%")
    
    # Test search
    response = client.get('/api/contacts/search/?query=API')
    print(f"✓ Search contacts: {response.status_code}")
    if response.status_code == 200:
        data = response.json()
        if 'results' in data:
            search_count = len(data['results'])
        else:
            search_count = len(data['data'])
        print(f"  - Found {search_count} contacts matching 'API'")


def test_cleanup_functionality():
    """Test automatic cleanup of unmatched contacts"""
    print("\n=== Testing Cleanup Functionality ===")
    
    # Create some old unmatched contacts
    user = User.objects.create_user(
        username=f'cleanup_user_{uuid.uuid4().hex[:8]}',
        email=f'cleanup_{uuid.uuid4().hex[:8]}@example.com',
        password='testpass123'
    )
    student = Student.objects.create(user=user, phone='9876543220')
    
    # Create contacts with different ages
    old_time = timezone.now() - timezone.timedelta(hours=25)  # Older than 24 hours
    recent_time = timezone.now() - timezone.timedelta(hours=1)  # Recent
    
    # Old unmatched contact (should be cleaned up)
    old_contact = Contact.objects.create(
        user=user,
        name='Old Contact',
        contact_number='9876543221',
        is_matched=False
    )
    old_contact.synced_at = old_time
    old_contact.save()
    
    # Recent unmatched contact (should be kept)
    recent_contact = Contact.objects.create(
        user=user,
        name='Recent Contact',
        contact_number='9876543222',
        is_matched=False
    )
    recent_contact.synced_at = recent_time
    recent_contact.save()
    
    # Matched contact (should be kept regardless of age)
    matched_contact = Contact.objects.create(
        user=user,
        name='Matched Contact',
        contact_number='9876543223',
        is_matched=True
    )
    matched_contact.synced_at = old_time
    matched_contact.save()
    
    print(f"✓ Created test contacts:")
    print(f"  - Old unmatched: {old_contact.id}")
    print(f"  - Recent unmatched: {recent_contact.id}")
    print(f"  - Old matched: {matched_contact.id}")
    
    # Test cleanup
    deleted_count = Contact.cleanup_unmatched_contacts(hours=24)
    print(f"✓ Cleanup deleted {deleted_count} contacts")
    
    # Verify results
    remaining_contacts = Contact.objects.filter(user=user)
    print(f"✓ Remaining contacts: {remaining_contacts.count()}")
    
    for contact in remaining_contacts:
        age_hours = (timezone.now() - contact.synced_at).total_seconds() / 3600
        print(f"  - {contact.name}: {contact.is_matched} matched, {age_hours:.1f} hours old")


def test_deduplication():
    """Test contact deduplication"""
    print("\n=== Testing Contact Deduplication ===")
    
    user = User.objects.create_user(
        username=f'dedup_user_{uuid.uuid4().hex[:8]}',
        email=f'dedup_{uuid.uuid4().hex[:8]}@example.com',
        password='testpass123'
    )
    student = Student.objects.create(user=user, phone='9876543230')
    
    # First sync
    contacts_data1 = [
        {'name': 'John Doe', 'contact': '9876543231'},
        {'name': 'Jane Smith', 'contact': '9876543232'},
    ]
    
    result1 = Contact.sync_contacts_for_user(user, contacts_data1)
    print(f"✓ First sync: {result1}")
    
    # Second sync with same contacts (should update, not create new)
    contacts_data2 = [
        {'name': 'John Doe Updated', 'contact': '9876543231'},  # Same number, updated name
        {'name': 'Jane Smith', 'contact': '9876543232'},  # Same contact
        {'name': 'Bob Johnson', 'contact': '9876543233'},  # New contact
    ]
    
    result2 = Contact.sync_contacts_for_user(user, contacts_data2)
    print(f"✓ Second sync: {result2}")
    
    # Verify total contacts
    total_contacts = Contact.objects.filter(user=user).count()
    print(f"✓ Total unique contacts: {total_contacts} (should be 3)")
    
    # Check if name was updated
    updated_contact = Contact.objects.get(user=user, contact_number='9876543231')
    print(f"✓ Updated contact name: {updated_contact.name} (should be 'John Doe Updated')")


def main():
    """Main test function"""
    print("=" * 60)
    print("STREAMLINED CONTACT MANAGEMENT SYSTEM TEST")
    print("=" * 60)
    
    try:
        test_phone_normalization()
        test_contact_sync()
        test_api_endpoints()
        test_cleanup_functionality()
        test_deduplication()
        
        print("\n" + "=" * 60)
        print("✓ ALL TESTS COMPLETED SUCCESSFULLY!")
        print("=" * 60)
        
        # Final statistics
        total_contacts = Contact.objects.count()
        matched_contacts = Contact.objects.filter(is_matched=True).count()
        unmatched_contacts = Contact.objects.filter(is_matched=False).count()
        
        print(f"\nFinal Statistics:")
        print(f"Total contacts: {total_contacts}")
        print(f"Matched contacts: {matched_contacts}")
        print(f"Unmatched contacts: {unmatched_contacts}")
        print(f"Match rate: {(matched_contacts/total_contacts*100):.1f}%" if total_contacts > 0 else "N/A")
        
    except Exception as e:
        print(f"\n✗ Test failed with error: {str(e)}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
