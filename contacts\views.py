from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework import status
from rest_framework.permissions import IsAuthenticated
from rest_framework.pagination import PageNumberPagination
from django.db.models import Q
from django.shortcuts import get_object_or_404
from django.db import transaction
from django.utils import timezone
import logging

from .models import Contact
from .serializers import (
    ContactSyncSerializer, ContactSerializer, ContactStatsSerializer, ContactSearchSerializer
)
from .permissions import IsStudentOwner, IsAdminOnly, IsStudentOwnerOrAdminReadOnly, get_user_role
from log_admin.models import UserActivity
from students.models import Student

logger = logging.getLogger(__name__)


class ContactPagination(PageNumberPagination):
    """Custom pagination for contact lists"""
    page_size = 50
    page_size_query_param = 'page_size'
    max_page_size = 200


class ContactSyncView(APIView):
    """
    API endpoint for syncing contacts from Android app.
    Only students can sync their contacts.
    Expects JSON data in format: {"contacts": [{"name": "...", "contact": "..."}, ...]}
    """
    permission_classes = [IsStudentOwner]

    def post(self, request):
        """Sync contacts from phone"""
        serializer = ContactSyncSerializer(data=request.data, context={'request': request})

        if serializer.is_valid():
            try:
                result = serializer.save()

                # Log the activity
                UserActivity.objects.create(
                    user=request.user,
                    action="Contact Sync",
                    metadata={
                        "created": result['created'],
                        "updated": result['updated'],
                        "matched": result['matched'],
                        "total_processed": result['total_processed'],
                        "timestamp": timezone.now().isoformat()
                    }
                )

                return Response({
                    'success': True,
                    'message': f"Successfully synced {result['total_processed']} contacts",
                    'data': {
                        'created': result['created'],
                        'updated': result['updated'],
                        'matched': result['matched'],
                        'total_processed': result['total_processed'],
                        'errors': result['errors']
                    }
                }, status=status.HTTP_201_CREATED)

            except Exception as e:
                logger.error(f"Error syncing contacts for user {request.user.username}: {str(e)}")
                return Response({
                    'success': False,
                    'message': 'An error occurred while syncing contacts',
                    'error': str(e)
                }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

        return Response({
            'success': False,
            'message': 'Invalid data provided',
            'errors': serializer.errors
        }, status=status.HTTP_400_BAD_REQUEST)


class UserContactsView(APIView):
    """
    API endpoint to retrieve user's synced contacts.
    Students can only see their own contacts.
    Admins can see all contacts (read-only).
    """
    permission_classes = [IsStudentOwnerOrAdminReadOnly]
    pagination_class = ContactPagination

    def get(self, request):
        """Get user's synced contacts with proper access control"""
        user_role = get_user_role(request.user)

        # Get query parameters
        show_matched_only = request.query_params.get('matched_only', 'false').lower() == 'true'

        if user_role == 'admin':
            # Admins can see all contacts but read-only
            contacts = Contact.objects.all().select_related('user', 'related_user').order_by('-synced_at')
        elif user_role == 'student':
            # Students can only see their own contacts
            contacts = Contact.objects.filter(user=request.user).select_related('related_user').order_by('-synced_at')
        else:
            # This should not happen due to permission class, but safety check
            return Response({
                'success': False,
                'message': 'Access denied. Only students and admins can access contact data.'
            }, status=status.HTTP_403_FORBIDDEN)

        # Filter by matched status if requested
        if show_matched_only:
            contacts = contacts.filter(is_matched=True)

        # Apply pagination
        paginator = self.pagination_class()
        page = paginator.paginate_queryset(contacts, request)

        if page is not None:
            serializer = ContactSerializer(page, many=True)
            return paginator.get_paginated_response(serializer.data)

        serializer = ContactSerializer(contacts, many=True)
        return Response({
            'success': True,
            'data': serializer.data
        })


class MatchedContactsView(APIView):
    """
    API endpoint to get contacts that are registered users.
    This answers: "Who from my contacts is on the app?"
    """
    permission_classes = [IsStudentOwner]
    pagination_class = ContactPagination

    def get(self, request):
        """Get user's matched contacts (registered users)"""
        # Get only matched contacts for the current user
        matched_contacts = Contact.get_user_matches(request.user)

        # Apply pagination
        paginator = self.pagination_class()
        page = paginator.paginate_queryset(matched_contacts, request)

        if page is not None:
            serializer = ContactSerializer(page, many=True)
            return paginator.get_paginated_response(serializer.data)

        serializer = ContactSerializer(matched_contacts, many=True)
        return Response({
            'success': True,
            'data': serializer.data,
            'message': f"Found {matched_contacts.count()} contacts who are registered users"
        })


class ContactStatsView(APIView):
    """
    API endpoint to get user's contact statistics.
    """
    permission_classes = [IsStudentOwnerOrAdminReadOnly]

    def get(self, request):
        """Get contact statistics"""
        user_role = get_user_role(request.user)

        if user_role == 'admin':
            # Admin gets global statistics
            total_contacts = Contact.objects.count()
            total_matched = Contact.objects.filter(is_matched=True).count()
            total_unmatched = Contact.objects.filter(is_matched=False).count()

            # Recent activity
            from django.utils import timezone
            last_24h = timezone.now() - timezone.timedelta(hours=24)
            recent_syncs = Contact.objects.filter(synced_at__gte=last_24h).count()

            return Response({
                'success': True,
                'data': {
                    'total_contacts': total_contacts,
                    'total_matched': total_matched,
                    'total_unmatched': total_unmatched,
                    'recent_syncs_24h': recent_syncs,
                    'match_rate': round((total_matched / total_contacts * 100) if total_contacts > 0 else 0, 2)
                }
            })

        elif user_role == 'student':
            # Student gets their own statistics
            user_contacts = Contact.objects.filter(user=request.user)
            total_synced = user_contacts.count()
            total_matched = user_contacts.filter(is_matched=True).count()
            total_unmatched = user_contacts.filter(is_matched=False).count()

            # Get last sync time
            last_sync = user_contacts.order_by('-synced_at').first()
            last_sync_time = last_sync.synced_at if last_sync else None

            return Response({
                'success': True,
                'data': {
                    'total_synced': total_synced,
                    'total_matched': total_matched,
                    'total_unmatched': total_unmatched,
                    'last_sync': last_sync_time,
                    'match_rate': round((total_matched / total_synced * 100) if total_synced > 0 else 0, 2)
                }
            })

        else:
            return Response({
                'success': False,
                'message': 'Access denied'
            }, status=status.HTTP_403_FORBIDDEN)


class ContactSearchView(APIView):
    """
    API endpoint to search contacts.
    Students can only search their own contacts.
    Admins can search all contacts (read-only).
    """
    permission_classes = [IsStudentOwnerOrAdminReadOnly]
    pagination_class = ContactPagination

    def get(self, request):
        """Search contacts based on various criteria"""
        serializer = ContactSearchSerializer(data=request.query_params)

        if not serializer.is_valid():
            return Response({
                'success': False,
                'message': 'Invalid search parameters',
                'errors': serializer.errors
            }, status=status.HTTP_400_BAD_REQUEST)

        # Start with contacts based on user role
        user_role = get_user_role(request.user)

        if user_role == 'admin':
            # Admins can search all contacts
            contacts = Contact.objects.all().select_related('user', 'related_user')
        elif user_role == 'student':
            # Students can only search their own contacts
            contacts = Contact.objects.filter(user=request.user).select_related('related_user')
        else:
            return Response({
                'success': False,
                'message': 'Access denied'
            }, status=status.HTTP_403_FORBIDDEN)

        # Apply filters
        query = serializer.validated_data.get('query')
        if query:
            contacts = contacts.filter(
                Q(name__icontains=query) |
                Q(contact_number__icontains=query)
            )

        contact_number = serializer.validated_data.get('contact_number')
        if contact_number:
            contacts = contacts.filter(contact_number=contact_number)

        is_matched = serializer.validated_data.get('is_matched')
        if is_matched is not None:
            contacts = contacts.filter(is_matched=is_matched)

        # Apply pagination
        paginator = self.pagination_class()
        page = paginator.paginate_queryset(contacts, request)

        if page is not None:
            serializer = ContactSerializer(page, many=True)
            return paginator.get_paginated_response(serializer.data)

        serializer = ContactSerializer(contacts, many=True)
        return Response({
            'success': True,
            'data': serializer.data
        })


class AdminContactsView(APIView):
    """
    Admin-only endpoint to view all student contacts.
    Only superusers/staff can access this endpoint.
    """
    permission_classes = [IsAdminOnly]
    pagination_class = ContactPagination

    def get(self, request):
        """Get all student contacts for admin view"""
        # Get query parameters
        user_id = request.query_params.get('user_id')
        search = request.query_params.get('search')
        show_matched_only = request.query_params.get('matched_only', 'false').lower() == 'true'

        # Start with all contacts
        contacts = Contact.objects.all().select_related('user', 'related_user').order_by('-synced_at')

        # Filter by user if specified
        if user_id:
            contacts = contacts.filter(user_id=user_id)

        # Filter by matched status if requested
        if show_matched_only:
            contacts = contacts.filter(is_matched=True)

        # Search functionality
        if search:
            contacts = contacts.filter(
                Q(name__icontains=search) |
                Q(contact_number__icontains=search) |
                Q(user__username__icontains=search) |
                Q(user__email__icontains=search)
            )

        # Apply pagination
        paginator = self.pagination_class()
        page = paginator.paginate_queryset(contacts, request)

        if page is not None:
            # Custom serializer data for admin view
            data = []
            for contact in page:
                data.append({
                    'id': contact.id,
                    'name': contact.name,
                    'contact_number': contact.contact_number,
                    'is_matched': contact.is_matched,
                    'user': {
                        'id': contact.user.id,
                        'username': contact.user.username,
                        'email': contact.user.email,
                        'first_name': contact.user.first_name,
                        'last_name': contact.user.last_name
                    },
                    'related_user': {
                        'id': contact.related_user.id,
                        'username': contact.related_user.username,
                        'email': contact.related_user.email
                    } if contact.related_user else None,
                    'synced_at': contact.synced_at,
                    'updated_at': contact.updated_at
                })

            return paginator.get_paginated_response(data)

        # If no pagination
        data = []
        for contact in contacts:
            data.append({
                'id': contact.id,
                'name': contact.name,
                'contact_number': contact.contact_number,
                'is_matched': contact.is_matched,
                'user': {
                    'id': contact.user.id,
                    'username': contact.user.username,
                    'email': contact.user.email,
                    'first_name': contact.user.first_name,
                    'last_name': contact.user.last_name
                },
                'related_user': {
                    'id': contact.related_user.id,
                    'username': contact.related_user.username,
                    'email': contact.related_user.email
                } if contact.related_user else None,
                'synced_at': contact.synced_at,
                'updated_at': contact.updated_at
            })

        return Response({
            'success': True,
            'data': data
        })
