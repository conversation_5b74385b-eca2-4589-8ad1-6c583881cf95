from django.urls import path
from .views import (
    AdminWithdrawalRequestUpdateView,
    RegisterView,
    VerifyOTPView,
    ResendOTPView,
    LoginView,
    LogoutView,
    StudentListView,
    StudentDetailView,
    TokenRefreshView,
    CreateReferralLinkView,
    SignupRequestView,
    VerifyOTPAndRegisterView,
    WithdrawalRequestView,
    ReferralQRCodeView,
    LoginwithGoogle,
    SignupContentView,
    SignupContentDetailView,
    ScratchCardListView,
    ScratchCardRevealView,
)

urlpatterns = [
    path("register/", RegisterView.as_view(), name="register"),
    path("verify-otp/", VerifyOTPView.as_view(), name="verify-otp"),
    path("resend-otp/", ResendOTPView.as_view(), name="resend_otp"),
    path("object/<int:pk>/", StudentDetailView.as_view(), name="student-detail"),
    path("login/", LoginView.as_view(), name="student-login"),
    path("logout/", LogoutView.as_view(), name="student-logout"),
    path("token/refresh/", TokenRefreshView.as_view(), name="token_refresh"),
    path("list/", StudentListView.as_view(), name="student_list"),
    path("create-link/", CreateReferralLinkView.as_view(), name="create_referral_link"),
    path(
        "signup-request/<str:reffer_code>",
        SignupRequestView.as_view(),
        name="signup-request",
    ),
    path(
        "verify-otp-register/",
        VerifyOTPAndRegisterView.as_view(),
        name="verify-otp-register",
    ),
    path(
        "withdrawal/request/",
        WithdrawalRequestView.as_view(),
        name="withdrawal_request",
    ),
    path(
        "withdrawal/request/<str:transaction_id>/",
        WithdrawalRequestView.as_view(),
        name="withdrawal_request_detail",
    ),
    path(
        "withdrawal/request/update/<str:transaction_id>/",
        AdminWithdrawalRequestUpdateView.as_view(),
        name="admin_withdrawal_update",
    ),
    path("generate-qr/", ReferralQRCodeView.as_view(), name="generate_qr"),
    path("login_with_goole_student/",LoginwithGoogle.as_view(), name="login_with_goole_student"),
    path('signup-content/', SignupContentView.as_view(), name='signup-content'),
    path('signup-content-details/<pk>/', SignupContentDetailView.as_view(), name='signup-content-delete'),
    path("scratch-cards/", ScratchCardListView.as_view(), name="scratch_card_list"),
    path("scratch-cards/scratch/", ScratchCardRevealView.as_view(), name="scratch_card_action"),

]
