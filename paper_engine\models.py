from django.db import models
from django.contrib.auth.models import User
from students.models import Student
import random
from django.core.cache import cache
from django.conf import settings

class TestPattern(models.Model):
    pattern_id = models.BigAutoField(
        primary_key=True, help_text="Unique auto-incrementing ID for the test pattern"
    )
    name = models.CharField(max_length=255)
    number_of_sections = models.PositiveIntegerField(default=0)
   
    sections = models.JSONField(
        help_text="Number of questions for each section (e.g., {'Section 1': 10, 'Section 2': 15})"
    )
    negative_marking = models.FloatField(
        default=0.0, help_text="Negative marking for wrong answers (e.g., -0.25)"
    )
    positive_mark = models.FloatField(
        default=1.0, help_text="Positive marks for correct answers"
    )
    version = models.CharField(max_length=50, default="1.0")
    updated_at = models.DateTimeField(auto_now=True)
    difficulty = models.PositiveIntegerField(default=1)
    contributor = models.ForeignKey(
        "contributor.ContributorProfile",
        on_delete=models.CASCADE,
        related_name="test_patterns",
    )

    # New fields for percentages and classifications
    normal_question_percentage = models.FloatField(
        default=50.0, help_text="Percentage of normal questions (e.g., 50 for 50%)"
    )
    is_normal_question = models.BooleanField(
        default=True, help_text="Are normal questions included in this test pattern?"
    )
    master_question_percentage = models.FloatField(
        default=30.0, help_text="Percentage of master questions (e.g., 30 for 30%)"
    )
    is_master_question = models.BooleanField(
        default=False, help_text="Are master questions included in this test pattern?"
    )
    master_option_percentage = models.FloatField(
        default=20.0, help_text="Percentage of master options (e.g., 20 for 20%)"
    )
    is_master_option = models.BooleanField(
        default=False, help_text="Are master options included in this test pattern?"
    )
    rank = models.IntegerField(default=0, help_text="Rank of section for ordering") 
    class Meta:
        verbose_name = "Test Pattern"
        verbose_name_plural = "Test Patterns"
        app_label = "paper_engine"

    def __str__(self):
        return f"{self.name} - {self.pattern_id}"
    
    def save(self, *args, **kwargs):
        super().save(*args, **kwargs)
        # Invalidate cache on save
        cache.delete(f'test_pattern_{self.pattern_id}')
        cache.delete('all_test_patterns')
    
    @classmethod
    def get_cached_pattern(cls, pattern_id):
        cache_key = f'test_pattern_{pattern_id}'
        pattern = cache.get(cache_key)
        if not pattern:
            pattern = cls.objects.get(pk=pattern_id)
            cache.set(cache_key, pattern, settings.TEST_PATTERN_CACHE_TIMEOUT)
        return pattern
    
    @classmethod
    def get_all_cached_patterns(cls):
        patterns = cache.get('all_test_patterns')
        if not patterns:
            patterns = list(cls.objects.all())
            cache.set('all_test_patterns', patterns, settings.TEST_PATTERN_CACHE_TIMEOUT)
        return patterns
    
    def total_subjects(self):
        if not self.sections:
            return []
        sum = []
        for questions in self.sections:
            sum +=questions["subject_ids"]
        return sum

    def total_questions(self):
        """
        Calculate the total number of questions based on sections.
        """
        if not self.sections:
            return 0
        sum = 0
        for questions in self.sections:
            sum +=questions["number_of_questions"]
        return sum

    def fetch_difficulty_level(self):
        if 1 <= self.difficulty <= 4:
            return [1,2,3,4,]
        elif 5 <= self.difficulty <= 7:
            return [5,6,7]
        elif 8 <= self.difficulty <= 9:
            return [8,9]
        elif self.difficulty == 10:
            return [10]
        else:
            return []

  

    def question_distribution(self, section_question):
        """
        Generate the distribution of questions based on percentages.
        """
        
        total_questions = section_question
        sections_count = len(self.sections)

        # Calculate total question type counts
        normal_questions = int((self.normal_question_percentage / 100) * total_questions)
        master_questions = int((self.master_question_percentage / 100) * total_questions)
        master_options = int((self.master_option_percentage / 100) * total_questions)

        # Adjust normal questions to maintain total count integrity
        normal_questions += section_question - (normal_questions + master_questions + master_options)

        # Create a list to hold question counts for each section
        section_distribution = [{"normal_questions": 0, "master_questions": 0, "master_options": 0} for _ in range(sections_count)]

        # Function to distribute questions randomly
        def distribute_questions(question_type, total_count, key):
            section_indices = list(range(sections_count))
            random.shuffle(section_indices)  # Shuffle to ensure randomness

            for _ in range(total_count):
                index = random.choice(section_indices)  # Randomly select a section
                section_distribution[index][key] += 1

        # Distribute each type of question
        distribute_questions("normal", normal_questions, "normal_questions")
        distribute_questions("master", master_questions, "master_questions")
        distribute_questions("master_option", master_options, "master_options")

        return section_distribution
   


class TestPaper(models.Model):
    """
    Represents a generated paper for a student, saved in the database, with questions and options.
    """

    paper_id = models.AutoField(primary_key=True)
    subcourse = models.ForeignKey(
        "questions.SubCourse", on_delete=models.CASCADE, related_name="test_papers"
    )
    # subject = models.ForeignKey(
    #     "questions.Subject", on_delete=models.CASCADE, related_name="test_papers"
    # )
    student = models.ForeignKey(
        "students.Student", on_delete=models.CASCADE, related_name="test_papers"
    )  # Assuming student is a User
    generated_at = models.DateTimeField(auto_now_add=True)
    status = models.CharField(max_length=20, null=True, blank=True)
    total_marks = models.IntegerField(default=0)  # Store total marks of paper
    time_limit = models.IntegerField(null=True, blank=True)  # Time limit in minutes
    duration_in_minutes = models.IntegerField(null=True, blank=True)
    created_at = models.DateTimeField(auto_now_add=True)
    # Stores the questions and options axs JSON field
    questions = models.JSONField(
        default=list
    )  # Stores the list of questions and their options in a JSON format.

    class Meta:
        app_label = "paper_engine"

    def __str__(self):
        return f"Paper for {self.subcourse.name} - {self.student.user.username} ({self.status})"

    def calculate_total_marks(self):
        """
        Calculates total marks of the paper based on the questions and marks of each question.
        """
        total_marks = sum(q["marks"] for q in self.questions)
        self.total_marks = total_marks
        self.save()
        return total_marks

class SubmitTest(models.Model):
    student = models.ForeignKey(Student, on_delete=models.CASCADE, related_name="submitted_tests")
    test_paper_id = models.ForeignKey(TestPaper, on_delete=models.CASCADE, related_name="submitted_tests")
    date = models.DateField()
    recommended_tier = models.CharField(max_length=20, null=True, blank=True)
    total_score = models.FloatField()
    correct_answers = models.IntegerField(default=0)
    incorrect_answers = models.IntegerField(default=0)
    negative_marks = models.FloatField(default=0)
    section_scores = models.JSONField(default=dict)
    accuracy = models.FloatField(default=0)
    created_at = models.DateTimeField(auto_now_add=True)
    status = models.CharField(max_length=20, default="pending")
    started_at = models.DateTimeField(null=True, blank=True)
    expires_at = models.DateTimeField(null=True, blank=True)
    def __str__(self):
        return f"SubmitTest for {self.student.user.username} on {self.date}"
    
from datetime import datetime, timedelta
from django.db.models import Sum

class PracticeRecord(models.Model):
    user = models.ForeignKey(User, on_delete=models.CASCADE)
    date = models.DateField(auto_now_add=True)
    questions_practiced = models.IntegerField(default=0)
    @classmethod
    def get_daily_practice(cls, user):
        today = datetime.today().date()
        return cls.objects.filter(user=user, date=today).aggregate(Sum('questions_practiced'))['questions_practiced__sum'] or 0

    @classmethod
    def get_weekly_practice(cls, user):
        today = datetime.today().date()
        start_of_week = today - timedelta(days=today.weekday())
        return cls.objects.filter(user=user, date__gte=start_of_week).aggregate(Sum('questions_practiced'))['questions_practiced__sum'] or 0

    @classmethod
    def get_monthly_practice(cls, user):
        today = datetime.today().date()
        start_of_month = today.replace(day=1)
        return cls.objects.filter(user=user, date__gte=start_of_month).aggregate(Sum('questions_practiced'))['questions_practiced__sum'] or 0

    @classmethod
    def get_continuous_practice(cls, user):
        records = cls.objects.filter(user=user).order_by('date')
        max_streak = 0
        current_streak = 0
        previous_date = None

        for record in records:
            if previous_date and (record.date - previous_date).days == 1:
                current_streak += 1
            else:
                current_streak = 1
            max_streak = max(max_streak, current_streak)
            previous_date = record.date

        return max_streak

class ScoreTracker(models.Model):
    user = models.ForeignKey(User, on_delete=models.CASCADE)
    points = models.IntegerField(default=0)
    last_test_date = models.DateField(null=True, blank=True)
    
class ExamTier(models.Model):
    name = models.CharField(max_length=50)  # e.g., "Tier 1"
    min_score = models.FloatField(help_text="Minimum score to qualify for this tier")
    max_score = models.FloatField(help_text="Maximum score allowed for this tier")
    exams = models.JSONField(help_text="List of exams under this tier")  # e.g., ["SSC CGL", "UPSC"]

    def __str__(self):
        return self.name