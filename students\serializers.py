import random
import string
from rest_framework import serializers
from django.contrib.auth.models import User
from django.contrib.auth import authenticate
from .models import Student, Referral, ReferralPoints, WithdrawalRequest, SignupContent, ScratchCard
from django.core.cache import cache
from rest_framework.exceptions import ValidationError
from django.utils import timezone
from django.core.mail import send_mail
from django.conf import settings
from wallet_and_transaction.models import Wallet


class SignupContentSerializer(serializers.ModelSerializer):
    class Meta:
        model = SignupContent
        fields = '__all__'

class UserSerializer(serializers.ModelSerializer):
    class Meta:
        model = User
        fields = [
            "id",
            "username",
            "email",
            "first_name",
            "last_name",
            "password",
        ]
        extra_kwargs = {"password": {"write_only": True}}


class StudentSerializer(serializers.ModelSerializer):
    user = UserSerializer()
    current_subscription_status = serializers.ReadOnlyField()

    class Meta:
        model = Student
        fields = [
            "user",
            "id",
            "student_id",
            "phone",
            "address",
            "course",
            "account_type",
            "account_status",
            "subscription_type",  # Keep for backward compatibility
            "current_subscription_status",  # New computed field
            "active_services",
            "state",
            "gender",
            "language_preferred",
            "referral_code",
            "total_points",
            "image_url",
            "streak",
            "streak_reason",
        ]

    def create(self, validated_data):
        # Safely attempt to get user data, or set to None if it doesn't exist
        user_data = validated_data.pop("user", None)

        if user_data:
            user = User.objects.create_user(**user_data)
        else:
            raise serializers.ValidationError(
                {"user": "User data is required to create a student."}
            )

        # Proceed to create the Student instance
        student = Student.objects.create(user=user, **validated_data)
        Wallet.objects.create(user=user)
        return student

    def verify_otp(self, email, otp):
        # Retrieve the OTP from the cache
        stored_otp = cache.get(email)

        if stored_otp is None:
            raise ValidationError("OTP has expired or does not exist.")

        if stored_otp != otp:
            raise ValidationError("Invalid OTP.")

        return True

    def update(self, instance, validated_data):
        user_data = validated_data.pop("user", {})

        # Remove username and password from user_data to prevent updates
        
        user_data.pop("password", None)  # Ignore password updates


        # Update user fields
        for attr, value in user_data.items():
            setattr(instance.user, attr, value)

        # Save user and student profile
        instance.user.save()
        for attr, value in validated_data.items():
            setattr(instance, attr, value)
        instance.save()

        return instance

    def to_internal_value(self, data):
        # Allow for an ID to be provided
        if isinstance(data, dict) and "id" in data:
            return {"id": data["id"]}
        elif isinstance(data, int):
            return {"id": data}
        return super().to_internal_value(data)
    
from datetime import timedelta
from django.utils.timezone import now

class LoginSerializer(serializers.Serializer):
    username = serializers.CharField()
    password = serializers.CharField()

    def validate(self, data):
        user = authenticate(**data)
        if user is None:
            raise serializers.ValidationError("Invalid credentials")
        student = Student.objects.get(user=user)
        self.update_streak(student)
        return {"user": user, "student": student}
    
    def update_streak(self, student):
        today = now().date()

        if student.last_login_date:
            last_login_date = student.last_login_date

            if last_login_date == today:
                student.streak_reason = "Already logged in today. No streak change."
                return
            elif last_login_date == today - timedelta(days=1):
                student.streak += 1
                student.streak_reason = f"Streak continued! You're on a {student.streak}-day streak."
            else:
                student.streak = 1
                missed_days = (today - last_login_date).days - 1
                student.streak_reason = f"Streak broken! You missed {missed_days} day(s). Starting fresh."
        else:
            student.streak = 1
            student.streak_reason = "First login. Streak started!"

        student.last_login_date = today
        student.save()

class CreateReferralLinkSerializer(serializers.ModelSerializer):
    referral_code = serializers.CharField(read_only=True)

    class Meta:
        model = Referral
        fields = ["referral_code", "created_at"]

    def create(self, validated_data):
        # Get the current logged-in student's profile
        student = self.context["request"].user.student_profile

        try:
            # Try to retrieve the latest referral for the student
            referral = Referral.objects.filter(referrer=student).last()

            # If no referral exists, create a new one
            if not referral:
                referral = Referral.objects.create(
                    referrer=student, referral_code=self.generate_unique_referral_code()
                )
        except Referral.DoesNotExist:
            # If there's an issue with the query, handle it by creating a new referral
            referral = Referral.objects.create(
                referrer=student, referral_code=self.generate_unique_referral_code()
            )

        return referral

    def generate_unique_referral_code(self):
        while True:
            code = "".join(random.choices(string.ascii_uppercase + string.digits, k=8))
            if not Referral.objects.filter(referral_code=code).exists():
                return code

def referral_point_generator():
    points = [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 20, 25, 27, 23]
    weights = [20, 10, 5, 3, 2, 1, 0.5, 0.5, 0.4, 0.2, 0.2, 0.1, 0.05, 0.05, 0.03]

    select_point = random.choices(points, weights=weights, k=1)[0]
    return select_point
    
class RegisterWithReferralSerializer(serializers.Serializer):
    user = UserSerializer()
    phone = serializers.CharField(required=True)
    course = serializers.CharField(required=True)
    referral_code = serializers.CharField(required=True)

    def validate_referral_code(self, value):
        try:
            # Check if the referral code exists and has not been used
            referral = Referral.objects.filter(referral_code=value).last()
        except Referral.DoesNotExist:
            raise serializers.ValidationError("Invalid referral code.")
        return referral

    def create(self, validated_data):

        user_data = validated_data.pop("user", {})
        username = user_data.get("username")
        email = user_data.get("email")
        password = user_data.get("password")
        phone = validated_data["phone"]
        referral_code = validated_data["referral_code"]

        # Create the User instance
        user = User.objects.create_user(
            username=username, email=email, password=password
        )

        # Create the Student instance
        student = Student.objects.create(user=user, phone=phone)

        # Associate the referred student with the referral
        referral = Referral.objects.filter(referral_code=referral_code).last()
        
        Referral.objects.create(
            referrer=referral.referrer, referred=student, referral_code=referral_code
        )

        # Try to retrieve the latest ReferralPoints object for the student
        referred_points = ReferralPoints.objects.filter(student=student).last()

        # If no ReferralPoints object exists, create a new one
        if not referred_points:
            referred_points = ReferralPoints.objects.create(student=student)

        select_point = referral_code_generator()
        referred_points.points += select_point
        referred_points.save()

        referral_user = Student.objects.get(user=referral.referrer.user)
        referral_user.total_points += select_point
        referral_user.refferred_count += 1
        referral_user.save()

        student.referral_code = referral.referral_code  # Set the referral code string
        student.save()

        return {"points_earned": referred_points.points}


class WithdrawalRequestSerializer(serializers.ModelSerializer):
    class Meta:
        model = WithdrawalRequest
        fields = [
            "student",
            "points",
            "upi_id",
            "notes",
            "transaction_id",
            "status",
            "created_at",
        ]
        read_only_fields = ["transaction_id", "status", "created_at", "student"]

    def validate_points(self, value):
        student = self.context["request"].user.student_profile
        if value > student.total_points:
            raise serializers.ValidationError("Insufficient points for withdrawal.")
        return value

    def generate_transaction_id(self):
        today = timezone.now().strftime("%Y%m%d")
        prefix = f"WD-{today}-"
        last_request = (
            WithdrawalRequest.objects.filter(transaction_id__startswith=prefix)
            .order_by("id")
            .last()
        )

        if last_request:
            last_id = int(last_request.transaction_id[-3:])
            new_id = f"{last_id + 1:03}"
        else:
            new_id = "001"

        return f"{prefix}{new_id}"

    def create(self, validated_data):
        student = self.context["request"].user.student_profile
        points = validated_data["points"]

        # Generate transaction ID
        validated_data["transaction_id"] = self.generate_transaction_id()

        # Deduct points from student
        student.total_points -= points
        student.save()

        # Create the withdrawal request
        withdrawal_request = WithdrawalRequest.objects.create(
            student=student,
            points=points,
            upi_id=validated_data["upi_id"],
            transaction_id=validated_data["transaction_id"],
        )

        # Send email notification to admin
        send_mail(
            subject="Withdrawal Request Submitted",
            message=f"Student {student.user.username} requested a withdrawal.\n"
            f"Transaction ID: {withdrawal_request.transaction_id}\n"
            f"Points: {points}\nUPI ID: {validated_data['upi_id']}\nNotes: {validated_data.get('notes', '')}",
            from_email=settings.DEFAULT_FROM_EMAIL,
            recipient_list=[settings.DEFAULT_FROM_EMAIL],
        )

        return withdrawal_request


class AdminWithdrawalUpdateSerializer(serializers.ModelSerializer):
    class Meta:
        model = WithdrawalRequest
        fields = ["status", "notes"]


class ScratchCardSerializer(serializers.ModelSerializer):
    class Meta:
        model = ScratchCard
        fields = '__all__'
        read_only_fields = ['referrer', 'referral', 'amount', 'created_at', 'scratched_at']

class ScratchCardRevealSerializer(serializers.Serializer):
    card_id = serializers.IntegerField()

    def validate_card_id(self, value):
        try:
            card = ScratchCard.objects.get(id=value, is_scratched=False)
            return card
        except ScratchCard.DoesNotExist:
            raise serializers.ValidationError("Invalid or already scratched card.")

    def save(self):
        card = self.validated_data['card_id']
        card.is_scratched = True
        card.scratched_at = timezone.now()
        card.save()
        referrer = card.referrer
        referrer.total_points += card.amount
        referrer.save()
        return card
