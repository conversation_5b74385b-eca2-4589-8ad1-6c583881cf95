"""
Notification system for subscription-related events
"""
import threading
from django.core.mail import send_mail, EmailMessage
from django.template.loader import render_to_string
from django.conf import settings
from firebase_admin import messaging
from notifications.models import FCMDevice
from students.models import Student, Referral, ScratchCard
from customrcare.models import CustomrcareProfile
import logging

logger = logging.getLogger(__name__)


class SubscriptionNotificationService:
    """Service class to handle all subscription-related notifications"""
    
    @staticmethod
    def send_subscription_confirmation(subscription):
        """Send confirmation notifications when a subscription is created"""
        student = subscription.student
        package = subscription.package
        
        # Send to student
        SubscriptionNotificationService._send_student_subscription_notification(student, package, subscription)
        
        # Send to referrer if applicable
        SubscriptionNotificationService._send_referrer_notification(student, subscription)
        
        # Send to customer care
        SubscriptionNotificationService._send_customer_care_notification(student, package, subscription)
    
    @staticmethod
    def send_subscription_activation(subscription):
        """Send notifications when subscription is activated after payment"""
        student = subscription.student
        package = subscription.package
        
        # Send activation email to student
        SubscriptionNotificationService._send_activation_email(student, package, subscription)
        
        # Send FCM notification to student
        SubscriptionNotificationService._send_activation_fcm(student, package)
    
    @staticmethod
    def send_subscription_expiry_warning(subscription, days_remaining):
        """Send warning notifications before subscription expires"""
        student = subscription.student
        package = subscription.package
        
        # Send email warning
        SubscriptionNotificationService._send_expiry_warning_email(student, package, days_remaining)
        
        # Send FCM notification
        SubscriptionNotificationService._send_expiry_warning_fcm(student, package, days_remaining)
    
    @staticmethod
    def _send_student_subscription_notification(student, package, subscription):
        """Send subscription confirmation to student"""
        def send_email():
            try:
                subject = f"Subscription Confirmation - {package.name}"
                context = {
                    'student': student,
                    'package': package,
                    'subscription': subscription,
                    'package_type': package.get_package_type_display(),
                }
                
                if package.package_type == 'event':
                    template = 'emails/event_subscription_confirmation.html'
                else:
                    template = 'emails/validity_subscription_confirmation.html'
                
                html_message = render_to_string(template, context)
                
                email = EmailMessage(
                    subject=subject,
                    body=html_message,
                    from_email=settings.DEFAULT_FROM_EMAIL,
                    to=[student.user.email]
                )
                email.content_subtype = "html"
                email.send()
                
                logger.info(f"Subscription confirmation email sent to {student.user.email}")
            except Exception as e:
                logger.error(f"Failed to send subscription confirmation email: {str(e)}")
        
        # Send email in background thread
        email_thread = threading.Thread(target=send_email)
        email_thread.start()
        
        # Send FCM notification
        try:
            fcm_devices = FCMDevice.objects.filter(user=student.user, is_active=True)
            for device in fcm_devices:
                message = messaging.Message(
                    notification=messaging.Notification(
                        title="🎉 Subscription Created!",
                        body=f"Your {package.name} subscription is being processed.",
                    ),
                    token=device.registration_token,
                    data={
                        "type": "subscription_created",
                        "package_name": package.name,
                        "package_type": package.package_type,
                        "subscription_id": str(subscription.id),
                    },
                )
                messaging.send(message)
        except Exception as e:
            logger.error(f"Failed to send FCM notification to student: {str(e)}")
    
    @staticmethod
    def _send_referrer_notification(student, subscription):
        """Send notification to referrer about new subscription"""
        try:
            # Find referral relationship
            referral = Referral.objects.filter(referred=student).first()
            if not referral:
                return
            
            referrer = referral.referrer
            
            # Check if this qualifies for scratch card reward
            is_first_subscription = not subscription.__class__.objects.filter(
                student=student, 
                is_active=True
            ).exclude(id=subscription.id).exists()
            
            if is_first_subscription:
                referrer_last_sub = subscription.__class__.objects.filter(
                    student=referrer, 
                    is_active=True
                ).last()
                
                if referrer_last_sub:
                    referrer_price = referrer_last_sub.final_price
                    referee_price = subscription.final_price
                    
                    if referee_price >= (referrer_price / 2):
                        # Create scratch card reward
                        reward_amount = min(100, int(referee_price * 0.1))
                        
                        scratch_card = ScratchCard.objects.create(
                            referrer=referrer,
                            referral=referral,
                            amount=reward_amount
                        )
                        
                        # Send FCM notification to referrer
                        fcm_devices = FCMDevice.objects.filter(user=referrer.user, is_active=True)
                        for device in fcm_devices:
                            message = messaging.Message(
                                notification=messaging.Notification(
                                    title="🎁 You got a Scratch Card!",
                                    body=f"Your friend {student.user.first_name} subscribed! Open your scratch card now.",
                                ),
                                token=device.registration_token,
                                data={
                                    "type": "scratch_card_granted",
                                    "amount": str(reward_amount),
                                    "referee_name": student.user.first_name,
                                },
                            )
                            messaging.send(message)
                        
                        # Send email to referrer
                        def send_referrer_email():
                            try:
                                subject = "🎁 You got a Scratch Card!"
                                context = {
                                    'referrer': referrer,
                                    'referee': student,
                                    'reward_amount': reward_amount,
                                    'package': subscription.package,
                                }
                                html_message = render_to_string('emails/referrer_scratch_card.html', context)
                                
                                email = EmailMessage(
                                    subject=subject,
                                    body=html_message,
                                    from_email=settings.DEFAULT_FROM_EMAIL,
                                    to=[referrer.user.email]
                                )
                                email.content_subtype = "html"
                                email.send()
                            except Exception as e:
                                logger.error(f"Failed to send referrer email: {str(e)}")
                        
                        email_thread = threading.Thread(target=send_referrer_email)
                        email_thread.start()
                        
        except Exception as e:
            logger.error(f"Failed to send referrer notification: {str(e)}")
    
    @staticmethod
    def _send_customer_care_notification(student, package, subscription):
        """Send notification to customer care team about new subscription"""
        try:
            # Get all customer care users
            care_profiles = CustomrcareProfile.objects.filter(account_status='active')
            
            for care_profile in care_profiles:
                # Send FCM notification
                fcm_devices = FCMDevice.objects.filter(user=care_profile.user, is_active=True)
                for device in fcm_devices:
                    message = messaging.Message(
                        notification=messaging.Notification(
                            title="📋 New Subscription",
                            body=f"{student.user.first_name} subscribed to {package.name}",
                        ),
                        token=device.registration_token,
                        data={
                            "type": "new_subscription",
                            "student_name": student.user.first_name,
                            "package_name": package.name,
                            "package_type": package.package_type,
                            "subscription_id": str(subscription.id),
                            "amount": str(subscription.final_price),
                        },
                    )
                    messaging.send(message)
            
            # Send summary email to customer care
            def send_care_email():
                try:
                    care_emails = [profile.user.email for profile in care_profiles]
                    if not care_emails:
                        return
                    
                    subject = f"New Subscription Alert - {package.name}"
                    context = {
                        'student': student,
                        'package': package,
                        'subscription': subscription,
                    }
                    html_message = render_to_string('emails/customer_care_subscription.html', context)
                    
                    email = EmailMessage(
                        subject=subject,
                        body=html_message,
                        from_email=settings.DEFAULT_FROM_EMAIL,
                        to=care_emails
                    )
                    email.content_subtype = "html"
                    email.send()
                except Exception as e:
                    logger.error(f"Failed to send customer care email: {str(e)}")
            
            email_thread = threading.Thread(target=send_care_email)
            email_thread.start()
            
        except Exception as e:
            logger.error(f"Failed to send customer care notification: {str(e)}")
    
    @staticmethod
    def _send_activation_email(student, package, subscription):
        """Send activation confirmation email"""
        def send_email():
            try:
                subject = f"🎉 Subscription Activated - {package.name}"
                context = {
                    'student': student,
                    'package': package,
                    'subscription': subscription,
                }
                
                template = 'emails/subscription_activation.html'
                html_message = render_to_string(template, context)
                
                email = EmailMessage(
                    subject=subject,
                    body=html_message,
                    from_email=settings.DEFAULT_FROM_EMAIL,
                    to=[student.user.email]
                )
                email.content_subtype = "html"
                email.send()
                
                logger.info(f"Subscription activation email sent to {student.user.email}")
            except Exception as e:
                logger.error(f"Failed to send activation email: {str(e)}")
        
        email_thread = threading.Thread(target=send_email)
        email_thread.start()
    
    @staticmethod
    def _send_activation_fcm(student, package):
        """Send activation FCM notification"""
        try:
            fcm_devices = FCMDevice.objects.filter(user=student.user, is_active=True)
            for device in fcm_devices:
                message = messaging.Message(
                    notification=messaging.Notification(
                        title="✅ Subscription Activated!",
                        body=f"Your {package.name} subscription is now active. Enjoy!",
                    ),
                    token=device.registration_token,
                    data={
                        "type": "subscription_activated",
                        "package_name": package.name,
                        "package_type": package.package_type,
                    },
                )
                messaging.send(message)
        except Exception as e:
            logger.error(f"Failed to send activation FCM: {str(e)}")
    
    @staticmethod
    def _send_expiry_warning_email(student, package, days_remaining):
        """Send expiry warning email"""
        def send_email():
            try:
                subject = f"⚠️ Subscription Expiring Soon - {package.name}"
                context = {
                    'student': student,
                    'package': package,
                    'days_remaining': days_remaining,
                }
                
                template = 'emails/subscription_expiry_warning.html'
                html_message = render_to_string(template, context)
                
                email = EmailMessage(
                    subject=subject,
                    body=html_message,
                    from_email=settings.DEFAULT_FROM_EMAIL,
                    to=[student.user.email]
                )
                email.content_subtype = "html"
                email.send()
                
                logger.info(f"Expiry warning email sent to {student.user.email}")
            except Exception as e:
                logger.error(f"Failed to send expiry warning email: {str(e)}")
        
        email_thread = threading.Thread(target=send_email)
        email_thread.start()
    
    @staticmethod
    def _send_expiry_warning_fcm(student, package, days_remaining):
        """Send expiry warning FCM notification"""
        try:
            fcm_devices = FCMDevice.objects.filter(user=student.user, is_active=True)
            for device in fcm_devices:
                message = messaging.Message(
                    notification=messaging.Notification(
                        title="⚠️ Subscription Expiring Soon",
                        body=f"Your {package.name} expires in {days_remaining} days. Renew now!",
                    ),
                    token=device.registration_token,
                    data={
                        "type": "subscription_expiring",
                        "package_name": package.name,
                        "days_remaining": str(days_remaining),
                    },
                )
                messaging.send(message)
        except Exception as e:
            logger.error(f"Failed to send expiry warning FCM: {str(e)}")
