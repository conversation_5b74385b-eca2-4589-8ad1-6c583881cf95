from django.db import models
from django.urls import reverse
from django.utils.text import slugify
from questions.utils import generate_unique_slug


class BlogPost(models.Model):
    title = models.CharField(max_length=255)
    slug = models.SlugField(max_length=255, unique=True, blank=True)
    author = models.ForeignKey(
        "contributor.ContributorProfile", on_delete=models.CASCADE
    )
    category = models.CharField(max_length=255, blank=True, null=True)
    tags = models.CharField(max_length=500, blank=True, null=True)
    views_counts = models.PositiveIntegerField(default=1)

    introduction = models.TextField(blank=True, null=True)
    content = models.TextField()
    short_content = models.TextField(blank=True, null=True)
    internal_link = models.URLField(blank=True, null=True)
    external_link = models.URLField(blank=True, null=True)
    published_date = models.DateTimeField(auto_now_add=True)
    updated_date = models.DateTimeField(auto_now=True)
    image = models.ImageField(upload_to="blog_images/", blank=True, null=True)
    image_caption = models.CharField(max_length=255, blank=True, null=True)
    approval_status = models.CharField(max_length=10, default="pending")
    created_at = models.DateTimeField(auto_now_add=True)
    updated_by_custmorcare = models.ForeignKey(
        "customrcare.CustomrcareProfile",
        on_delete=models.CASCADE,
        null=True,
        blank=True,
    )
    updated_at = models.DateTimeField(auto_now=True)
    blacklist_keywords = models.JSONField(default=list, blank=True)

    # SEO fields
    meta_title = models.CharField(max_length=255, blank=True, null=True)
    meta_description = models.TextField(blank=True, null=True)
    meta_keywords = models.CharField(max_length=255, blank=True, null=True)
    canonical_url = models.URLField(blank=True, null=True)
    open_graph = models.JSONField(blank=True, null=True)
    twitter_cards = models.JSONField(blank=True, null=True)
    breadcrumb_schema = models.JSONField(blank=True, null=True)
    article_schema = models.JSONField(blank=True, null=True)
    reason = models.CharField(max_length=500, null=True, blank=True)
    reason_document = models.ImageField(upload_to="reason/", null=True, blank=True)
    views = models.PositiveIntegerField(default=0)
    class Meta:
        ordering = ["-published_date"]
        verbose_name = "Blog Post"
        verbose_name_plural = "Blog Posts"
        app_label = "blogs"

    def __str__(self):
        return self.title

    def save(self, *args, **kwargs):
        if not self.slug:
            self.slug = generate_unique_slug(BlogPost, self.title)
        super().save(*args, **kwargs)

    def get_absolute_url(self):
        return reverse("blogpost-detail", kwargs={"slug": self.slug})
  
