"""
Django management command for automated log cleanup and retention management.
"""

from django.core.management.base import BaseCommand, CommandError
from django.utils import timezone
from datetime import timedelta
import os
from django.core import serializers
from django.conf import settings

from log_admin.models import (
    LogConfig, PerformanceLog, ErrorLog, UserActivity,
    APIAccessLog, DatabaseQueryLog, AuthenticationLog,
    SecurityIncident, SystemHealthLog
)


class Command(BaseCommand):
    help = 'Clean up old log entries based on retention policies'

    def add_arguments(self, parser):
        parser.add_argument('--dry-run', action='store_true', help='Preview without deleting')
        parser.add_argument('--archive', action='store_true', help='Archive before deletion')
        parser.add_argument('--force', action='store_true', help='Skip confirmations')
        parser.add_argument('--verbose', action='store_true', help='Verbose output')

    def handle(self, *args, **options):
        """Main command handler"""
        self.dry_run = options['dry_run']
        self.archive = options['archive']
        self.force = options['force']
        self.verbose = options['verbose']

        try:
            # Get configuration
            config = LogConfig.get_active_config()
            self.stdout.write(self.style.SUCCESS(f"Using configuration: {config.name}"))

            # Calculate cutoff dates
            now = timezone.now()
            general_cutoff = now - timedelta(days=config.log_retention_days)
            error_cutoff = now - timedelta(days=config.error_retention_days)
            security_cutoff = now - timedelta(days=config.security_retention_days)

            # Define log types and their configurations
            log_types_config = {
                'performance_logs': {'model': PerformanceLog, 'cutoff': general_cutoff, 'date_field': 'created_at'},
                'user_activities': {'model': UserActivity, 'cutoff': general_cutoff, 'date_field': 'timestamp'},
                'api_access_logs': {'model': APIAccessLog, 'cutoff': general_cutoff, 'date_field': 'timestamp'},
                'database_query_logs': {'model': DatabaseQueryLog, 'cutoff': general_cutoff, 'date_field': 'timestamp'},
                'system_health_logs': {'model': SystemHealthLog, 'cutoff': general_cutoff, 'date_field': 'timestamp'},
                'error_logs': {'model': ErrorLog, 'cutoff': error_cutoff, 'date_field': 'timestamp'},
                'authentication_logs': {'model': AuthenticationLog, 'cutoff': security_cutoff, 'date_field': 'timestamp'},
                'security_incidents': {'model': SecurityIncident, 'cutoff': security_cutoff, 'date_field': 'timestamp'}
            }

            # Preview cleanup
            self._preview_cleanup(log_types_config)

            # Confirm if not forced and not dry run
            if not self.force and not self.dry_run:
                confirm = input("\nProceed with cleanup? [y/N]: ")
                if confirm.lower() != 'y':
                    self.stdout.write("Cleanup cancelled.")
                    return

            # Execute cleanup
            results = self._execute_cleanup(log_types_config)
            self._display_results(results)

        except Exception as e:
            raise CommandError(f"Cleanup failed: {str(e)}")

    def _preview_cleanup(self, log_types_config):
        """Preview what will be cleaned up"""
        self.stdout.write("\n" + "="*60)
        self.stdout.write("CLEANUP PREVIEW")
        self.stdout.write("="*60)

        total_to_delete = 0
        
        for log_type, config_data in log_types_config.items():
            model = config_data['model']
            cutoff = config_data['cutoff']
            date_field = config_data['date_field']

            # Count logs to be deleted
            filter_kwargs = {f"{date_field}__lt": cutoff}
            count_to_delete = model.objects.filter(**filter_kwargs).count()
            total_count = model.objects.count()
            total_to_delete += count_to_delete

            # Display info
            status_color = self.style.WARNING if count_to_delete > 0 else self.style.SUCCESS
            self.stdout.write(
                f"{log_type.replace('_', ' ').title()}: "
                f"{status_color(f'{count_to_delete:,}')} / {total_count:,} entries"
            )

        self.stdout.write(f"\nTotal entries to delete: {self.style.WARNING(f'{total_to_delete:,}')}")
        
        if self.dry_run:
            self.stdout.write(self.style.NOTICE("DRY RUN - No actual deletion will occur"))
        if self.archive:
            self.stdout.write(self.style.NOTICE("ARCHIVE MODE - Logs will be archived before deletion"))

    def _execute_cleanup(self, log_types_config):
        """Execute the actual cleanup"""
        results = {'deleted_counts': {}, 'archived_counts': {}, 'errors': []}

        self.stdout.write("\n" + "="*60)
        self.stdout.write("EXECUTING CLEANUP")
        self.stdout.write("="*60)

        for log_type, config_data in log_types_config.items():
            try:
                model = config_data['model']
                cutoff = config_data['cutoff']
                date_field = config_data['date_field']

                # Build query
                filter_kwargs = {f"{date_field}__lt": cutoff}
                queryset = model.objects.filter(**filter_kwargs)
                count_to_delete = queryset.count()

                if count_to_delete > 0:
                    # Archive if requested
                    if self.archive and not self.dry_run:
                        archived_count = self._archive_logs(log_type, queryset)
                        results['archived_counts'][log_type] = archived_count

                    # Delete logs
                    if not self.dry_run:
                        deleted_count, _ = queryset.delete()
                        results['deleted_counts'][log_type] = deleted_count
                        self.stdout.write(self.style.SUCCESS(f"✓ Deleted {deleted_count:,} {log_type} entries"))
                    else:
                        results['deleted_counts'][log_type] = count_to_delete
                        self.stdout.write(self.style.NOTICE(f"[DRY RUN] Would delete {count_to_delete:,} {log_type} entries"))
                else:
                    results['deleted_counts'][log_type] = 0

            except Exception as e:
                error_msg = f"Error cleaning {log_type}: {str(e)}"
                results['errors'].append(error_msg)
                self.stdout.write(self.style.ERROR(f"✗ {error_msg}"))

        return results

    def _archive_logs(self, log_type, queryset):
        """Archive logs to JSON files before deletion"""
        try:
            # Create archive directory
            archive_dir = os.path.join(settings.BASE_DIR, 'log_archives')
            os.makedirs(archive_dir, exist_ok=True)

            # Create filename with timestamp
            timestamp = timezone.now().strftime('%Y%m%d_%H%M%S')
            filename = f"{log_type}_{timestamp}.json"
            filepath = os.path.join(archive_dir, filename)

            # Serialize and save logs
            serialized_data = serializers.serialize('json', queryset)
            with open(filepath, 'w') as f:
                f.write(serialized_data)

            return queryset.count()

        except Exception as e:
            self.stdout.write(self.style.ERROR(f"Archiving failed for {log_type}: {str(e)}"))
            return 0

    def _display_results(self, results):
        """Display cleanup results"""
        self.stdout.write("\n" + "="*60)
        self.stdout.write("CLEANUP RESULTS")
        self.stdout.write("="*60)

        total_deleted = sum(results['deleted_counts'].values())
        total_archived = sum(results['archived_counts'].values())

        if total_deleted > 0:
            self.stdout.write(self.style.SUCCESS(f"Total entries deleted: {total_deleted:,}"))

        if total_archived > 0:
            self.stdout.write(self.style.SUCCESS(f"Total entries archived: {total_archived:,}"))

        if results['errors']:
            self.stdout.write(self.style.ERROR(f"Errors encountered: {len(results['errors'])}"))
            for error in results['errors']:
                self.stdout.write(f"  - {error}")

        if total_deleted == 0 and total_archived == 0 and not results['errors']:
            self.stdout.write(self.style.SUCCESS("No cleanup needed - all logs within retention policy"))

        self.stdout.write("\nCleanup completed successfully!")
