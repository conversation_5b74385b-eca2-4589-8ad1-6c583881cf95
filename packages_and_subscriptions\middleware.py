"""
Access Control Middleware for Subscription-based Content
"""
import logging
from django.http import JsonResponse
from django.urls import resolve
from django.conf import settings
from students.models import Student

logger = logging.getLogger(__name__)


class SubscriptionAccessMiddleware:
    """
    Middleware to control access to content based on active subscriptions
    """
    
    def __init__(self, get_response):
        self.get_response = get_response
        
        # Define protected URL patterns and their required content IDs
        self.protected_patterns = {
            'api/practice-tests/': 'practice_tests',
            'api/mock-tests/': 'mock_tests',
            'api/video-solutions/': 'video_solutions',
            'api/performance-analysis/': 'performance_analysis',
            'api/peer-ranking/': 'peer_ranking',
            'api/jee-mock-tests/': 'jee_mock_tests',
            'api/premium-content/': 'premium_content',
        }
        
        # URLs that should always be accessible (free content)
        self.free_patterns = [
            'api/auth/',
            'api/packages/',
            'api/students/register/',
            'api/students/login/',
            'admin/',
            'static/',
            'media/',
        ]
    
    def __call__(self, request):
        # Check if this is a protected URL
        if self.is_protected_url(request.path):
            # Check user authentication
            if not request.user.is_authenticated:
                return JsonResponse({
                    'error': 'Authentication required',
                    'message': 'Please log in to access this content'
                }, status=401)
            
            # Get student object
            try:
                student = Student.objects.get(user=request.user)
            except Student.DoesNotExist:
                return JsonResponse({
                    'error': 'Student profile not found',
                    'message': 'Please complete your profile setup'
                }, status=403)
            
            # Check subscription access
            content_id = self.get_content_id(request.path)
            if not student.has_access_to_content(content_id):
                return JsonResponse({
                    'error': 'Subscription required',
                    'message': f'Active subscription required to access this content',
                    'content_id': content_id,
                    'current_status': student.current_subscription_status,
                    'upgrade_url': '/api/packages/'
                }, status=403)
            
            # Log access for analytics
            logger.info(f"Content access granted: student={student.id}, content={content_id}, path={request.path}")
        
        response = self.get_response(request)
        return response
    
    def is_protected_url(self, path):
        """Check if URL requires subscription access"""
        # Check if it's a free URL first
        for free_pattern in self.free_patterns:
            if free_pattern in path:
                return False
        
        # Check if it matches any protected pattern
        for protected_pattern in self.protected_patterns.keys():
            if protected_pattern in path:
                return True
        
        return False
    
    def get_content_id(self, path):
        """Get content ID for the given path"""
        for pattern, content_id in self.protected_patterns.items():
            if pattern in path:
                return content_id
        return None


class SubscriptionLoggingMiddleware:
    """
    Middleware to log all subscription-related activities
    """
    
    def __init__(self, get_response):
        self.get_response = get_response
    
    def __call__(self, request):
        # Log subscription-related requests
        if any(pattern in request.path for pattern in [
            '/api/packages/',
            '/api/subscriptions/',
            '/verify-payment/',
            '/create-subscription/'
        ]):
            logger.info(f"Subscription request: {request.method} {request.path} from {request.META.get('REMOTE_ADDR')}")
            
            if request.user.is_authenticated:
                try:
                    student = Student.objects.get(user=request.user)
                    logger.info(f"Request from student: {student.id} ({student.user.first_name} {student.user.last_name})")
                except Student.DoesNotExist:
                    logger.warning(f"Request from user without student profile: {request.user.id}")
        
        response = self.get_response(request)
        
        # Log response status for subscription requests
        if any(pattern in request.path for pattern in [
            '/api/packages/',
            '/api/subscriptions/',
            '/verify-payment/',
            '/create-subscription/'
        ]):
            logger.info(f"Subscription response: {response.status_code} for {request.method} {request.path}")
        
        return response


# Decorator for view-based access control
def subscription_required(content_id=None):
    """
    Decorator to require active subscription for view access
    
    Usage:
    @subscription_required('premium_content')
    def my_premium_view(request):
        # View logic here
    """
    def decorator(view_func):
        def wrapper(request, *args, **kwargs):
            if not request.user.is_authenticated:
                return JsonResponse({
                    'error': 'Authentication required'
                }, status=401)
            
            try:
                student = Student.objects.get(user=request.user)
            except Student.DoesNotExist:
                return JsonResponse({
                    'error': 'Student profile not found'
                }, status=403)
            
            if not student.has_access_to_content(content_id):
                return JsonResponse({
                    'error': 'Subscription required',
                    'content_id': content_id,
                    'current_status': student.current_subscription_status
                }, status=403)
            
            return view_func(request, *args, **kwargs)
        return wrapper
    return decorator


# Utility functions for access control
def check_content_access(user, content_id):
    """
    Utility function to check if user has access to specific content
    
    Args:
        user: Django User object
        content_id: String identifier for the content
    
    Returns:
        dict: Access information including status and details
    """
    if not user.is_authenticated:
        return {
            'has_access': False,
            'reason': 'not_authenticated',
            'message': 'User not authenticated'
        }
    
    try:
        student = Student.objects.get(user=user)
    except Student.DoesNotExist:
        return {
            'has_access': False,
            'reason': 'no_student_profile',
            'message': 'Student profile not found'
        }
    
    has_access = student.has_access_to_content(content_id)
    
    return {
        'has_access': has_access,
        'reason': 'subscription_required' if not has_access else 'access_granted',
        'current_status': student.current_subscription_status,
        'active_subscriptions': student.active_subscriptions.count(),
        'validity_subscriptions': student.validity_subscriptions.count(),
        'event_subscriptions': student.event_subscriptions.count()
    }


def get_user_subscription_summary(user):
    """
    Get comprehensive subscription summary for a user
    
    Args:
        user: Django User object
    
    Returns:
        dict: Comprehensive subscription information
    """
    if not user.is_authenticated:
        return {'error': 'User not authenticated'}
    
    try:
        student = Student.objects.get(user=user)
    except Student.DoesNotExist:
        return {'error': 'Student profile not found'}
    
    active_subscriptions = student.active_subscriptions
    
    summary = {
        'student_id': student.id,
        'current_status': student.current_subscription_status,
        'total_active_subscriptions': active_subscriptions.count(),
        'validity_subscriptions': student.validity_subscriptions.count(),
        'event_subscriptions': student.event_subscriptions.count(),
        'subscriptions': []
    }
    
    for subscription in active_subscriptions:
        sub_info = {
            'id': subscription.id,
            'package_name': subscription.package.name,
            'package_type': subscription.package.package_type,
            'start_date': subscription.start_date.isoformat() if subscription.start_date else None,
            'end_date': subscription.end_date.isoformat() if subscription.end_date else None,
            'is_valid': subscription.is_valid_subscription(),
            'final_price': float(subscription.final_price),
            'is_active': subscription.is_active
        }
        summary['subscriptions'].append(sub_info)
    
    return summary
