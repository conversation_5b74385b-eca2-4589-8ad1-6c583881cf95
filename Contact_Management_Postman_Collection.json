{"info": {"name": "Contact Management API - Streamlined", "description": "Complete API collection for the streamlined contact management system", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "variable": [{"key": "base_url", "value": "http://localhost:8000", "type": "string"}, {"key": "jwt_token", "value": "", "type": "string"}], "item": [{"name": "Authentication", "item": [{"name": "Student Login", "event": [{"listen": "test", "script": {"exec": ["if (pm.response.code === 200) {", "    const jsonData = pm.response.json();", "    if (jsonData.JWT_Token && jsonData.JWT_Token.access) {", "        pm.environment.set('jwt_token', jsonData.JWT_Token.access);", "        console.log('JWT token saved to environment');", "    }", "}"]}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"username\": \"your_username\",\n  \"password\": \"your_password\"\n}"}, "url": {"raw": "{{base_url}}/api/students/login/", "host": ["{{base_url}}"], "path": ["api", "students", "login", ""]}}}]}, {"name": "Contact Management", "item": [{"name": "1. <PERSON><PERSON> <PERSON><PERSON>", "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 201', function () {", "    pm.response.to.have.status(201);", "});", "", "pm.test('Response has success field', function () {", "    const jsonData = pm.response.json();", "    pm.expect(jsonData).to.have.property('success', true);", "});", "", "pm.test('Response has data field', function () {", "    const jsonData = pm.response.json();", "    pm.expect(jsonData).to.have.property('data');", "    pm.expect(jsonData.data).to.have.property('created');", "    pm.expect(jsonData.data).to.have.property('matched');", "});"]}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{jwt_token}}"}], "body": {"mode": "raw", "raw": "{\n  \"contacts\": [\n    {\n      \"name\": \"<PERSON>\",\n      \"contact\": \"9876543210\"\n    },\n    {\n      \"name\": \"<PERSON>\",\n      \"contact\": \"+91-9876543211\"\n    },\n    {\n      \"name\": \"<PERSON>\",\n      \"contact\": \"91 9876 543 212\"\n    },\n    {\n      \"name\": \"<PERSON>\",\n      \"contact\": \"9876543213\"\n    },\n    {\n      \"name\": \"<PERSON>\",\n      \"contact\": \"+919876543214\"\n    }\n  ]\n}"}, "url": {"raw": "{{base_url}}/api/contacts/sync/", "host": ["{{base_url}}"], "path": ["api", "contacts", "sync", ""]}}}, {"name": "2. Get My Contacts", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{jwt_token}}"}], "url": {"raw": "{{base_url}}/api/contacts/my-contacts/", "host": ["{{base_url}}"], "path": ["api", "contacts", "my-contacts", ""]}}}, {"name": "3. Get Matched Contacts (Who's on App)", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{jwt_token}}"}], "url": {"raw": "{{base_url}}/api/contacts/matched/", "host": ["{{base_url}}"], "path": ["api", "contacts", "matched", ""]}}}, {"name": "4. Get Contact Statistics", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{jwt_token}}"}], "url": {"raw": "{{base_url}}/api/contacts/stats/", "host": ["{{base_url}}"], "path": ["api", "contacts", "stats", ""]}}}, {"name": "5. Search Contacts by Name", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{jwt_token}}"}], "url": {"raw": "{{base_url}}/api/contacts/search/?query=John", "host": ["{{base_url}}"], "path": ["api", "contacts", "search", ""], "query": [{"key": "query", "value": "<PERSON>"}]}}}, {"name": "6. Search by Phone Number", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{jwt_token}}"}], "url": {"raw": "{{base_url}}/api/contacts/search/?contact_number=9876543210", "host": ["{{base_url}}"], "path": ["api", "contacts", "search", ""], "query": [{"key": "contact_number", "value": "9876543210"}]}}}, {"name": "7. Search Matched Contacts Only", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{jwt_token}}"}], "url": {"raw": "{{base_url}}/api/contacts/search/?is_matched=true", "host": ["{{base_url}}"], "path": ["api", "contacts", "search", ""], "query": [{"key": "is_matched", "value": "true"}]}}}]}, {"name": "Admin Endpoints", "item": [{"name": "Admin - View All Contacts", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{jwt_token}}"}], "url": {"raw": "{{base_url}}/api/contacts/admin/contacts/", "host": ["{{base_url}}"], "path": ["api", "contacts", "admin", "contacts", ""]}}}, {"name": "Admin - Search All Contacts", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{jwt_token}}"}], "url": {"raw": "{{base_url}}/api/contacts/admin/contacts/?search=John", "host": ["{{base_url}}"], "path": ["api", "contacts", "admin", "contacts", ""], "query": [{"key": "search", "value": "<PERSON>"}]}}}]}, {"name": "Error Testing", "item": [{"name": "Test Without Auth (Should get 401/403)", "request": {"method": "GET", "url": {"raw": "{{base_url}}/api/contacts/my-contacts/", "host": ["{{base_url}}"], "path": ["api", "contacts", "my-contacts", ""]}}}, {"name": "Test Invalid Contact Data", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{jwt_token}}"}], "body": {"mode": "raw", "raw": "{\n  \"contacts\": [\n    {\n      \"name\": \"Invalid Contact\",\n      \"contact\": \"123\"\n    }\n  ]\n}"}, "url": {"raw": "{{base_url}}/api/contacts/sync/", "host": ["{{base_url}}"], "path": ["api", "contacts", "sync", ""]}}}, {"name": "Test Empty Contacts List", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{jwt_token}}"}], "body": {"mode": "raw", "raw": "{\n  \"contacts\": []\n}"}, "url": {"raw": "{{base_url}}/api/contacts/sync/", "host": ["{{base_url}}"], "path": ["api", "contacts", "sync", ""]}}}]}]}