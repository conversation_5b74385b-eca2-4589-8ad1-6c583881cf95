from django.urls import path, include
from .views import FCMDeviceListCreateAPIView, FCMDeviceDetailAPIView,send_test_notification, FCMDeviceView, NotificationViewSet, notification_image 
from rest_framework.routers import DefaultRouter
router = DefaultRouter()

router.register(r'menu-notifications', NotificationViewSet, basename='menu-notification')

urlpatterns = [
    path('devices/', FCMDeviceListCreateAPIView.as_view(), name='fcm-device-list-create'),
    path('devices/<int:pk>/', FCMDeviceDetailAPIView.as_view(), name='fcm-device-detail'),
    path("fcm-device/", FCMDeviceView.as_view(), name="fcm-device"),

    # Notifications
    path('send-test-notification/', send_test_notification, name='send-test-notification'),
    path('notification-image/', notification_image, name='notification-image'),

    # DRF ViewSet routing
    path('', include(router.urls)),  # menu-notifications → menu-notification-list/detail
]

