# Python bytecode
__pycache__/
*.py[cod]
*$py.class

# Django
*.log
*.pot
*.pyc
db.sqlite3
test_db.sqlite3
media/
migrations/

# Distribution / packaging
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg

# Virtual environments
venv/
ENV/
env/
.env
.venv
env.bak/
venv.bak/

# IDE specific files
.idea/
.vscode/
*.swp
*.swo
.DS_Store

# Generated by Windows
Thumbs.db

# Applications
*.app
*.exe
*.war

# Large media files

*.mp4
*.tiff
*.avi
*.flv
*.mov
*.wmv
*.pyc
*.sqlite3
# Environment files
.venv
myenv/
.myenv
.env.local
.env.*.local
venv/
Lib/
*/migrations/*
shashtrarth/settings.py

staticfiles/