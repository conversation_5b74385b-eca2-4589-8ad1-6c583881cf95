<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Frontend Error Logging Demo</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .demo-section {
            margin: 30px 0;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
            background: #f9f9f9;
        }
        .demo-button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            margin: 10px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 14px;
        }
        .demo-button:hover {
            background: #0056b3;
        }
        .error-button {
            background: #dc3545;
        }
        .error-button:hover {
            background: #c82333;
        }
        .warning-button {
            background: #ffc107;
            color: #212529;
        }
        .warning-button:hover {
            background: #e0a800;
        }
        .success-button {
            background: #28a745;
        }
        .success-button:hover {
            background: #218838;
        }
        .log-output {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 15px;
            margin-top: 15px;
            font-family: monospace;
            font-size: 12px;
            max-height: 200px;
            overflow-y: auto;
        }
        .status-indicator {
            display: inline-block;
            width: 10px;
            height: 10px;
            border-radius: 50%;
            margin-right: 8px;
        }
        .status-ok { background: #28a745; }
        .status-warning { background: #ffc107; }
        .status-error { background: #dc3545; }
        h1, h2 { color: #333; }
        .csrf-token { display: none; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🚀 Frontend Error Logging System Demo</h1>
        <p>This demo showcases the comprehensive frontend error logging system with automatic error detection, categorization, and reporting.</p>
        
        <!-- CSRF Token for Django -->
        <div class="csrf-token">
            {% csrf_token %}
        </div>
        
        <!-- Status Section -->
        <div class="demo-section">
            <h2>📊 System Status</h2>
            <p><span class="status-indicator status-ok"></span>Error Logger: <span id="logger-status">Initializing...</span></p>
            <p><span class="status-indicator status-ok"></span>Error Count: <span id="error-count">0</span></p>
            <p><span class="status-indicator status-ok"></span>Last Error: <span id="last-error">None</span></p>
        </div>
        
        <!-- Manual Error Testing -->
        <div class="demo-section">
            <h2>🧪 Manual Error Testing</h2>
            <p>Test different types of errors and see how they're automatically categorized:</p>
            
            <button class="demo-button error-button" onclick="triggerJavaScriptError()">
                JavaScript Error
            </button>
            <button class="demo-button error-button" onclick="triggerNetworkError()">
                Network Error
            </button>
            <button class="demo-button error-button" onclick="triggerValidationError()">
                Validation Error
            </button>
            <button class="demo-button warning-button" onclick="triggerWarning()">
                Warning
            </button>
            <button class="demo-button" onclick="triggerCustomError()">
                Custom Error
            </button>
        </div>
        
        <!-- User Action Tracking -->
        <div class="demo-section">
            <h2>👆 User Action Tracking</h2>
            <p>All user interactions are automatically tracked. Try these actions:</p>
            
            <button class="demo-button" onclick="trackableAction('button_click')">
                Trackable Button
            </button>
            <input type="text" placeholder="Type something..." onchange="trackableAction('input_change')" style="margin: 10px; padding: 8px;">
            <form onsubmit="trackableAction('form_submit'); return false;" style="display: inline;">
                <button type="submit" class="demo-button success-button">Submit Form</button>
            </form>
        </div>
        
        <!-- Error Analytics -->
        <div class="demo-section">
            <h2>📈 Error Analytics</h2>
            <button class="demo-button" onclick="loadErrorAnalytics()">
                Load Error Analytics
            </button>
            <button class="demo-button" onclick="loadRecentErrors()">
                Load Recent Errors
            </button>
            <div id="analytics-output" class="log-output" style="display: none;">
                Analytics data will appear here...
            </div>
        </div>
        
        <!-- Console Output -->
        <div class="demo-section">
            <h2>📝 Console Output</h2>
            <p>Real-time logging output:</p>
            <div id="console-output" class="log-output">
                Console output will appear here...
            </div>
            <button class="demo-button" onclick="clearConsole()">Clear Console</button>
        </div>
    </div>

    <!-- Include the Error Logger -->
    <script src="/static/js/error-logger.js"></script>
    
    <script>
        // Initialize the error logging system
        document.addEventListener('DOMContentLoaded', function() {
            // Initialize ErrorLogger
            ErrorLogger.init({
                apiEndpoint: '/api/customrcare/log-error/',
                enableConsoleCapture: true,
                enableUserActionTracking: true,
                enableNetworkErrorTracking: true,
                enableUnhandledRejectionTracking: true,
                maxUserActions: 20,
                maxConsoleMessages: 30
            });
            
            updateStatus('Logger initialized successfully', 'ok');
            logToConsole('✅ Frontend Error Logger initialized');
            
            // Update error count periodically
            setInterval(updateErrorCount, 5000);
        });
        
        let errorCount = 0;
        
        function updateStatus(message, type) {
            document.getElementById('logger-status').textContent = message;
            const indicator = document.querySelector('.status-indicator');
            indicator.className = `status-indicator status-${type}`;
        }
        
        function updateErrorCount() {
            document.getElementById('error-count').textContent = errorCount;
        }
        
        function logToConsole(message) {
            const output = document.getElementById('console-output');
            const timestamp = new Date().toLocaleTimeString();
            output.innerHTML += `[${timestamp}] ${message}\n`;
            output.scrollTop = output.scrollHeight;
        }
        
        function clearConsole() {
            document.getElementById('console-output').innerHTML = '';
        }
        
        // Error testing functions
        function triggerJavaScriptError() {
            try {
                // This will cause a ReferenceError
                nonExistentFunction();
            } catch (error) {
                ErrorLogger.logError(
                    'Intentional JavaScript error for testing',
                    {
                        test_type: 'manual_trigger',
                        original_error: error.message
                    },
                    'MEDIUM'
                );
                errorCount++;
                updateErrorCount();
                document.getElementById('last-error').textContent = 'JavaScript Error';
                logToConsole('🔴 JavaScript error triggered and logged');
            }
        }
        
        function triggerNetworkError() {
            fetch('/api/nonexistent-endpoint')
                .catch(error => {
                    ErrorLogger.logError(
                        'Network request failed: ' + error.message,
                        {
                            test_type: 'network_error',
                            endpoint: '/api/nonexistent-endpoint'
                        },
                        'HIGH'
                    );
                    errorCount++;
                    updateErrorCount();
                    document.getElementById('last-error').textContent = 'Network Error';
                    logToConsole('🔴 Network error triggered and logged');
                });
        }
        
        function triggerValidationError() {
            ErrorLogger.logError(
                'Validation failed: Required field is missing',
                {
                    test_type: 'validation_error',
                    field: 'email',
                    validation_rule: 'required'
                },
                'LOW'
            );
            errorCount++;
            updateErrorCount();
            document.getElementById('last-error').textContent = 'Validation Error';
            logToConsole('🟡 Validation error triggered and logged');
        }
        
        function triggerWarning() {
            console.warn('This is a test warning message');
            ErrorLogger.logError(
                'Warning: Deprecated function used',
                {
                    test_type: 'warning',
                    function_name: 'deprecatedFunction'
                },
                'LOW'
            );
            errorCount++;
            updateErrorCount();
            document.getElementById('last-error').textContent = 'Warning';
            logToConsole('🟠 Warning triggered and logged');
        }
        
        function triggerCustomError() {
            ErrorLogger.logError(
                'Custom error message for testing purposes',
                {
                    test_type: 'custom_error',
                    custom_field: 'custom_value',
                    timestamp: new Date().toISOString()
                },
                'MEDIUM'
            );
            errorCount++;
            updateErrorCount();
            document.getElementById('last-error').textContent = 'Custom Error';
            logToConsole('🔵 Custom error triggered and logged');
        }
        
        function trackableAction(actionType) {
            logToConsole(`👆 User action tracked: ${actionType}`);
        }
        
        // Analytics functions
        function loadErrorAnalytics() {
            const output = document.getElementById('analytics-output');
            output.style.display = 'block';
            output.innerHTML = 'Loading analytics...';
            
            fetch('/api/customrcare/error-analytics/?days=7')
                .then(response => response.json())
                .then(data => {
                    output.innerHTML = JSON.stringify(data, null, 2);
                    logToConsole('📊 Error analytics loaded');
                })
                .catch(error => {
                    output.innerHTML = 'Error loading analytics: ' + error.message;
                    logToConsole('❌ Failed to load analytics');
                });
        }
        
        function loadRecentErrors() {
            const output = document.getElementById('analytics-output');
            output.style.display = 'block';
            output.innerHTML = 'Loading recent errors...';
            
            fetch('/api/customrcare/get-errors/?limit=10')
                .then(response => response.json())
                .then(data => {
                    output.innerHTML = JSON.stringify(data, null, 2);
                    logToConsole('📋 Recent errors loaded');
                })
                .catch(error => {
                    output.innerHTML = 'Error loading recent errors: ' + error.message;
                    logToConsole('❌ Failed to load recent errors');
                });
        }
        
        // Simulate some user actions for demonstration
        setTimeout(() => {
            logToConsole('🎯 Demo ready! Try the buttons above to test error logging.');
        }, 1000);
    </script>
</body>
</html>
