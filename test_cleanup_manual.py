#!/usr/bin/env python3
"""
Manual test for cleanup functionality with real old logs
"""

import os
import sys
import django

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'shashtrarth.settings')
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

django.setup()

from log_admin.models import PerformanceLog, ErrorLog, LogConfig
from django.contrib.auth.models import User
from django.utils import timezone
from datetime import timedelta
from rest_framework.test import APIClient

def main():
    print("🧪 Manual Cleanup Test")
    print("="*50)
    
    # Get or create admin user
    admin_user, created = User.objects.get_or_create(
        username='cleanup_test_admin',
        defaults={
            'email': '<EMAIL>',
            'is_superuser': True,
            'is_staff': True
        }
    )
    if created:
        admin_user.set_password('testpass123')
        admin_user.save()
        print("✅ Created admin user")
    else:
        print("✅ Using existing admin user")
    
    # Create old logs manually
    now = timezone.now()
    old_date = now - timedelta(days=35)  # Older than 30 day retention
    very_old_date = now - timedelta(days=100)  # Very old
    
    # Create old performance logs (need to update timestamps after creation due to auto_now_add)
    old_perf_log = PerformanceLog.objects.create(
        path='/test/old/performance',
        method='GET',
        duration=1.5,
        status_code=200,
        user=admin_user
    )
    # Update timestamp manually (auto_now_add prevents setting it during creation)
    PerformanceLog.objects.filter(id=old_perf_log.id).update(created_at=old_date)
    old_perf_log.refresh_from_db()

    very_old_perf_log = PerformanceLog.objects.create(
        path='/test/very-old/performance',
        method='GET',
        duration=2.0,
        status_code=200,
        user=admin_user
    )
    # Update timestamp manually
    PerformanceLog.objects.filter(id=very_old_perf_log.id).update(created_at=very_old_date)
    very_old_perf_log.refresh_from_db()

    # Create old error log (should not be cleaned up due to 90 day retention)
    old_error_log = ErrorLog.objects.create(
        view_name='test_view',
        error_type='BUSINESS_LOGIC',
        error_message='Old test error',
        severity='MEDIUM',
        user=admin_user
    )
    # Update timestamp manually
    ErrorLog.objects.filter(id=old_error_log.id).update(timestamp=old_date)
    old_error_log.refresh_from_db()
    
    print(f"✅ Created test logs:")
    print(f"   - Performance log from {old_perf_log.created_at} ({(now - old_perf_log.created_at).days} days ago)")
    print(f"   - Very old performance log from {very_old_perf_log.created_at} ({(now - very_old_perf_log.created_at).days} days ago)")
    print(f"   - Error log from {old_error_log.timestamp} ({(now - old_error_log.timestamp).days} days ago)")
    print()
    
    # Check current status
    config = LogConfig.get_active_config()
    general_cutoff = now - timedelta(days=config.log_retention_days)
    error_cutoff = now - timedelta(days=config.error_retention_days)
    
    old_perf_count = PerformanceLog.objects.filter(created_at__lt=general_cutoff).count()
    old_error_count = ErrorLog.objects.filter(timestamp__lt=error_cutoff).count()
    
    print(f"📊 Current Status:")
    print(f"   - Performance logs older than {config.log_retention_days} days: {old_perf_count}")
    print(f"   - Error logs older than {config.error_retention_days} days: {old_error_count}")
    print()
    
    # Test cleanup API
    client = APIClient()
    client.force_authenticate(user=admin_user)
    
    # Test cleanup preview
    print("🔍 Testing cleanup preview...")
    response = client.get('/api/log-admin/cleanup/')
    
    if response.status_code == 200:
        data = response.json()
        total_to_delete = data.get('total_to_delete', 0)
        print(f"✅ Cleanup preview successful")
        print(f"   - Total logs to delete: {total_to_delete}")
        
        if total_to_delete > 0:
            print("✅ Old logs identified for cleanup")
        else:
            print("❌ No old logs identified for cleanup")
    else:
        print(f"❌ Cleanup preview failed: {response.status_code}")
    print()
    
    # Test dry run cleanup
    print("🧪 Testing dry run cleanup...")
    response = client.post('/api/log-admin/cleanup/', {
        'dry_run': True
    })
    
    if response.status_code == 200:
        data = response.json()
        print(f"✅ Dry run successful")
        print(f"   - Total would be deleted: {data.get('total_deleted', 0)}")
        
        # Check if logs still exist (they should in dry run)
        if PerformanceLog.objects.filter(id=old_perf_log.id).exists():
            print("✅ Logs preserved in dry run")
        else:
            print("❌ Logs were deleted in dry run")
    else:
        print(f"❌ Dry run failed: {response.status_code}")
    print()
    
    # Test actual cleanup
    print("🗑️  Testing actual cleanup...")
    response = client.post('/api/log-admin/cleanup/', {
        'dry_run': False
    })
    
    if response.status_code == 200:
        data = response.json()
        print(f"✅ Cleanup successful")
        print(f"   - Total deleted: {data.get('total_deleted', 0)}")
        
        # Check if old performance logs were deleted
        if not PerformanceLog.objects.filter(id=old_perf_log.id).exists():
            print("✅ Old performance log deleted")
        else:
            print("❌ Old performance log not deleted")

        if not PerformanceLog.objects.filter(id=very_old_perf_log.id).exists():
            print("✅ Very old performance log deleted")
        else:
            print("❌ Very old performance log not deleted")
        
        # Check if error log still exists (should due to longer retention)
        if ErrorLog.objects.filter(id=old_error_log.id).exists():
            print("✅ Error log preserved (longer retention)")
        else:
            print("❌ Error log deleted (should be preserved)")
    else:
        print(f"❌ Cleanup failed: {response.status_code}")
    print()
    
    print("🎯 Manual cleanup test completed!")

if __name__ == '__main__':
    main()
