from rest_framework.response import Response
from rest_framework.views import APIView
from django.shortcuts import get_object_or_404
from rest_framework.permissions import AllowAny, IsAuthenticated
from contributor.models import ContributorProfile
from contributor.permissions import IsContributorUser
from .models import (
    PreviousYearQuestion,
    Question,
    Option,
    Course,
    SubCourse,
    Tier,
    Paper,
    Section,
    Module,
    Subject,
    Topic,
    SubTopic,
    MasterQuestion,
    MasterOption,
)
from .serializers import (
    PreviousYearQuestionSerializer,
    QuestionSerializer,
    OptionSerializer,
    CourseSerializer,
    SubCourseSerializer,
    SubjectSerializer,
    TopicSerializer,
    SubTopicSerializer,
    MasterQuestionSerializer,
    MasterOptionSerializer,
    TierSerializer,
    PaperSerializer,
    SectionSerializer,
    ModuleSerializer,
)
from rest_framework import status
from django.db.models import Q
from blogs.models import BlogPost
import json
class QuestionListView(APIView):
    permission_classes = (IsContributorUser,)

    def get(self, request, slug=""):
        if slug != "":
            try:
                question = Question.objects.get(slug=slug)
                serializer = QuestionSerializer(question)
                return Response(serializer.data)
            except Question.DoesNotExist:
                return Response(
                    {"error": "Question not found."}, status=status.HTTP_404_NOT_FOUND
                )
        contributor_profile = ContributorProfile.objects.get(user=request.user)
        questions = Question.objects.filter(author=contributor_profile)[::-1]
        serializer = QuestionSerializer(questions, many=True)
        return Response(serializer.data)

    def post(self, request, slug=None):
        if request.data.get("is_current_affairs"):
            slug = request.data["current_affairs"]
            request.data['current_affairs'] = BlogPost.objects.get(slug=slug).id
     

        if request.data.get("is_master"):
            slug = request.data["master_question"]
            request.data['master_question'] = MasterQuestion.objects.get(slug=slug).master_question_id
     

        if request.data.get("is_master_option"):
            slug = request.data["master_option"]
            request.data['master_option'] = MasterOption.objects.get(slug=slug).master_option_id
       
        serializer = QuestionSerializer(data=request.data)

        if serializer.is_valid():
            question = serializer.save()
        
            # Handle many-to-many relationships
            return Response(serializer.data, status=status.HTTP_201_CREATED)
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

    def patch(self, request, slug):
        try:
            question = Question.objects.get(slug=slug)
        except Question.DoesNotExist:
            return Response(
                {"error": "Question not found."}, status=status.HTTP_404_NOT_FOUND
            )

        # # Ensure the course_id, subcourse_id, and master_question_id are passed in request data for updating
        # course_id = request.data.get("course_id")
        # subcourse_id = request.data.get("subcourse_id")
        # master_question_id = request.data.get("master_question_id")

        # if course_id:
        #     try:
        #         course = Course.objects.get(pk=course_id)
        #         request.data["course"] = course
        #     except Course.DoesNotExist:
        #         return Response(
        #             {"error": "Course not found."}, status=status.HTTP_400_BAD_REQUEST
        #         )

        # if subcourse_id:
        #     try:
        #         subcourse = SubCourse.objects.get(pk=subcourse_id)
        #         request.data["subcourse"] = subcourse
        #     except SubCourse.DoesNotExist:
        #         return Response(
        #             {"error": "SubCourse not found."},
        #             status=status.HTTP_400_BAD_REQUEST,
        #         )

        # if master_question_id:
        #     try:
        #         master_question = MasterQuestion.objects.get(pk=master_question_id)
        #         request.data["master_question"] = master_question
        #     except MasterQuestion.DoesNotExist:
        #         return Response(
        #             {"error": "MasterQuestion not found."},
        #             status=status.HTTP_400_BAD_REQUEST,
        #         )

        serializer = QuestionSerializer(question, data=request.data)
        if serializer.is_valid():
            serializer.save()
            return Response(serializer.data)
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

    def delete(self, request, slug):
        try:
            question = Question.objects.get(slug=slug)
        except Question.DoesNotExist:
            return Response(
                {"error": "Question not found."}, status=status.HTTP_404_NOT_FOUND
            )

        question.delete()
        return Response(
            {"success": "Question deleted successfully."}, status=status.HTTP_200_OK
        )


class CourseListView(APIView):

    def get(self, request):
        courses = Course.objects.all().order_by('-created_date')
        serializer = CourseSerializer(courses, many=True)
        return Response(serializer.data)

    def post(self, request):
        print(request.data)
        serializer = CourseSerializer(data=request.data)
        if serializer.is_valid():
            serializer.save()
            return Response(serializer.data, status=status.HTTP_201_CREATED)
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)


class CourseDetailView(APIView):

    def get(self, request, slug):
        try:
            course = Course.objects.get(slug=slug)
            serializer = CourseSerializer(course)
            return Response(serializer.data)
        except Course.DoesNotExist:
            return Response(
                {"error": "Course not found."}, status=status.HTTP_404_NOT_FOUND
            )

    def put(self, request, slug):
        try:
            course = Course.objects.get(slug=slug)
        except Course.DoesNotExist:
            return Response(
                {"error": "Course not found."}, status=status.HTTP_404_NOT_FOUND
            )

        serializer = CourseSerializer(course, data=request.data)
        if serializer.is_valid():
            serializer.save()
            return Response(serializer.data)
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

    def delete(self, request, slug):
        try:
            course = Course.objects.get(slug=slug)
        except Course.DoesNotExist:
            return Response(
                {"error": "Course not found."}, status=status.HTTP_404_NOT_FOUND
            )

        course.delete()
        return Response(
            {"success": "Course deleted successfully."}, status=status.HTTP_200_OK
        )


class SubCourseListView(APIView):

    def get(self, request, slug=None):
        if slug:
            subcourses = SubCourse.objects.filter(slug=slug)
        else:
            subcourses = SubCourse.objects.all().order_by('-created_date')
        serializer = SubCourseSerializer(subcourses, many=True)
        return Response(serializer.data)

    def post(self, request, slug=None):
        slug_course = request.data["course"]
        # breakpoint()
        try:
            course = Course.objects.get(slug=slug_course)
        except Course.DoesNotExist:
            return Response(
                {"error": "Course not found."}, status=status.HTTP_404_NOT_FOUND
            )

        request.data["course"] = course.course_id
        serializer = SubCourseSerializer(data=request.data)
        if serializer.is_valid():
            serializer.save()
            return Response(serializer.data, status=status.HTTP_201_CREATED)
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)


class SubCourseDetailView(APIView):
    def get(self, request, slug):
        try:
            subcourse = SubCourse.objects.get(slug=slug)
            serializer = SubCourseSerializer(subcourse)
            return Response(serializer.data)
        except SubCourse.DoesNotExist:
            return Response(
                {"error": "SubCourse not found."}, status=status.HTTP_404_NOT_FOUND
            )

    def put(self, request, slug):
        try:
            subcourse = SubCourse.objects.get(slug=slug)
        except SubCourse.DoesNotExist:
            return Response(
                {"error": "SubCourse not found."}, status=status.HTTP_404_NOT_FOUND
            )

        serializer = SubCourseSerializer(subcourse, data=request.data)
        if serializer.is_valid():
            serializer.save()
            return Response(serializer.data)
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

    def delete(self, request, slug):
        try:
            subcourse = SubCourse.objects.get(slug=slug)
        except SubCourse.DoesNotExist:
            return Response(
                {"error": "SubCourse not found."}, status=status.HTTP_404_NOT_FOUND
            )

        subcourse.delete()
        return Response(
            {"success": "SubCourse deleted successfully."}, status=status.HTTP_200_OK
        )


class CourseWithSubCoursesAPIView(APIView):
    def post(self, request, *args, **kwargs):
        # Client se course IDs ka array lete hain
        course_slugs = request.data.get("course_slugs", [])

        if not course_slugs:
            return Response(
                {"error": "course_slugs field is required and cannot be empty."},
                status=status.HTTP_400_BAD_REQUEST,
            )

        # Diye gaye IDs ke basis par courses fetch karein
        courses = Course.objects.filter(slug__in=course_slugs)
        serializer = CourseSerializer(courses, many=True)

        return Response(serializer.data, status=status.HTTP_200_OK)


# Tier API
class TierAPIView(APIView):

    def get(self, request, slug=None):
        if slug:
            tier = Tier.objects.get(slug=slug)
            serializer = TierSerializer(tier)
        else:
            tiers = Tier.objects.all().order_by('-created_date')
            serializer = TierSerializer(tiers, many=True)
        return Response(serializer.data)

    def post(self, request):
        slug = request.data["subcourse"]

        try:
            subcourse = SubCourse.objects.get(slug=slug)
        except SubCourse.DoesNotExist:
            return Response(
                {"error": "SubCourse not found."}, status=status.HTTP_404_NOT_FOUND
            )

        if slug:
            request.data["subcourse"] = subcourse.subcourse_id
        serializer = TierSerializer(data=request.data)
        if serializer.is_valid():
            serializer.save()
            return Response(serializer.data, status=status.HTTP_201_CREATED)
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

    def put(self, request, slug):
        try:
            tier = Tier.objects.get(slug=slug)
        except Tier.DoesNotExist:
            return Response(
                {"error": "Tier not found."}, status=status.HTTP_404_NOT_FOUND
            )

        serializer = TierSerializer(tier, data=request.data, partial=True)
        if serializer.is_valid():
            serializer.save()
            return Response(serializer.data)
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

    def delete(self, request, slug):
        try:
            tier = Tier.objects.get(slug=slug)
        except Tier.DoesNotExist:
            return Response(
                {"error": "Tier not found."}, status=status.HTTP_404_NOT_FOUND
            )
        tier.delete()
        return Response(
            {"success": "Tier deleted successfully."}, status=status.HTTP_200_OK
        )


# Paper API
class PaperAPIView(APIView):

    def get(self, request, slug=None):
        if slug:
            paper = Paper.objects.get(slug=slug)
            serializer = PaperSerializer(paper)
        else:
            papers = Paper.objects.all().order_by('-created_date')
            serializer = PaperSerializer(papers, many=True)
        return Response(serializer.data)

    def post(self, request):
        slug = request.data["tier"]
        try:
            tier = Tier.objects.get(slug=slug)
        except Tier.DoesNotExist:
            return Response(
                {"error": "Tier not found."}, status=status.HTTP_404_NOT_FOUND
            )
        if slug:
            request.data["tier"] = tier.tier_id
        serializer = PaperSerializer(data=request.data)
        if serializer.is_valid():
            serializer.save()
            return Response(serializer.data, status=status.HTTP_201_CREATED)
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

    def put(self, request, slug):
        try:
            paper = Paper.objects.get(slug=slug)
        except Paper.DoesNotExist:
            return Response(
                {"error": "Paper not found."}, status=status.HTTP_404_NOT_FOUND
            )

        serializer = PaperSerializer(paper, data=request.data, partial=True)
        if serializer.is_valid():
            serializer.save()
            return Response(serializer.data)
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

    def delete(self, request, slug):
        try:
            paper = Paper.objects.get(slug=slug)
        except Paper.DoesNotExist:
            return Response(
                {"error": "Paper not found."}, status=status.HTTP_404_NOT_FOUND
            )
        paper.delete()
        return Response(
            {"success": "Paper deleted successfully."}, status=status.HTTP_200_OK
        )


# Section API
class SectionAPIView(APIView):

    def get(self, request, slug=None):
        if slug:
            section = Section.objects.get(slug=slug)
            serializer = SectionSerializer(section)
        else:
            sections = Section.objects.all().order_by('-created_date')
            serializer = SectionSerializer(sections, many=True)
        return Response(serializer.data)

    def post(self, request):
        slug = request.data["paper"]
        try:
            paper = Paper.objects.get(slug=slug)
        except Paper.DoesNotExist:
            return Response(
                {"error": "Paper not found."}, status=status.HTTP_404_NOT_FOUND
            )
        if slug:
            request.data["paper"] = paper.paper_id
        serializer = SectionSerializer(data=request.data)
        if serializer.is_valid():
            serializer.save()
            return Response(serializer.data, status=status.HTTP_201_CREATED)
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

    def put(self, request, slug):
        try:
            section = Section.objects.get(slug=slug)
        except Section.DoesNotExist:
            return Response(
                {"error": "Section not found."}, status=status.HTTP_404_NOT_FOUND
            )
        serializer = SectionSerializer(section, data=request.data, partial=True)
        if serializer.is_valid():
            serializer.save()
            return Response(serializer.data)
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

    def delete(self, request, slug):
        try:
            section = Section.objects.get(slug=slug)
        except Section.DoesNotExist:
            return Response(
                {"error": "Section not found."}, status=status.HTTP_404_NOT_FOUND
            )
        section.delete()
        return Response(
            {"success": "Section deleted successfully."}, status=status.HTTP_200_OK
        )


# Module API
class ModuleAPIView(APIView):
    def get(self, request, slug=None):
        if slug:
            module = Module.objects.get(slug=slug)
            serializer = ModuleSerializer(module)
        else:
            modules = Module.objects.all().order_by('-created_date')
            serializer = ModuleSerializer(modules, many=True)
        return Response(serializer.data)

    def post(self, request):
        slug = request.data["section"]
        try:
            section = Section.objects.get(slug=slug)
        except Section.DoesNotExist:
            return Response(
                {"error": "Section not found."}, status=status.HTTP_404_NOT_FOUND
            )
        if slug:
            request.data["section"] = section.section_id
        serializer = ModuleSerializer(data=request.data)
        if serializer.is_valid():
            serializer.save()
            return Response(serializer.data, status=status.HTTP_201_CREATED)
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

    def put(self, request, slug):
        try:
            module = Module.objects.get(slug=slug)
        except Module.DoesNotExist:
            return Response(
                {"error": "Module not found."}, status=status.HTTP_404_NOT_FOUND
            )

        serializer = ModuleSerializer(module, data=request.data)
        if serializer.is_valid():
            serializer.save()
            return Response(serializer.data)
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

    def delete(self, request, slug):
        try:
            module = Module.objects.get(slug=slug)
        except Module.DoesNotExist:
            return Response(
                {"error": "Module not found."}, status=status.HTTP_404_NOT_FOUND
            )
        module.delete()
        return Response(
            {"success": "Module deleted successfully."}, status=status.HTTP_200_OK
        )


class SubjectListCreateView(APIView):
    def get_permissions(self):
        if self.request.method == 'GET':
            return [AllowAny()]
        return [IsAuthenticated()]
    
    def get(self, request):
        subjects = Subject.objects.all().order_by('rank') 
        serializer = SubjectSerializer(subjects, many=True)
        return Response({"success": True, "data": serializer.data})

    def post(self, request):
        serializer = SubjectSerializer(data=request.data)
        if serializer.is_valid():
            serializer.save()
            return Response(
                {
                    "data": serializer.data,
                },
                status=status.HTTP_201_CREATED,
            )
        return Response(
            {"errors": serializer.errors},
            status=status.HTTP_400_BAD_REQUEST,
        )


class SubjectDetailView(APIView):
    def get(self, request, slug):
        try:
            subject = Subject.objects.get(slug=slug)
            serializer = SubjectSerializer(subject)
            return Response({"data": serializer.data})
        except Subject.DoesNotExist:
            return Response(
                {"message": "Subject not found."},
                status=status.HTTP_404_NOT_FOUND,
            )

    def put(self, request, slug):
        try:
            subject = Subject.objects.get(slug=slug)
            serializer = SubjectSerializer(subject, data=request.data)
            if serializer.is_valid():
                serializer.save()
                return Response(
                    {
                        "data": serializer.data,
                    }
                )
            return Response(
                {"success": False, "errors": serializer.errors},
                status=status.HTTP_400_BAD_REQUEST,
            )
        except Subject.DoesNotExist:
            return Response(
                {"success": False, "message": "Subject not found."},
                status=status.HTTP_404_NOT_FOUND,
            )

    def delete(self, request, slug):
        try:
            subject = Subject.objects.get(slug=slug)
            subject.delete()
            return Response(
                {"success": True, "message": "Subject deleted successfully."},
                status=status.HTTP_204_NO_CONTENT,
            )
        except Subject.DoesNotExist:
            return Response(
                {"success": False, "message": "Subject not found."},
                status=status.HTTP_404_NOT_FOUND,
            )


class TopicListCreateView(APIView):
    def get(self, request):
        topics = Topic.objects.all().order_by('-created_date')
        serializer = TopicSerializer(topics, many=True)
        return Response({"data": serializer.data})

    def post(self, request):
        slug = request.data["subject"]
        try:
            subject = Subject.objects.get(slug=slug)
        except Subject.DoesNotExist:
            return Response(
                {"error": "Subject not found."}, status=status.HTTP_404_NOT_FOUND
            )

        if slug:
            request.data["subject"] = subject.subject_id
        serializer = TopicSerializer(data=request.data)
        if serializer.is_valid():
            serializer.save()
            return Response(
                {
                    "data": serializer.data,
                },
                status=status.HTTP_201_CREATED,
            )
        return Response(
            {"success": False, "errors": serializer.errors},
            status=status.HTTP_400_BAD_REQUEST,
        )


class TopicDetailView(APIView):
    def get(self, request, slug):
        try:
            topic = Topic.objects.get(slug=slug)
            serializer = TopicSerializer(topic)
            return Response({"data": serializer.data})
        except Topic.DoesNotExist:
            return Response(
                {"message": "Topic not found."},
                status=status.HTTP_404_NOT_FOUND,
            )

    def put(self, request, slug):
        try:
            sub_slug = request.data["subject"]
            subject = Subject.objects.get(slug=sub_slug)
            if slug:
                request.data["subject"] = subject.subject_id
            topic = Topic.objects.get(slug=slug)
            serializer = TopicSerializer(topic, data=request.data)
            if serializer.is_valid():
                serializer.save()
                return Response(
                    {
                        "data": serializer.data,
                    }
                )
            return Response(
                {"errors": serializer.errors},
                status=status.HTTP_400_BAD_REQUEST,
            )
        except Topic.DoesNotExist:
            return Response(
                {"message": "Topic not found."},
                status=status.HTTP_404_NOT_FOUND,
            )

    def delete(self, request, slug):
        try:
            topic = Topic.objects.get(slug=slug)
            topic.delete()
            return Response(
                {"message": "Topic deleted successfully."},
                status=status.HTTP_204_NO_CONTENT,
            )
        except Topic.DoesNotExist:
            return Response(
                {"message": "Topic not found."},
                status=status.HTTP_404_NOT_FOUND,
            )


class SubTopicListCreateView(APIView):

    def get(self, request):
        sub_topics = SubTopic.objects.all().order_by('-created_date')
        serializer = SubTopicSerializer(sub_topics, many=True)
        return Response({"data": serializer.data})

    def post(self, request):
        slug = request.data["topic"]
        try:
            topic = Topic.objects.get(slug=slug)
        except Topic.DoesNotExist:
            return Response(
                {"error": "Topic not found."}, status=status.HTTP_404_NOT_FOUND
            )

        if slug:
            request.data["topic"] = topic.topic_id
        serializer = SubTopicSerializer(data=request.data)
        if serializer.is_valid():
            serializer.save()
            return Response(
                {
                    "success": True,
                    "message": "SubTopic created successfully.",
                    "data": serializer.data,
                },
                status=status.HTTP_201_CREATED,
            )
        return Response(
            {"success": False, "errors": serializer.errors},
            status=status.HTTP_400_BAD_REQUEST,
        )


class SubTopicDetailView(APIView):

    def get(self, request, slug):
        try:
            sub_topic = SubTopic.objects.get(slug=slug)
            serializer = SubTopicSerializer(sub_topic)
            return Response({"data": serializer.data})
        except SubTopic.DoesNotExist:
            return Response(
                {"message": "SubTopic not found."},
                status=status.HTTP_404_NOT_FOUND,
            )

    def put(self, request, slug):
        try:
            topic_slug = request.data["topic"]
            topic = Topic.objects.get(slug=topic_slug)
            if slug:
                request.data["topic"] = topic.topic_id
            sub_topic = SubTopic.objects.get(slug=slug)
            serializer = SubTopicSerializer(sub_topic, data=request.data)
            if serializer.is_valid():
                serializer.save()
                return Response(
                    {
                        "data": serializer.data,
                    }
                )
            return Response(
                {"errors": serializer.errors},
                status=status.HTTP_400_BAD_REQUEST,
            )
        except SubTopic.DoesNotExist:
            return Response(
                {"message": "SubTopic not found."},
                status=status.HTTP_404_NOT_FOUND,
            )

    def delete(self, request, slug):
        try:
            sub_topic = SubTopic.objects.get(slug=slug)
            sub_topic.delete()
            return Response(
                {"success": True, "message": "SubTopic deleted successfully."},
                status=status.HTTP_204_NO_CONTENT,
            )
        except SubTopic.DoesNotExist:
            return Response(
                {"success": False, "message": "SubTopic not found."},
                status=status.HTTP_404_NOT_FOUND,
            )


class MasterQuestionListView(APIView):
    permission_classes = (IsContributorUser,)

    def get(self, request):
        contributor_profile = ContributorProfile.objects.get(user=request.user)
        master_questions = MasterQuestion.objects.filter(author=contributor_profile).order_by('-created_at')
        serializer = MasterQuestionSerializer(master_questions, many=True)
        return Response(serializer.data)

    def post(self, request):
        if request.data.get("is_current_affairs"):
            slug = request.data["current_affairs"]
            request.data['current_affairs'] = BlogPost.objects.get(slug=slug).id
        serializer = MasterQuestionSerializer(data=request.data)
        if serializer.is_valid():
            serializer.save()
            return Response(serializer.data, status=status.HTTP_201_CREATED)
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)


class MasterQuestionDetailView(APIView):
    permission_classes = (IsContributorUser,)

    def get(self, request, slug):
        try:
            master_question = MasterQuestion.objects.get(slug=slug)
            serializer = MasterQuestionSerializer(master_question)
            return Response(serializer.data)
        except MasterQuestion.DoesNotExist:
            return Response(
                {"error": "MasterQuestion not found."}, status=status.HTTP_404_NOT_FOUND
            )

    def put(self, request, slug):
        try:
            master_question = MasterQuestion.objects.get(slug=slug)
        except MasterQuestion.DoesNotExist:
            return Response(
                {"error": "MasterQuestion not found."}, status=status.HTTP_404_NOT_FOUND
            )

        serializer = MasterQuestionSerializer(master_question, data=request.data)
        if serializer.is_valid():
            serializer.save()
            return Response(serializer.data)
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

    def delete(self, request, slug):
        try:
            master_question = MasterQuestion.objects.get(slug=slug)
        except MasterQuestion.DoesNotExist:
            return Response(
                {"error": "MasterQuestion not found."}, status=status.HTTP_404_NOT_FOUND
            )

        master_question.delete()
        return Response(status=status.HTTP_204_NO_CONTENT)


class OptionListView(APIView):
    permission_classes = (IsContributorUser,)

    def get(self, request, question_slug):
        try:
            question = Question.objects.get(slug=question_slug)
        except Question.DoesNotExist:
            return Response(
                {"error": "Question not found for options."}, status=status.HTTP_404_NOT_FOUND
            )

        options = Option.objects.filter(question_id=question.question_id)
        serializer = OptionSerializer(options, many=True)
        return Response(serializer.data)

    def post(self, request, question_slug):
        try:
            question = Question.objects.get(slug=question_slug)
        except Question.DoesNotExist:
            return Response(
                {"error": "Question not found."}, status=status.HTTP_404_NOT_FOUND
            )
        request.data["question"] = question.question_id
        serializer = OptionSerializer(data=request.data)

        if serializer.is_valid():
            serializer.save()
            return Response(serializer.data, status=201)
        else:
            print("Data is invalid:", serializer.errors)  # Print errors if invalid

        return Response(serializer.errors, status=400)

    def put(self, request, question_slug, slug):
        try:
            option = Option.objects.get(slug=slug)
        except Option.DoesNotExist:
            return Response(
                {"error": "Option not found."}, status=status.HTTP_404_NOT_FOUND
            )
        try:
            question = Question.objects.get(slug=question_slug)
        except Question.DoesNotExist:
            return Response(
                {"error": "Question not found."}, status=status.HTTP_404_NOT_FOUND
            )
        request.data["question"] = question.question_id
        serializer = OptionSerializer(option, data=request.data)
        if serializer.is_valid():
            serializer.save()
            return Response(serializer.data)
        return Response(serializer.errors, status=400)

    def delete(self, request, question_slug, slug):
        try:
            option = Option.objects.get(slug=slug)
        except Option.DoesNotExist:
            return Response(
                {"error": "Option not found."}, status=status.HTTP_404_NOT_FOUND
            )
        option.delete()
        return Response(status=204)


class MasterOptionListCreateView(APIView):
    permission_classes = (IsContributorUser,)

    def get(self, request):
        # List all MasterOption records
        contributor_profile = ContributorProfile.objects.get(user=request.user)
        master_options = MasterOption.objects.filter(author=contributor_profile).order_by('-created_at')
        serializer = MasterOptionSerializer(master_options, many=True)
        return Response(serializer.data)

    def post(self, request):
        # Create a new MasterOption record
        
        serializer = MasterOptionSerializer(data=request.data)
        if serializer.is_valid():
            serializer.save()
            return Response(serializer.data, status=status.HTTP_201_CREATED)
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)


class MasterOptionDetailView(APIView):
    permission_classes = (IsContributorUser,)

    def get(self, request, slug):
        try:
            master_option = MasterOption.objects.get(slug=slug)
            serializer = MasterOptionSerializer(master_option)
            return Response(serializer.data)
        except MasterOption.DoesNotExist:
            return Response(
                {"error": "MasterOption not found."}, status=status.HTTP_404_NOT_FOUND
            )

    def put(self, request, slug):
        try:
            master_option = MasterOption.objects.get(slug=slug)
        except MasterOption.DoesNotExist:
            return Response(
                {"error": "MasterOption not found."}, status=status.HTTP_404_NOT_FOUND
            )

        serializer = MasterOptionSerializer(master_option, data=request.data)
        if serializer.is_valid():
            serializer.save()
            return Response(serializer.data)
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

    def delete(self, request, slug):
        try:
            master_option = MasterOption.objects.get(slug=slug)
        except MasterOption.DoesNotExist:
            return Response(
                {"error": "MasterOption not found."}, status=status.HTTP_404_NOT_FOUND
            )

        master_option.delete()
        return Response(status=status.HTTP_204_NO_CONTENT)


class PreviousYearQuestionListCreateView(APIView):
    permission_classes = (IsContributorUser,)

    def get(self, request):
        """Retrieve a list of previous year questions, with optional filtering."""

        contributor = ContributorProfile.objects.get(user=request.user)
        year = request.query_params.get("year")
        status_param = request.query_params.get("status")
        subject = request.query_params.get("subject")
        exam = request.query_params.get("exam")
        queryset = PreviousYearQuestion.objects.filter(
            question__author=contributor
        ).order_by("-id")

        if year:
            queryset = queryset.filter(year=year)
        if status_param:
            queryset = queryset.filter(status=status_param)
        if subject:
            queryset = queryset.filter(question__subject_name__icontains=subject)
        if exam:
            exam_list = [e.strip().lower() for e in exam.split(",")]
            print("Exam List:", exam_list)  # Debug: Ensure correct parsing
            query = Q()
            for e in exam_list:
                query |= Q(exams__iregex=rf"(^|,){e}(,|$)")
            queryset = queryset.filter(query)
            print("Queryset after filtering by exam:", queryset)

        serializer = PreviousYearQuestionSerializer(queryset, many=True)
        return Response(serializer.data, status=status.HTTP_200_OK)

    def post(self, request):
        """Create a new previous year question."""
        serializer = PreviousYearQuestionSerializer(data=request.data)
        if serializer.is_valid():
            serializer.save()
            return Response(serializer.data, status=status.HTTP_201_CREATED)
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)


class PreviousYearQuestionDetailView(APIView):
    permission_classes = (IsContributorUser,)

    def get_object(self, slug):
        """Retrieve the specific object or return 404."""
        try:
            return PreviousYearQuestion.objects.get(slug=slug)
        except PreviousYearQuestion.DoesNotExist:
            return Response(
                {"error": "PreviousYearQuestion not found."},
                status=status.HTTP_404_NOT_FOUND,
            )

    def get(self, request, slug):
        """Retrieve details of a specific previous year question."""
        obj = self.get_object(slug)
        if obj is None:
            return Response({"error": "Not Found"}, status=status.HTTP_404_NOT_FOUND)

        serializer = PreviousYearQuestionSerializer(obj)
        return Response(serializer.data, status=status.HTTP_200_OK)

    def put(self, request, slug):
        """Update a specific previous year question."""
        obj = self.get_object(slug)
        if obj is None:
            return Response({"error": "Not Found"}, status=status.HTTP_404_NOT_FOUND)

        serializer = PreviousYearQuestionSerializer(obj, data=request.data)
        if serializer.is_valid():
            serializer.save()
            return Response(serializer.data, status=status.HTTP_200_OK)
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

    def delete(self, request, slug):
        """Delete a specific previous year question."""
        obj = self.get_object(slug)
        if obj is None:
            return Response({"error": "Not Found"}, status=status.HTTP_404_NOT_FOUND)

        obj.delete()
        return Response(
            {"message": "Deleted successfully."}, status=status.HTTP_204_NO_CONTENT
        )

from datetime import timedelta

def format_duration(duration):
    if duration:
        total_seconds = int(duration.total_seconds())  # Convert timedelta to seconds
        hours, remainder = divmod(total_seconds, 3600)  # Get hours
        minutes, seconds = divmod(remainder, 60)  # Get minutes and seconds
        return f"{hours:02}:{minutes:02}:{seconds:02}"  # Format as HH:MM:SS
    return "00:00:00" 


class TestSeries(APIView):
    def get(self, request):
        sc = SubCourse.objects.all().order_by("-created_date")
        card_array = []
        
        for cnt,i in enumerate(sc):
            data = {}
            data['course'] = i.course.name
            data['course_slug'] = i.course.slug
            data["language"] = "English & Hindi"
            data["subcourse_slug"] = i.slug
            t = Tier.objects.filter(subcourse=i)
            if len(t) <1:
                continue
            data["sub_course_name"] = i.name
            if cnt < 4 :
                data["paid"] = True
            else:
                data["paid"] = False
            paper_array = []
            for j in t:
                p_data = {}
                p = Paper.objects.filter(tier=j)
                for paper in p:
                    print(paper)
                    p_data['paper_id'] = paper.paper_id
                    p_data['duration'] = format_duration(paper.duration)
                    p_data['total_marks'] = paper.max_marks
                    p_data['slug'] = paper.slug
                    paper_array.append(p_data)
                data['paper_details'] = paper_array                       
            card_array.append(data)
                
        return Response({"success": True, "data": card_array}, status=200)


class TestSeriesView(APIView):
    def get(self, request, slug=None):
        sub_course = SubCourse.objects.get(slug=slug)  
        response_data = []

        sub_course_data = {
            "subcourse_name": sub_course.name,
            
            "subcourse_description": sub_course.description,
            "totalUsers": 5000,
            "Tiers": []
        }

        tiers = Tier.objects.filter(subcourse=sub_course)
        total_sub_course_obj = Question.objects.filter(subcourse=sub_course).count()

        print("Total Sub Course Obj", total_sub_course_obj)
        for tier in tiers:
            tier_data = {
                "Tier_name": tier.name,
                "key": tier.slug,
                "paper": []
            }

            papers = Paper.objects.filter(tier=tier)
            for paper in papers:
                totalQuestions = 0#Question.objects.filter(paper=paper).count()
                if paper.duration:
                    ttl_sec = paper.duration.total_seconds()
                else:
                    ttl_sec  = 0
                paper_data = {
                    'paper_id' :paper.paper_id,
                    "paper_name": paper.name,
                    "totalQuestions": total_sub_course_obj,  
                    "total_test_series": 455,
                    "freeTestsTotal": 4,
                    "totalMarks": paper.max_marks,
                    "durationMinutes": int(ttl_sec // 60),
                    "expiryDate": 66, 
                    "type": "paid" 
                }
                tier_data["paper"].append(paper_data)

            sub_course_data["Tiers"].append(tier_data)

        response_data.append(sub_course_data)

        return Response({"testSeries": response_data}, status=200)

            
class SubjectRankUpdateView(APIView):
    def post(self, request, subject_id):
        try:
            subject = Subject.objects.get(subject_id=subject_id)
            subject.rank += 1  # Increment the rank by 1
            subject.save()
            return Response(
                {"success": True, "data": {"subject_id": subject.subject_id, "rank": subject.rank}},
                status=status.HTTP_200_OK,
            )
        except Subject.DoesNotExist:
            return Response(
                {"error": "Subject not found"},
                status=status.HTTP_404_NOT_FOUND,
            )