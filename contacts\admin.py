from django.contrib import admin
from django.utils.html import format_html
from django.urls import reverse
from django.utils import timezone
from .models import Contact


@admin.register(Contact)
class ContactAdmin(admin.ModelAdmin):
    list_display = [
        'user_link', 'name', 'contact_number', 'is_matched',
        'related_user_link', 'synced_at', 'age_hours'
    ]
    list_filter = ['is_matched', 'synced_at', 'updated_at']
    search_fields = [
        'user__username', 'name', 'contact_number',
        'related_user__username'
    ]
    readonly_fields = ['synced_at', 'updated_at', 'age_hours']
    raw_id_fields = ['user', 'related_user']

    fieldsets = (
        ('Contact Information', {
            'fields': ('user', 'name', 'contact_number')
        }),
        ('Matching Information', {
            'fields': ('is_matched', 'related_user')
        }),
        ('Additional Data', {
            'fields': ('raw_data',),
            'classes': ('collapse',)
        }),
        ('Timestamps', {
            'fields': ('synced_at', 'updated_at', 'age_hours'),
            'classes': ('collapse',)
        }),
    )

    def get_queryset(self, request):
        return super().get_queryset(request).select_related('user', 'related_user')

    def user_link(self, obj):
        if obj.user:
            url = reverse('admin:auth_user_change', args=[obj.user.pk])
            return format_html('<a href="{}">{}</a>', url, obj.user.username)
        return '-'
    user_link.short_description = 'User'
    user_link.admin_order_field = 'user__username'

    def related_user_link(self, obj):
        if obj.related_user:
            url = reverse('admin:auth_user_change', args=[obj.related_user.pk])
            return format_html('<a href="{}">{}</a>', url, obj.related_user.username)
        return '-'
    related_user_link.short_description = 'Related User'
    related_user_link.admin_order_field = 'related_user__username'

    def age_hours(self, obj):
        """Show how old this contact is in hours"""
        age = timezone.now() - obj.synced_at
        hours = age.total_seconds() / 3600
        if hours < 1:
            return f"{int(age.total_seconds() / 60)} minutes"
        elif hours < 24:
            return f"{hours:.1f} hours"
        else:
            days = hours / 24
            return f"{days:.1f} days"
    age_hours.short_description = 'Age'

    actions = ['cleanup_unmatched', 'force_rematch']

    def cleanup_unmatched(self, request, queryset):
        """Admin action to cleanup unmatched contacts"""
        unmatched = queryset.filter(is_matched=False)
        count = unmatched.count()
        unmatched.delete()

        self.message_user(
            request,
            f'Successfully deleted {count} unmatched contacts.'
        )
    cleanup_unmatched.short_description = 'Delete unmatched contacts'

    def force_rematch(self, request, queryset):
        """Admin action to force re-matching of contacts"""
        updated_count = 0
        for contact in queryset:
            if contact.check_and_match_user():
                contact.save()
                updated_count += 1

        self.message_user(
            request,
            f'Successfully re-matched {updated_count} contacts.'
        )
    force_rematch.short_description = 'Force re-match contacts'
