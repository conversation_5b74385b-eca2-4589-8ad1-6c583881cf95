/**
 * React Error Boundary Component for Frontend Error Logging
 * 
 * This component catches JavaScript errors anywhere in the child component tree,
 * logs those errors, and displays a fallback UI instead of the component tree that crashed.
 * 
 * Usage:
 * import ErrorBoundary from './react-error-boundary.js';
 * 
 * <ErrorBoundary>
 *   <YourComponent />
 * </ErrorBoundary>
 */

import React from 'react';

class ErrorBoundary extends React.Component {
    constructor(props) {
        super(props);
        
        this.state = {
            hasError: false,
            error: null,
            errorInfo: null,
            errorId: null
        };
        
        this.retryCount = 0;
        this.maxRetries = 3;
    }
    
    static getDerivedStateFromError(error) {
        // Update state so the next render will show the fallback UI
        return { hasError: true };
    }
    
    componentDidCatch(error, errorInfo) {
        // Log the error
        this.logError(error, errorInfo);
        
        // Update state with error details
        this.setState({
            error: error,
            errorInfo: errorInfo,
            errorId: this.generateErrorId()
        });
    }
    
    async logError(error, errorInfo) {
        try {
            const errorData = {
                error_type: 'RENDER_ERROR',
                error_message: error.message || 'React component error',
                stack_trace: error.stack || '',
                page_url: window.location.href,
                page_title: document.title,
                referrer_url: document.referrer,
                component_name: this.getComponentName(errorInfo),
                severity: 'HIGH',
                additional_data: {
                    react_error: true,
                    component_stack: errorInfo.componentStack,
                    error_boundary: this.constructor.name,
                    props: this.sanitizeProps(this.props),
                    state: this.sanitizeState(this.state),
                    retry_count: this.retryCount,
                    timestamp: new Date().toISOString(),
                    user_agent: navigator.userAgent,
                    screen_resolution: `${screen.width}x${screen.height}`
                }
            };
            
            // Send error to backend
            await this.sendErrorToBackend(errorData);
            
            // Also log to ErrorLogger if available
            if (window.ErrorLogger) {
                window.ErrorLogger.logError(
                    `React Error: ${error.message}`,
                    {
                        component_stack: errorInfo.componentStack,
                        error_boundary: true
                    },
                    'HIGH'
                );
            }
            
        } catch (logError) {
            console.error('Failed to log React error:', logError);
        }
    }
    
    async sendErrorToBackend(errorData) {
        const maxRetries = 3;
        let retryCount = 0;
        
        while (retryCount < maxRetries) {
            try {
                const response = await fetch('/customrcare/log-error/', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRFToken': this.getCSRFToken()
                    },
                    body: JSON.stringify(errorData)
                });
                
                if (response.ok) {
                    const result = await response.json();
                    console.log('React error logged successfully:', result);
                    break;
                } else {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
                
            } catch (error) {
                retryCount++;
                console.error(`Failed to log React error (attempt ${retryCount}):`, error);
                
                if (retryCount < maxRetries) {
                    // Wait before retrying (exponential backoff)
                    await new Promise(resolve => setTimeout(resolve, 1000 * retryCount));
                }
            }
        }
    }
    
    getComponentName(errorInfo) {
        if (!errorInfo || !errorInfo.componentStack) return 'Unknown';
        
        const lines = errorInfo.componentStack.split('\n');
        for (const line of lines) {
            const match = line.trim().match(/^in (\w+)/);
            if (match && match[1] !== 'ErrorBoundary') {
                return match[1];
            }
        }
        return 'Unknown';
    }
    
    sanitizeProps(props) {
        // Remove sensitive data and functions from props
        const sanitized = {};
        
        for (const [key, value] of Object.entries(props)) {
            if (typeof value === 'function') {
                sanitized[key] = '[Function]';
            } else if (key.toLowerCase().includes('password') || key.toLowerCase().includes('token')) {
                sanitized[key] = '[REDACTED]';
            } else if (typeof value === 'object' && value !== null) {
                sanitized[key] = '[Object]';
            } else {
                sanitized[key] = value;
            }
        }
        
        return sanitized;
    }
    
    sanitizeState(state) {
        // Remove sensitive data from state
        const sanitized = {};
        
        for (const [key, value] of Object.entries(state)) {
            if (key.toLowerCase().includes('password') || key.toLowerCase().includes('token')) {
                sanitized[key] = '[REDACTED]';
            } else if (typeof value === 'object' && value !== null) {
                sanitized[key] = '[Object]';
            } else {
                sanitized[key] = value;
            }
        }
        
        return sanitized;
    }
    
    generateErrorId() {
        return 'error_' + Math.random().toString(36).substr(2, 9) + '_' + Date.now();
    }
    
    getCSRFToken() {
        const token = document.querySelector('[name=csrfmiddlewaretoken]');
        return token ? token.value : '';
    }
    
    handleRetry = () => {
        if (this.retryCount < this.maxRetries) {
            this.retryCount++;
            this.setState({
                hasError: false,
                error: null,
                errorInfo: null,
                errorId: null
            });
        }
    }
    
    handleReload = () => {
        window.location.reload();
    }
    
    render() {
        if (this.state.hasError) {
            // Custom error UI
            const { fallback: Fallback } = this.props;
            
            if (Fallback) {
                return (
                    <Fallback
                        error={this.state.error}
                        errorInfo={this.state.errorInfo}
                        errorId={this.state.errorId}
                        onRetry={this.handleRetry}
                        onReload={this.handleReload}
                        canRetry={this.retryCount < this.maxRetries}
                    />
                );
            }
            
            // Default error UI
            return (
                <div style={{
                    padding: '20px',
                    margin: '20px',
                    border: '1px solid #ff6b6b',
                    borderRadius: '8px',
                    backgroundColor: '#ffe0e0',
                    color: '#d63031',
                    fontFamily: 'Arial, sans-serif'
                }}>
                    <h2 style={{ margin: '0 0 10px 0', color: '#d63031' }}>
                        ⚠️ Something went wrong
                    </h2>
                    
                    <p style={{ margin: '0 0 15px 0' }}>
                        We're sorry, but something unexpected happened. The error has been logged and our team will investigate.
                    </p>
                    
                    {this.state.errorId && (
                        <p style={{ margin: '0 0 15px 0', fontSize: '12px', color: '#666' }}>
                            Error ID: {this.state.errorId}
                        </p>
                    )}
                    
                    <div style={{ display: 'flex', gap: '10px' }}>
                        {this.retryCount < this.maxRetries && (
                            <button
                                onClick={this.handleRetry}
                                style={{
                                    padding: '8px 16px',
                                    backgroundColor: '#0984e3',
                                    color: 'white',
                                    border: 'none',
                                    borderRadius: '4px',
                                    cursor: 'pointer'
                                }}
                            >
                                Try Again ({this.maxRetries - this.retryCount} attempts left)
                            </button>
                        )}
                        
                        <button
                            onClick={this.handleReload}
                            style={{
                                padding: '8px 16px',
                                backgroundColor: '#6c5ce7',
                                color: 'white',
                                border: 'none',
                                borderRadius: '4px',
                                cursor: 'pointer'
                            }}
                        >
                            Reload Page
                        </button>
                    </div>
                    
                    {process.env.NODE_ENV === 'development' && this.state.error && (
                        <details style={{ marginTop: '15px' }}>
                            <summary style={{ cursor: 'pointer', fontWeight: 'bold' }}>
                                Error Details (Development Only)
                            </summary>
                            <pre style={{
                                marginTop: '10px',
                                padding: '10px',
                                backgroundColor: '#f8f9fa',
                                border: '1px solid #dee2e6',
                                borderRadius: '4px',
                                fontSize: '12px',
                                overflow: 'auto',
                                maxHeight: '200px'
                            }}>
                                {this.state.error.toString()}
                                {this.state.errorInfo.componentStack}
                            </pre>
                        </details>
                    )}
                </div>
            );
        }
        
        return this.props.children;
    }
}

// Higher-order component for easier usage
export const withErrorBoundary = (Component, fallback) => {
    return function WrappedComponent(props) {
        return (
            <ErrorBoundary fallback={fallback}>
                <Component {...props} />
            </ErrorBoundary>
        );
    };
};

// Hook for manual error reporting
export const useErrorHandler = () => {
    const reportError = React.useCallback((error, errorInfo = {}) => {
        // Create a temporary error boundary to handle the error
        const errorBoundary = new ErrorBoundary({});
        errorBoundary.logError(error, { componentStack: JSON.stringify(errorInfo) });
    }, []);
    
    return reportError;
};

export default ErrorBoundary;
