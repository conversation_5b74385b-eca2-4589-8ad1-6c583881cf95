{"metadata": {"generated_at": "2025-06-23T16:22:02.192405+00:00", "period_start": "2025-06-22T16:22:02.192405+00:00", "period_end": "2025-06-23T16:22:02.192405+00:00", "period_days": 1, "sections_included": ["all"]}, "error_summary": {"total_errors": 4, "by_type": [{"error_type": "BUSINESS_LOGIC", "count": 2}, {"error_type": "VALIDATION", "count": 2}], "by_severity": [{"severity": "LOW", "count": 2}, {"severity": "MEDIUM", "count": 2}], "unresolved": 4, "critical_errors": 0}, "error_details": [{"timestamp": "2025-06-23T16:22:00.489102+00:00", "view_name": "test_view", "error_type": "BUSINESS_LOGIC", "severity": "MEDIUM", "message": "Test error message", "user": "testuser2_a8e888e1", "path": "", "resolved": false}, {"timestamp": "2025-06-23T16:22:00.327874+00:00", "view_name": "test_view", "error_type": "VALIDATION", "severity": "LOW", "message": "Test error", "user": "testuser_1e65985f", "path": "", "resolved": false}, {"timestamp": "2025-06-23T16:19:34.405300+00:00", "view_name": "test_view", "error_type": "BUSINESS_LOGIC", "severity": "MEDIUM", "message": "Test error message", "user": "testuser2", "path": "", "resolved": false}, {"timestamp": "2025-06-23T16:19:34.243859+00:00", "view_name": "test_view", "error_type": "VALIDATION", "severity": "LOW", "message": "Test error", "user": "testuser", "path": "", "resolved": false}], "performance_summary": {"total_requests": 8, "avg_response_time": 0.18517476320266724, "max_response_time": 0.5, "min_response_time": 0.0018718242645263672, "slow_requests": 0, "avg_db_queries": 6.25, "by_status_code": [{"status_code": 200, "count": 8}]}, "performance_details": [{"timestamp": "2025-06-23T16:22:00.326542+00:00", "path": "/test/path/", "method": "GET", "duration": 0.5, "status_code": 200, "db_queries": 0, "user": "testuser_1e65985f"}, {"timestamp": "2025-06-23T16:19:34.242276+00:00", "path": "/test/path/", "method": "GET", "duration": 0.5, "status_code": 200, "db_queries": 0, "user": "testuser"}, {"timestamp": "2025-06-23T16:22:01.109262+00:00", "path": "/api/log-admin/health/", "method": "GET", "duration": 0.45415616035461426, "status_code": 200, "db_queries": 4, "user": "admin_03676461"}, {"timestamp": "2025-06-23T16:22:01.138394+00:00", "path": "/api/log-admin/analytics/", "method": "GET", "duration": 0.00956106185913086, "status_code": 200, "db_queries": 24, "user": "admin_03676461"}, {"timestamp": "2025-06-23T16:22:01.118722+00:00", "path": "/api/log-admin/performance/", "method": "GET", "duration": 0.006453990936279297, "status_code": 200, "db_queries": 3, "user": "admin_03676461"}, {"timestamp": "2025-06-23T16:22:01.146269+00:00", "path": "/api/log-admin/dashboard/", "method": "GET", "duration": 0.004803895950317383, "status_code": 200, "db_queries": 14, "user": "admin_03676461"}, {"timestamp": "2025-06-23T16:22:01.126096+00:00", "path": "/api/log-admin/errors/", "method": "GET", "duration": 0.0045511722564697266, "status_code": 200, "db_queries": 3, "user": "admin_03676461"}, {"timestamp": "2025-06-23T16:22:01.151329+00:00", "path": "/api/log-admin/config/", "method": "GET", "duration": 0.0018718242645263672, "status_code": 200, "db_queries": 2, "user": "admin_03676461"}], "activity_summary": {"total_activities": 8, "unique_users": 7, "by_type": [{"activity_type": "API_CALL", "count": 2}, {"activity_type": "LOGIN", "count": 2}, {"activity_type": "OTHER", "count": 4}], "successful_activities": 8, "failed_activities": 0}, "activity_details": [{"timestamp": "2025-06-23T16:22:00.484893+00:00", "user": "testuser2_a8e888e1", "activity_type": "API_CALL", "action": "Test API call", "success": true, "ip_address": null}, {"timestamp": "2025-06-23T16:22:00.330524+00:00", "user": "testuser_1e65985f", "activity_type": "LOGIN", "action": "User logged in", "success": true, "ip_address": "127.0.0.1"}, {"timestamp": "2025-06-23T16:19:34.400831+00:00", "user": "testuser2", "activity_type": "API_CALL", "action": "Test API call", "success": true, "ip_address": null}, {"timestamp": "2025-06-23T16:19:34.245827+00:00", "user": "testuser", "activity_type": "LOGIN", "action": "User logged in", "success": true, "ip_address": "127.0.0.1"}, {"timestamp": "2025-06-23T14:49:28.273577+00:00", "user": "<PERSON><PERSON><PERSON>", "activity_type": "OTHER", "action": "Contributor <PERSON><PERSON>", "success": true, "ip_address": null}, {"timestamp": "2025-06-23T10:28:25.283082+00:00", "user": "nayan", "activity_type": "OTHER", "action": "Student Login", "success": true, "ip_address": null}, {"timestamp": "2025-06-22T20:46:03.453611+00:00", "user": "nayan", "activity_type": "OTHER", "action": "Student Login", "success": true, "ip_address": null}, {"timestamp": "2025-06-22T20:42:56.267666+00:00", "user": "testlogin", "activity_type": "OTHER", "action": "Student Login", "success": true, "ip_address": null}], "security_summary": {"total_incidents": 2, "by_type": [{"incident_type": "BRUTE_FORCE", "count": 2}], "by_severity": [{"severity": "MEDIUM", "count": 2}], "unresolved": 2, "critical_incidents": 0, "blocked_incidents": 0}, "security_details": [{"timestamp": "2025-06-23T16:22:00.335627+00:00", "incident_type": "BRUTE_FORCE", "severity": "MEDIUM", "description": "Test security incident", "ip_address": "127.0.0.1", "blocked": false, "resolved": false}, {"timestamp": "2025-06-23T16:19:34.251806+00:00", "incident_type": "BRUTE_FORCE", "severity": "MEDIUM", "description": "Test security incident", "ip_address": "127.0.0.1", "blocked": false, "resolved": false}]}