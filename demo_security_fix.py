#!/usr/bin/env python3
"""
Demo script to show the authentication security fix in action
"""

import os
import sys
import django

# Add the project directory to Python path
sys.path.insert(0, '/Users/<USER>/Documents/code/shash_b')

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'shashtrarth.settings')
django.setup()

# Override ALLOWED_HOSTS for testing
from django.conf import settings
if 'testserver' not in settings.ALLOWED_HOSTS:
    settings.ALLOWED_HOSTS.append('testserver')

from django.contrib.auth.models import User
from customrcare.models import CustomrcareProfile
from contributor.models import ContributorProfile
from rest_framework.test import APIClient

def demo_security_fix():
    print("🔒 AUTHENTICATION SECURITY FIX DEMONSTRATION")
    print("=" * 60)

    # Clean up any existing demo users first
    print("\n0. Cleaning up any existing demo users...")
    try:
        User.objects.filter(username__startswith='demo_').delete()
        print("   ✅ Cleaned up existing demo users")
    except:
        print("   ✅ No existing demo users to clean up")

    # Create test users
    print("\n1. Creating test users...")
    
    # Customer care user
    cc_user = User.objects.create_user(
        username='demo_cc_user',
        password='demo123',
        email='<EMAIL>'
    )
    cc_profile = CustomrcareProfile.objects.create(
        user=cc_user,
        contact=1234567890,
        role='customrcare'
    )
    print(f"✅ Created customer care user: {cc_user.username}")
    
    # Contributor user
    contrib_user = User.objects.create_user(
        username='demo_contrib_user',
        password='demo123',
        email='<EMAIL>'
    )
    contrib_profile = ContributorProfile.objects.create(
        user=contrib_user,
        role='contributor'
    )
    print(f"✅ Created contributor user: {contrib_user.username}")
    
    client = APIClient()
    
    print("\n2. Testing LEGITIMATE access...")
    
    # Test legitimate customer care login
    print("\n🔍 Customer care user logging into customer care system:")
    response = client.post('/api/customrcare/login/', {
        'username': 'demo_cc_user',
        'password': 'demo123'
    })
    print(f"   Status: {response.status_code}")
    if response.status_code == 200:
        data = response.json()
        print(f"   ✅ SUCCESS - Role: {data.get('role')}")
    else:
        try:
            print(f"   ❌ FAILED - {response.json()}")
        except:
            print(f"   ❌ FAILED - Status: {response.status_code}")

    # Test legitimate contributor login
    print("\n🔍 Contributor user logging into contributor system:")
    response = client.post('/api/contributor/login/', {
        'username': 'demo_contrib_user',
        'password': 'demo123'
    })
    print(f"   Status: {response.status_code}")
    if response.status_code == 200:
        data = response.json()
        print(f"   ✅ SUCCESS - Role: {data.get('role')}")
    else:
        try:
            print(f"   ❌ FAILED - {response.json()}")
        except:
            print(f"   ❌ FAILED - Status: {response.status_code}")
    
    print("\n3. Testing CROSS-AUTHENTICATION (should be blocked)...")
    
    # Test customer care user trying to login to contributor system
    print("\n🚫 Customer care user trying to login to contributor system:")
    response = client.post('/api/contributor/login/', {
        'username': 'demo_cc_user',
        'password': 'demo123'
    })
    print(f"   Status: {response.status_code}")
    if response.status_code == 401:
        try:
            data = response.json()
            print(f"   ✅ CORRECTLY BLOCKED - {data}")
        except:
            print(f"   ✅ CORRECTLY BLOCKED - Status: {response.status_code}")
    else:
        try:
            print(f"   ❌ SECURITY ISSUE - Should be blocked but got: {response.json()}")
        except:
            print(f"   ❌ SECURITY ISSUE - Status: {response.status_code}")

    # Test contributor user trying to login to customer care system
    print("\n🚫 Contributor user trying to login to customer care system:")
    response = client.post('/api/customrcare/login/', {
        'username': 'demo_contrib_user',
        'password': 'demo123'
    })
    print(f"   Status: {response.status_code}")
    if response.status_code == 401:
        try:
            data = response.json()
            print(f"   ✅ CORRECTLY BLOCKED - {data}")
        except:
            print(f"   ✅ CORRECTLY BLOCKED - Status: {response.status_code}")
    else:
        try:
            print(f"   ❌ SECURITY ISSUE - Should be blocked but got: {response.json()}")
        except:
            print(f"   ❌ SECURITY ISSUE - Status: {response.status_code}")
    
    print("\n4. Verifying no automatic profile creation...")
    
    # Check that no unwanted profiles were created
    try:
        cc_user.refresh_from_db()
        if hasattr(cc_user, 'contributor_profile'):
            print("   ❌ SECURITY ISSUE: Customer care user got contributor profile!")
        else:
            print("   ✅ Customer care user has no contributor profile")
    except:
        print("   ✅ Customer care user has no contributor profile")
    
    try:
        contrib_user.refresh_from_db()
        if hasattr(contrib_user, 'customrcare_profile'):
            print("   ❌ SECURITY ISSUE: Contributor user got customer care profile!")
        else:
            print("   ✅ Contributor user has no customer care profile")
    except:
        print("   ✅ Contributor user has no customer care profile")
    
    print("\n5. Summary of the fix...")
    print("   🔒 BEFORE: Users could login to any system and profiles were auto-created")
    print("   🛡️  AFTER: Users can only login to systems they're authorized for")
    print("   ✅ No automatic profile creation for unauthorized users")
    print("   ✅ Proper role validation enforced")
    print("   ✅ Clear error messages for unauthorized access")
    
    # Cleanup
    print("\n6. Cleaning up test data...")
    cc_profile.delete()
    cc_user.delete()
    contrib_profile.delete()
    contrib_user.delete()
    print("   ✅ Test data cleaned up")
    
    print(f"\n🎉 SECURITY FIX VERIFICATION COMPLETE!")
    print("   The authentication systems are now properly isolated.")

if __name__ == '__main__':
    demo_security_fix()
