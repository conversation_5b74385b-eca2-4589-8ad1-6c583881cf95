"""
Comprehensive test suite for all views in the application.
"""
from django.test import TestCase, Client
from django.contrib.auth.models import User
from django.urls import reverse
from django.core.files.uploadedfile import SimpleUploadedFile
from rest_framework.test import APITestCase, APIClient
from rest_framework import status
import json

# Import models
from contributor.models import Banner, ContributorProfile, PageCounter
from customrcare.models import CustomrcareProfile
from questions.models import Course, SubCourse, Subject
from packages_and_subscriptions.models import Package, Subscription
from students.models import Student
from paper_engine.models import TestPattern


class BannerAPITest(APITestCase):
    """Test Banner API endpoints."""
    
    def setUp(self):
        self.client = APIClient()
        self.user = User.objects.create_user(
            username='testuser',
            password='testpass123'
        )
        self.customrcare_profile = CustomrcareProfile.objects.create(
            user=self.user,
            contact=1234567890
        )
        self.client.force_authenticate(user=self.user)
    
    def test_banner_list_endpoint(self):
        """Test banner list endpoint returns 200."""
        Banner.objects.create(
            banner_name='Test Banner',
            banner_image='test.jpg'
        )
        url = reverse('banner-list-create')
        response = self.client.get(url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
    
    def test_banner_create_endpoint(self):
        """Test banner creation endpoint."""
        url = reverse('banner-list-create')
        data = {
            'banner_name': 'New Banner',
            'banner_image': SimpleUploadedFile("test.jpg", b"file_content", content_type="image/jpeg")
        }
        response = self.client.post(url, data, format='multipart')
        # Should return 201 or 403 depending on permissions
        self.assertIn(response.status_code, [status.HTTP_201_CREATED, status.HTTP_403_FORBIDDEN])


class PageVisitorsViewTest(APITestCase):
    """Test PageVisitors view functionality."""
    
    def setUp(self):
        self.client = APIClient()
    
    def test_page_visitors_post(self):
        """Test PageVisitors view correctly assigns values to fields."""
        url = reverse('track_page_view')
        data = {
            '/': 5,
            '/about': 3,
            '/contact': 2
        }
        response = self.client.post(url, data, format='json')
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        
        # Check if PageCounter objects were created correctly
        home_counter = PageCounter.objects.filter(url='/home').first()
        about_counter = PageCounter.objects.filter(url='/about').first()
        contact_counter = PageCounter.objects.filter(url='/contact').first()
        
        self.assertIsNotNone(home_counter)
        self.assertEqual(home_counter.count, 5)
        self.assertIsNotNone(about_counter)
        self.assertEqual(about_counter.count, 3)
        self.assertIsNotNone(contact_counter)
        self.assertEqual(contact_counter.count, 2)


class ModelCountsAPITest(APITestCase):
    """Test model counts API."""
    
    def setUp(self):
        self.client = APIClient()
    
    def test_model_counts_api_response_format(self):
        """Test model counts API returns correct format."""
        url = reverse('get-all-model-counts')
        response = self.client.get(url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        
        # Check if response contains model names
        data = response.json()
        self.assertIsInstance(data, dict)
        # The API should return lowercase model names
        self.assertIn('user', data)


class LoginProcessTest(APITestCase):
    """Test login process and profile creation."""
    
    def setUp(self):
        self.client = APIClient()
        self.user = User.objects.create_user(
            username='testuser',
            password='testpass123'
        )
    
    def test_customrcare_login_creates_profile_with_valid_fields(self):
        """Test login process creates CustomrcareProfile with valid fields."""
        url = reverse('login')  # customrcare login
        data = {
            'username': 'testuser',
            'password': 'testpass123'
        }
        response = self.client.post(url, data, format='json')
        
        # Should either succeed or fail gracefully
        self.assertIn(response.status_code, [
            status.HTTP_200_OK, 
            status.HTTP_401_UNAUTHORIZED,
            status.HTTP_400_BAD_REQUEST
        ])


class PackageDetailViewTest(APITestCase):
    """Test package detail view."""
    
    def setUp(self):
        self.client = APIClient()
        self.package = Package.objects.create(
            name='Test Package',
            price=100.00,
            discount_price=80.00
        )
    
    def test_package_detail_url_exists(self):
        """Test package-detail URL exists."""
        try:
            url = reverse('package_detail', kwargs={'pk': self.package.id})
            response = self.client.get(url)
            # Should return some response, not 404
            self.assertNotEqual(response.status_code, 404)
        except:
            # URL name might be different
            self.skipTest("package_detail URL name not found")


class SubscriptionAPITest(APITestCase):
    """Test subscription API endpoints."""
    
    def setUp(self):
        self.client = APIClient()
        self.user = User.objects.create_user(
            username='testuser',
            password='testpass123'
        )
        self.student = Student.objects.create(
            user=self.user,
            phone='1234567890'
        )
        self.package = Package.objects.create(
            name='Test Package',
            price=100.00,
            discount_price=80.00
        )
    
    def test_subscription_creation_api_data_format(self):
        """Test subscription creation API expects correct data format."""
        url = reverse('subscription-list')
        data = {
            'student': self.student.id,
            'package': self.package.id
        }
        response = self.client.post(url, data, format='json')
        
        # Should return some response indicating the expected format
        self.assertIn(response.status_code, [
            status.HTTP_201_CREATED,
            status.HTTP_400_BAD_REQUEST,
            status.HTTP_401_UNAUTHORIZED,
            status.HTTP_403_FORBIDDEN
        ])
    
    def test_subscription_search_api_parameters(self):
        """Test subscription search API expects correct parameters."""
        url = reverse('subscription-search')
        response = self.client.get(url, {'student_name': 'test'})
        
        # Should return some response
        self.assertIn(response.status_code, [
            status.HTTP_200_OK,
            status.HTTP_400_BAD_REQUEST,
            status.HTTP_404_NOT_FOUND
        ])


class VerifyPaymentViewTest(APITestCase):
    """Test verify payment view."""
    
    def setUp(self):
        self.client = APIClient()
        self.user = User.objects.create_user(
            username='testuser',
            password='testpass123'
        )
        self.student = Student.objects.create(
            user=self.user,
            phone='1234567890'
        )
        self.package = Package.objects.create(
            name='Test Package',
            price=100.00,
            discount_price=80.00
        )
    
    def test_verify_payment_data_expectations(self):
        """Test verify payment view data expectations."""
        url = reverse('verify_payment')
        data = {
            'razorpay_order_id': 'test_order_id',
            'razorpay_payment_id': 'test_payment_id',
            'razorpay_signature': 'test_signature',
            'subscription_id': 1,
            'amount': 8000  # in paise
        }
        response = self.client.post(url, data, format='json')
        
        # Should return some response
        self.assertIn(response.status_code, [
            status.HTTP_200_OK,
            status.HTTP_400_BAD_REQUEST,
            status.HTTP_401_UNAUTHORIZED,
            status.HTTP_404_NOT_FOUND
        ])


class TestPatternAPITest(APITestCase):
    """Test TestPattern API endpoints."""
    
    def setUp(self):
        self.client = APIClient()
        self.user = User.objects.create_user(
            username='testuser',
            password='testpass123'
        )
        self.contributor = ContributorProfile.objects.create(
            user=self.user,
            role='contributor'
        )
        self.client.force_authenticate(user=self.user)
    
    def test_test_pattern_list_url_exists(self):
        """Test test-pattern-list URL exists."""
        try:
            url = reverse('test-pattern-list-create')
            response = self.client.get(url)
            self.assertNotEqual(response.status_code, 404)
        except:
            self.skipTest("test-pattern-list URL name not found")
    
    def test_test_pattern_detail_url_exists(self):
        """Test test-pattern-detail URL exists."""
        try:
            pattern = TestPattern.objects.create(
                name='Test Pattern',
                contributor=self.contributor,
                sections=[]
            )
            url = reverse('test-pattern-detail', kwargs={'pk': pattern.pattern_id})
            response = self.client.get(url)
            self.assertNotEqual(response.status_code, 404)
        except:
            self.skipTest("test-pattern-detail URL name not found")
    
    def test_test_pattern_permissions(self):
        """Test test pattern permissions."""
        # Test without authentication
        self.client.force_authenticate(user=None)
        try:
            url = reverse('test-pattern-list-create')
            response = self.client.get(url)
            # Should require authentication
            self.assertIn(response.status_code, [
                status.HTTP_401_UNAUTHORIZED,
                status.HTTP_403_FORBIDDEN
            ])
        except:
            self.skipTest("test-pattern-list URL name not found")
