from django.db.models.signals import post_save
from django.dispatch import receiver
from django.contrib.auth.models import User
from .models import Contact
from students.models import Student, Referral
import logging

logger = logging.getLogger(__name__)


@receiver(post_save, sender=Contact)
def process_contact_referrals(sender, instance, created, **kwargs):
    """
    Process referrals when a contact is matched with a registered user.
    This creates referral relationships automatically.
    """
    if instance.is_matched and instance.related_user:
        try:
            # Get the referred student (the matched contact)
            referred_student = Student.objects.get(user=instance.related_user)

            # Get the referrer student (the user who has this contact)
            referrer_student = Student.objects.get(user=instance.user)

            # Check if referral already exists
            existing_referral = Referral.objects.filter(
                referrer=referrer_student,
                referred=referred_student
            ).first()

            if not existing_referral:
                # Create new referral
                referral = Referral.objects.create(
                    referrer=referrer_student,
                    referred=referred_student,
                    referral_code=referrer_student.referral_code or f"REF{referrer_student.id}"
                )

                # Update referrer's count
                referrer_student.refferred_count += 1
                referrer_student.save()

                logger.info(
                    f"New referral created: {referrer_student.user.username} "
                    f"referred {referred_student.user.username} via contact sync"
                )

        except Student.DoesNotExist:
            # One of the users is not a student, skip referral processing
            pass
        except Exception as e:
            logger.error(f"Error processing referral for contact {instance.id}: {str(e)}")
