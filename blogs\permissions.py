from rest_framework.permissions import BasePermission
from rest_framework.exceptions import PermissionDenied

from customrcare.models import CustomrcareProfile
from contributor.models import ContributorProfile

class IsBlogPostUser(BasePermission):
    def has_permission(self, request, view):
        user = request.user

        if user.is_authenticated:
            try:
                if ContributorProfile.objects.filter(user=user).exists():
                    return True
                else:
                    customr_care = CustomrcareProfile.objects.get(user=user)
                    if customr_care.role == "customrcare":
                        return True
                raise PermissionDenied(
                    detail="You do not have permission for this resource."
                )
            except CustomrcareProfile.DoesNotExist:
                raise PermissionDenied(detail="Customrcare profile not found.")
        raise PermissionDenied(detail="Authentication required.")
    
