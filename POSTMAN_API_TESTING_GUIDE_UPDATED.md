# 📋 Updated Postman API Testing Guide - Contact Management System

## 🚀 Base URL
```
http://localhost:8000
```

## 🔐 Authentication Setup

### Step 1: Get JWT Token
**Method:** `POST`  
**URL:** `http://localhost:8000/api/students/login/`  
**Headers:**
```json
{
  "Content-Type": "application/json"
}
```
**Body (raw JSON):**
```json
{
  "username": "your_username",
  "password": "your_password"
}
```

**Response:**
```json
{
  "student": {...},
  "JWT_Token": {
    "refresh": "...",
    "access": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
  }
}
```

Copy the `access` token for use in all subsequent requests.

---

## 📱 API Endpoints for Testing

### 1. 📤 Bulk Contact Upload

**Method:** `POST`  
**URL:** `http://localhost:8000/api/contacts/upload/`  
**Headers:**
```json
{
  "Content-Type": "application/json",
  "Authorization": "Bearer YOUR_JWT_ACCESS_TOKEN_HERE"
}
```
**Body (raw J<PERSON><PERSON>):**
```json
{
  "contacts": [
    {
      "name": "John Doe",
      "contact": "9876543210"
    },
    {
      "name": "Jane Smith", 
      "contact": "9876543211"
    },
    {
      "name": "Bob Johnson",
      "contact": "9876543212"
    },
    {
      "name": "Alice Brown",
      "contact": "9876543213"
    },
    {
      "name": "Charlie Wilson",
      "contact": "9876543214"
    }
  ]
}
```

**✅ Expected Success Response:**
```json
{
  "success": true,
  "message": "Successfully uploaded 5 contacts",
  "data": {
    "contacts_uploaded": 5,
    "relationships_created": 5
  }
}
```

---

### 2. 📋 Get User's Uploaded Contacts

**Method:** `GET`  
**URL:** `http://localhost:8000/api/contacts/my-contacts/`  
**Headers:**
```json
{
  "Authorization": "Bearer YOUR_JWT_ACCESS_TOKEN_HERE"
}
```

**✅ Expected Response:**
```json
{
  "count": 5,
  "next": null,
  "previous": null,
  "results": [
    {
      "id": 1,
      "name": "John Doe",
      "contact_number": "9876543210",
      "created_at": "2025-06-24T16:33:59.653989+05:30",
      "updated_at": "2025-06-24T16:33:59.654006+05:30"
    }
  ]
}
```

---

### 3. 🤝 Get Contact Relationships

**Method:** `GET`  
**URL:** `http://localhost:8000/api/contacts/relationships/`  
**Headers:**
```json
{
  "Authorization": "Bearer YOUR_JWT_ACCESS_TOKEN_HERE"
}
```

**Query Parameters (Optional):**
- `?type=friend` - Filter by relationship type
- `?registered_only=true` - Show only registered users

**✅ Expected Response:**
```json
{
  "count": 5,
  "next": null,
  "previous": null,
  "results": [
    {
      "id": 10,
      "contact_info": {
        "id": 1,
        "name": "John Doe",
        "contact_number": "9876543210",
        "is_active": true,
        "is_registered_user": false,
        "registered_user_info": null
      },
      "relationship_type": "contact",
      "is_active": true,
      "created_at": "2025-06-24T16:33:59.653989+05:30",
      "updated_at": "2025-06-24T16:33:59.654006+05:30"
    }
  ]
}
```

---

### 4. 🔍 Get Specific Relationship

**Method:** `GET`  
**URL:** `http://localhost:8000/api/contacts/relationships/{relationship_id}/`  
**Example:** `http://localhost:8000/api/contacts/relationships/10/`  
**Headers:**
```json
{
  "Authorization": "Bearer YOUR_JWT_ACCESS_TOKEN_HERE"
}
```

**✅ Expected Response:**
```json
{
  "success": true,
  "data": {
    "id": 10,
    "contact_info": {
      "id": 1,
      "name": "John Doe",
      "contact_number": "9876543210",
      "is_active": true,
      "is_registered_user": false,
      "registered_user_info": null
    },
    "relationship_type": "contact",
    "is_active": true,
    "created_at": "2025-06-24T16:33:59.653989+05:30",
    "updated_at": "2025-06-24T16:33:59.654006+05:30"
  }
}
```

---

### 5. ✏️ Update Contact Relationship

**Method:** `PATCH`  
**URL:** `http://localhost:8000/api/contacts/relationships/{relationship_id}/`  
**Example:** `http://localhost:8000/api/contacts/relationships/10/`  
**Headers:**
```json
{
  "Content-Type": "application/json",
  "Authorization": "Bearer YOUR_JWT_ACCESS_TOKEN_HERE"
}
```
**Body (raw JSON):**
```json
{
  "relationship_type": "friend"
}
```

**Available relationship types:**
- `"contact"` (default)
- `"friend"`
- `"mutual"`
- `"blocked"`

**✅ Expected Success Response:**
```json
{
  "success": true,
  "message": "Relationship updated successfully",
  "data": {
    "id": 10,
    "contact_info": {
      "id": 1,
      "name": "John Doe",
      "contact_number": "9876543210",
      "is_active": true,
      "is_registered_user": false,
      "registered_user_info": null
    },
    "relationship_type": "friend",
    "is_active": true,
    "created_at": "2025-06-24T16:33:59.653989+05:30",
    "updated_at": "2025-06-24T16:34:28.193114+05:30"
  }
}
```

---

### 6. 🔄 Update Contact Status (Active/Inactive)

**Method:** `PATCH`  
**URL:** `http://localhost:8000/api/contacts/status/{contact_id}/`  
**Example:** `http://localhost:8000/api/contacts/status/1/`  
**Headers:**
```json
{
  "Content-Type": "application/json",
  "Authorization": "Bearer YOUR_JWT_ACCESS_TOKEN_HERE"
}
```
**Body (raw JSON):**
```json
{
  "is_active": false
}
```

**✅ Expected Success Response:**
```json
{
  "success": true,
  "message": "Contact status updated to inactive",
  "data": {
    "contact_id": 1,
    "is_active": false
  }
}
```

**Note:** When a contact is set to inactive, it will no longer appear in the active relationships list.

---

### 7. 🔍 Search Contacts

**Method:** `GET`  
**URL:** `http://localhost:8000/api/contacts/search/`  
**Headers:**
```json
{
  "Authorization": "Bearer YOUR_JWT_ACCESS_TOKEN_HERE"
}
```

**Query Parameters (at least one required):**
- `?query=John` - Search by name or number
- `?contact_number=9876543210` - Exact contact number
- `?is_registered=true` - Filter by registration status
- `?relationship_type=friend` - Filter by relationship type

**Example URLs:**
- `http://localhost:8000/api/contacts/search/?query=John`
- `http://localhost:8000/api/contacts/search/?contact_number=9876543210`
- `http://localhost:8000/api/contacts/search/?relationship_type=friend`

---

### 8. 📊 Get Contact Statistics

**Method:** `GET`  
**URL:** `http://localhost:8000/api/contacts/stats/`  
**Headers:**
```json
{
  "Authorization": "Bearer YOUR_JWT_ACCESS_TOKEN_HERE"
}
```

**✅ Expected Response:**
```json
{
  "success": true,
  "data": {
    "total_contacts_uploaded": 5,
    "active_relationships": 4,
    "mutual_contacts": 0,
    "registered_friends": 0,
    "last_sync": "2025-06-24T16:33:59.666758+05:30"
  }
}
```

---

### 9. 👥 Get Mutual Contacts

**Method:** `GET`  
**URL:** `http://localhost:8000/api/contacts/mutual/`  
**Headers:**
```json
{
  "Authorization": "Bearer YOUR_JWT_ACCESS_TOKEN_HERE"
}
```

**✅ Expected Response:**
```json
{
  "count": 0,
  "next": null,
  "previous": null,
  "results": []
}
```

---

## ❌ Error Testing Examples

### 1. Unauthorized Access
**Method:** `GET`  
**URL:** `http://localhost:8000/api/contacts/my-contacts/`  
**Headers:** None

**Expected Response:**
```json
{
  "detail": "Authentication credentials were not provided."
}
```

### 2. Invalid Contact Data
**Method:** `POST`  
**URL:** `http://localhost:8000/api/contacts/upload/`  
**Body:**
```json
{
  "contacts": [
    {
      "name": "Invalid Contact",
      "contact": "123"
    }
  ]
}
```

**Expected Response:**
```json
{
  "success": false,
  "message": "Invalid data provided",
  "errors": {
    "contacts": [
      {
        "contact": ["Contact number must be between 10 and 15 digits"]
      }
    ]
  }
}
```

---

## 🛠️ Postman Setup Tips

### Environment Variables
Create these variables in Postman:
- `base_url`: `http://localhost:8000`
- `jwt_token`: Your JWT access token

### Pre-request Script for Auto Token Setting
```javascript
pm.test("Set JWT Token", function () {
    var jsonData = pm.response.json();
    if (jsonData.JWT_Token && jsonData.JWT_Token.access) {
        pm.environment.set("jwt_token", jsonData.JWT_Token.access);
    }
});
```

### Test Scripts for Validation
```javascript
pm.test("Status code is 200 or 201", function () {
    pm.expect(pm.response.code).to.be.oneOf([200, 201]);
});

pm.test("Response has success field", function () {
    var jsonData = pm.response.json();
    pm.expect(jsonData).to.have.property('success');
});
```

---

## ✅ Testing Workflow

1. **Login** → Get JWT token
2. **Upload Contacts** → Bulk upload test data
3. **View Relationships** → Get relationship IDs
4. **Update Relationship** → Change type to "friend"
5. **Update Status** → Set contact to inactive
6. **Verify Changes** → Check that inactive contacts don't appear in active list
7. **Search & Stats** → Test search and statistics endpoints

All endpoints are now fully functional and tested! 🎉
