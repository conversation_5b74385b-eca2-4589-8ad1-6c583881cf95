from django.db import models
from django.contrib.auth.models import User
from django.utils import timezone
from django.core.validators import MinValueValidator, MaxValueValidator
import json
# Create your models here.

class LogConfig(models.Model):
    """Enhanced configuration for logging system with dynamic management"""

    LOGGING_LEVELS = [
        ("DEBUG", "DEBUG - Detailed information for debugging"),
        ("INFO", "INFO - General information messages"),
        ("WARNING", "WARNING - Warning messages"),
        ("ERROR", "ERROR - Error messages only"),
        ("CRITICAL", "CRITICAL - Critical errors only"),
    ]

    RETENTION_POLICIES = [
        (1, "1 Day"),
        (7, "1 Week"),
        (30, "1 Month"),
        (90, "3 Months"),
        (180, "6 Months"),
        (365, "1 Year"),
    ]

    # Basic Configuration
    name = models.CharField(max_length=100, default="Default Configuration")
    description = models.TextField(blank=True, help_text="Description of this configuration")
    is_active = models.BooleanField(default=True, help_text="Whether this configuration is currently active")

    # Logging Levels
    level = models.CharField(
        max_length=10,
        choices=LOGGING_LEVELS,
        default="INFO",
        help_text="Minimum logging level to capture"
    )

    # Output Configuration
    enable_file_logging = models.BooleanField(
        default=True,
        help_text="Enable logging to files"
    )
    enable_console_logging = models.BooleanField(
        default=True,
        help_text="Enable logging to console"
    )
    enable_database_logging = models.BooleanField(
        default=True,
        help_text="Enable logging to database"
    )

    # Feature Toggles
    enable_performance_logging = models.BooleanField(
        default=True,
        help_text="Track request performance and response times"
    )
    enable_error_logging = models.BooleanField(
        default=True,
        help_text="Log application errors and exceptions"
    )
    enable_activity_logging = models.BooleanField(
        default=True,
        help_text="Track user activities and actions"
    )
    enable_api_logging = models.BooleanField(
        default=True,
        help_text="Log API access and usage"
    )
    enable_db_logging = models.BooleanField(
        default=False,
        help_text="Log database queries (can impact performance)"
    )
    enable_auth_logging = models.BooleanField(
        default=True,
        help_text="Log authentication events and security"
    )
    enable_security_logging = models.BooleanField(
        default=True,
        help_text="Log security incidents and threats"
    )
    enable_health_logging = models.BooleanField(
        default=True,
        help_text="Log system health metrics"
    )

    # Performance Settings
    max_log_entries = models.IntegerField(
        default=10000,
        validators=[MinValueValidator(100), MaxValueValidator(1000000)],
        help_text="Maximum number of log entries to keep in memory"
    )
    batch_size = models.IntegerField(
        default=100,
        validators=[MinValueValidator(10), MaxValueValidator(1000)],
        help_text="Number of log entries to process in batches"
    )
    flush_interval = models.IntegerField(
        default=60,
        validators=[MinValueValidator(1), MaxValueValidator(3600)],
        help_text="Interval in seconds to flush logs to storage"
    )

    # Retention Policies
    log_retention_days = models.IntegerField(
        default=30,
        choices=RETENTION_POLICIES,
        help_text="Number of days to retain logs"
    )
    error_retention_days = models.IntegerField(
        default=90,
        choices=RETENTION_POLICIES,
        help_text="Number of days to retain error logs (usually longer)"
    )
    security_retention_days = models.IntegerField(
        default=180,
        choices=RETENTION_POLICIES,
        help_text="Number of days to retain security logs (usually longest)"
    )

    # Alert Configuration
    enable_email_alerts = models.BooleanField(
        default=False,
        help_text="Send email alerts for critical issues"
    )
    alert_email_addresses = models.TextField(
        blank=True,
        help_text="Comma-separated list of email addresses for alerts"
    )
    critical_error_threshold = models.IntegerField(
        default=5,
        validators=[MinValueValidator(1)],
        help_text="Number of critical errors before sending alert"
    )
    alert_cooldown_minutes = models.IntegerField(
        default=60,
        validators=[MinValueValidator(1)],
        help_text="Minutes to wait before sending another alert"
    )

    # Advanced Settings
    enable_log_compression = models.BooleanField(
        default=True,
        help_text="Compress old log files to save space"
    )
    enable_log_rotation = models.BooleanField(
        default=True,
        help_text="Rotate log files when they reach size limit"
    )
    max_log_file_size_mb = models.IntegerField(
        default=100,
        validators=[MinValueValidator(1), MaxValueValidator(1000)],
        help_text="Maximum size of log files in MB before rotation"
    )

    # Sampling Configuration
    enable_sampling = models.BooleanField(
        default=False,
        help_text="Enable log sampling for high-traffic scenarios"
    )
    sampling_rate = models.FloatField(
        default=1.0,
        validators=[MinValueValidator(0.01), MaxValueValidator(1.0)],
        help_text="Sampling rate (0.01 = 1%, 1.0 = 100%)"
    )

    # Metadata
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    created_by = models.ForeignKey(
        User,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='created_log_configs'
    )
    updated_by = models.ForeignKey(
        User,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='updated_log_configs'
    )

    class Meta:
        verbose_name = "Log Configuration"
        verbose_name_plural = "Log Configurations"
        ordering = ['-is_active', '-updated_at']

    def __str__(self):
        status = "Active" if self.is_active else "Inactive"
        return f"{self.name} - {self.level} ({status})"

    def save(self, *args, **kwargs):
        # Ensure only one active configuration
        if self.is_active:
            LogConfig.objects.filter(is_active=True).exclude(pk=self.pk).update(is_active=False)

        super().save(*args, **kwargs)

        # Clear cache to force reload
        from django.core.cache import cache
        cache.delete('log_config')

    @classmethod
    def get_active_config(cls):
        """Get the currently active configuration"""
        from django.core.cache import cache

        config = cache.get('active_log_config')
        if not config:
            config = cls.objects.filter(is_active=True).first()
            if not config:
                # Create default configuration if none exists
                config = cls.objects.create(
                    name="Default Configuration",
                    description="Automatically created default configuration"
                )
            cache.set('active_log_config', config, timeout=300)  # Cache for 5 minutes

        return config

    def get_alert_emails(self):
        """Get list of alert email addresses"""
        if not self.alert_email_addresses:
            return []
        return [email.strip() for email in self.alert_email_addresses.split(',') if email.strip()]

    def should_log_level(self, level):
        """Check if a log level should be logged based on current configuration"""
        level_hierarchy = {
            'DEBUG': 0,
            'INFO': 1,
            'WARNING': 2,
            'ERROR': 3,
            'CRITICAL': 4
        }

        current_level = level_hierarchy.get(self.level, 1)
        check_level = level_hierarchy.get(level, 1)

        return check_level >= current_level

    def get_retention_days_for_type(self, log_type):
        """Get retention days for specific log type"""
        if log_type in ['error', 'exception']:
            return self.error_retention_days
        elif log_type in ['security', 'auth', 'authentication']:
            return self.security_retention_days
        else:
            return self.log_retention_days

class PerformanceLog(models.Model):
    """Enhanced performance logging"""
    path = models.CharField(max_length=500)
    method = models.CharField(max_length=10, default='GET')
    duration = models.FloatField()
    memory_usage = models.FloatField(null=True, blank=True)  # MB
    cpu_usage = models.FloatField(null=True, blank=True)  # Percentage
    db_queries_count = models.IntegerField(default=0)
    db_queries_time = models.FloatField(default=0.0)
    user = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, blank=True)
    user_agent = models.TextField(blank=True)
    ip_address = models.GenericIPAddressField(null=True, blank=True)
    response_size = models.IntegerField(null=True, blank=True)  # bytes
    status_code = models.IntegerField(null=True, blank=True)
    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        verbose_name = "Performance Log"
        verbose_name_plural = "Performance Logs"
        indexes = [
            models.Index(fields=['path', 'created_at']),
            models.Index(fields=['user', 'created_at']),
            models.Index(fields=['duration']),
        ]

    def __str__(self):
        return f"{self.method} {self.path} - {self.duration:.3f}s"

class ErrorLog(models.Model):
    """Enhanced error logging"""
    ERROR_TYPES = [
        ('VALIDATION', 'Validation Error'),
        ('PERMISSION', 'Permission Error'),
        ('NOT_FOUND', 'Not Found Error'),
        ('SERVER', 'Server Error'),
        ('DATABASE', 'Database Error'),
        ('EXTERNAL_API', 'External API Error'),
        ('AUTHENTICATION', 'Authentication Error'),
        ('BUSINESS_LOGIC', 'Business Logic Error'),
        ('UNKNOWN', 'Unknown Error'),
    ]

    SEVERITY_LEVELS = [
        ('LOW', 'Low'),
        ('MEDIUM', 'Medium'),
        ('HIGH', 'High'),
        ('CRITICAL', 'Critical'),
    ]

    view_name = models.CharField(max_length=255)
    error_type = models.CharField(max_length=20, choices=ERROR_TYPES, default='UNKNOWN')
    severity = models.CharField(max_length=10, choices=SEVERITY_LEVELS, default='MEDIUM')
    error_message = models.TextField()
    stack_trace = models.TextField()
    request_data = models.JSONField(default=dict, blank=True)
    response_data = models.JSONField(default=dict, blank=True)
    user = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, blank=True)
    ip_address = models.GenericIPAddressField(null=True, blank=True)
    user_agent = models.TextField(blank=True)
    path = models.CharField(max_length=500, blank=True)
    method = models.CharField(max_length=10, blank=True)
    status_code = models.IntegerField(null=True, blank=True)
    resolved = models.BooleanField(default=False)
    resolved_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, blank=True, related_name='resolved_errors')
    resolved_at = models.DateTimeField(null=True, blank=True)
    resolution_notes = models.TextField(blank=True)
    timestamp = models.DateTimeField(auto_now_add=True)

    class Meta:
        verbose_name = "Error Log"
        verbose_name_plural = "Error Logs"
        indexes = [
            models.Index(fields=['error_type', 'timestamp']),
            models.Index(fields=['severity', 'timestamp']),
            models.Index(fields=['resolved', 'timestamp']),
            models.Index(fields=['user', 'timestamp']),
        ]

    def __str__(self):
        return f"{self.error_type} - {self.view_name} ({self.severity})"

class UserActivity(models.Model):
    """Enhanced user activity logging"""
    ACTIVITY_TYPES = [
        ('LOGIN', 'User Login'),
        ('LOGOUT', 'User Logout'),
        ('REGISTER', 'User Registration'),
        ('PASSWORD_CHANGE', 'Password Change'),
        ('PROFILE_UPDATE', 'Profile Update'),
        ('API_CALL', 'API Call'),
        ('FILE_UPLOAD', 'File Upload'),
        ('FILE_DOWNLOAD', 'File Download'),
        ('PAYMENT', 'Payment Transaction'),
        ('SUBSCRIPTION', 'Subscription Action'),
        ('QUESTION_SUBMIT', 'Question Submission'),
        ('TEST_ATTEMPT', 'Test Attempt'),
        ('ADMIN_ACTION', 'Admin Action'),
        ('SECURITY_EVENT', 'Security Event'),
        ('OTHER', 'Other Activity'),
    ]

    user = models.ForeignKey(User, on_delete=models.CASCADE)
    activity_type = models.CharField(max_length=20, choices=ACTIVITY_TYPES, default='OTHER')
    action = models.CharField(max_length=200)
    description = models.TextField(blank=True)
    ip_address = models.GenericIPAddressField(null=True, blank=True)
    user_agent = models.TextField(blank=True)
    session_key = models.CharField(max_length=40, blank=True, null=True)
    path = models.CharField(max_length=500, blank=True)
    method = models.CharField(max_length=10, blank=True)
    success = models.BooleanField(default=True)
    metadata = models.JSONField(default=dict)
    timestamp = models.DateTimeField(auto_now_add=True)

    class Meta:
        verbose_name = "User Activity"
        verbose_name_plural = "User Activities"
        indexes = [
            models.Index(fields=['user', 'timestamp']),
            models.Index(fields=['activity_type', 'timestamp']),
            models.Index(fields=['success', 'timestamp']),
            models.Index(fields=['session_key']),
        ]

    def __str__(self):
        return f"{self.user.username} - {self.activity_type} - {self.action}"


class APIAccessLog(models.Model):
    """API access and usage logging"""
    endpoint = models.CharField(max_length=500)
    method = models.CharField(max_length=10)
    user = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, blank=True)
    ip_address = models.GenericIPAddressField(null=True, blank=True)
    user_agent = models.TextField(blank=True)
    request_headers = models.JSONField(default=dict, blank=True)
    request_body = models.JSONField(default=dict, blank=True)
    response_status = models.IntegerField()
    response_size = models.IntegerField(null=True, blank=True)  # bytes
    response_time = models.FloatField()  # seconds
    api_key = models.CharField(max_length=100, blank=True)
    rate_limited = models.BooleanField(default=False)
    cached_response = models.BooleanField(default=False)
    timestamp = models.DateTimeField(auto_now_add=True)

    class Meta:
        verbose_name = "API Access Log"
        verbose_name_plural = "API Access Logs"
        indexes = [
            models.Index(fields=['endpoint', 'timestamp']),
            models.Index(fields=['user', 'timestamp']),
            models.Index(fields=['ip_address', 'timestamp']),
            models.Index(fields=['response_status', 'timestamp']),
        ]

    def __str__(self):
        return f"{self.method} {self.endpoint} - {self.response_status} ({self.response_time:.3f}s)"


class DatabaseQueryLog(models.Model):
    """Database query performance logging"""
    query_type = models.CharField(max_length=20)  # SELECT, INSERT, UPDATE, DELETE
    table_name = models.CharField(max_length=100, blank=True)
    query_hash = models.CharField(max_length=64)  # MD5 hash of query
    execution_time = models.FloatField()  # seconds
    rows_affected = models.IntegerField(null=True, blank=True)
    query_plan = models.TextField(blank=True)
    user = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, blank=True)
    view_name = models.CharField(max_length=255, blank=True)
    slow_query = models.BooleanField(default=False)  # queries > 1 second
    timestamp = models.DateTimeField(auto_now_add=True)

    class Meta:
        verbose_name = "Database Query Log"
        verbose_name_plural = "Database Query Logs"
        indexes = [
            models.Index(fields=['query_type', 'timestamp']),
            models.Index(fields=['table_name', 'timestamp']),
            models.Index(fields=['slow_query', 'timestamp']),
            models.Index(fields=['execution_time']),
        ]

    def __str__(self):
        return f"{self.query_type} on {self.table_name} - {self.execution_time:.3f}s"


class AuthenticationLog(models.Model):
    """Authentication and security event logging"""
    AUTH_EVENTS = [
        ('LOGIN_SUCCESS', 'Successful Login'),
        ('LOGIN_FAILED', 'Failed Login'),
        ('LOGOUT', 'User Logout'),
        ('PASSWORD_CHANGE', 'Password Changed'),
        ('PASSWORD_RESET', 'Password Reset'),
        ('ACCOUNT_LOCKED', 'Account Locked'),
        ('ACCOUNT_UNLOCKED', 'Account Unlocked'),
        ('TOKEN_REFRESH', 'Token Refreshed'),
        ('TOKEN_EXPIRED', 'Token Expired'),
        ('SUSPICIOUS_ACTIVITY', 'Suspicious Activity'),
        ('BRUTE_FORCE', 'Brute Force Attempt'),
        ('INVALID_TOKEN', 'Invalid Token'),
        ('PERMISSION_DENIED', 'Permission Denied'),
    ]

    user = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, blank=True)
    username_attempted = models.CharField(max_length=150, blank=True)
    event_type = models.CharField(max_length=20, choices=AUTH_EVENTS)
    ip_address = models.GenericIPAddressField(null=True, blank=True)
    user_agent = models.TextField(blank=True)
    session_key = models.CharField(max_length=40, blank=True, null=True)
    success = models.BooleanField()
    failure_reason = models.CharField(max_length=255, blank=True)
    additional_data = models.JSONField(default=dict, blank=True)
    timestamp = models.DateTimeField(auto_now_add=True)

    class Meta:
        verbose_name = "Authentication Log"
        verbose_name_plural = "Authentication Logs"
        indexes = [
            models.Index(fields=['user', 'timestamp']),
            models.Index(fields=['event_type', 'timestamp']),
            models.Index(fields=['ip_address', 'timestamp']),
            models.Index(fields=['success', 'timestamp']),
        ]

    def __str__(self):
        user_display = self.user.username if self.user else self.username_attempted
        return f"{user_display} - {self.event_type} - {'Success' if self.success else 'Failed'}"


class SystemHealthLog(models.Model):
    """System health and resource monitoring"""
    HEALTH_METRICS = [
        ('CPU_USAGE', 'CPU Usage'),
        ('MEMORY_USAGE', 'Memory Usage'),
        ('DISK_USAGE', 'Disk Usage'),
        ('DATABASE_CONNECTIONS', 'Database Connections'),
        ('ACTIVE_SESSIONS', 'Active Sessions'),
        ('RESPONSE_TIME', 'Average Response Time'),
        ('ERROR_RATE', 'Error Rate'),
        ('THROUGHPUT', 'Request Throughput'),
    ]

    metric_type = models.CharField(max_length=20, choices=HEALTH_METRICS)
    value = models.FloatField()
    unit = models.CharField(max_length=20)  # %, MB, seconds, count, etc.
    threshold_warning = models.FloatField(null=True, blank=True)
    threshold_critical = models.FloatField(null=True, blank=True)
    status = models.CharField(max_length=10, choices=[
        ('OK', 'OK'),
        ('WARNING', 'Warning'),
        ('CRITICAL', 'Critical'),
    ], default='OK')
    additional_info = models.JSONField(default=dict, blank=True)
    timestamp = models.DateTimeField(auto_now_add=True)

    class Meta:
        verbose_name = "System Health Log"
        verbose_name_plural = "System Health Logs"
        indexes = [
            models.Index(fields=['metric_type', 'timestamp']),
            models.Index(fields=['status', 'timestamp']),
        ]

    def __str__(self):
        return f"{self.metric_type}: {self.value}{self.unit} ({self.status})"


class SecurityIncident(models.Model):
    """Security incident tracking"""
    INCIDENT_TYPES = [
        ('BRUTE_FORCE', 'Brute Force Attack'),
        ('SQL_INJECTION', 'SQL Injection Attempt'),
        ('XSS_ATTEMPT', 'XSS Attempt'),
        ('CSRF_ATTACK', 'CSRF Attack'),
        ('SUSPICIOUS_UPLOAD', 'Suspicious File Upload'),
        ('RATE_LIMIT_EXCEEDED', 'Rate Limit Exceeded'),
        ('UNAUTHORIZED_ACCESS', 'Unauthorized Access Attempt'),
        ('DATA_BREACH', 'Potential Data Breach'),
        ('MALICIOUS_REQUEST', 'Malicious Request'),
        ('OTHER', 'Other Security Incident'),
    ]

    SEVERITY_LEVELS = [
        ('LOW', 'Low'),
        ('MEDIUM', 'Medium'),
        ('HIGH', 'High'),
        ('CRITICAL', 'Critical'),
    ]

    incident_type = models.CharField(max_length=20, choices=INCIDENT_TYPES)
    severity = models.CharField(max_length=10, choices=SEVERITY_LEVELS)
    description = models.TextField()
    ip_address = models.GenericIPAddressField(null=True, blank=True)
    user_agent = models.TextField(blank=True)
    user = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, blank=True)
    path = models.CharField(max_length=500, blank=True)
    request_data = models.JSONField(default=dict, blank=True)
    blocked = models.BooleanField(default=False)
    resolved = models.BooleanField(default=False)
    resolved_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, blank=True, related_name='resolved_incidents')
    resolved_at = models.DateTimeField(null=True, blank=True)
    resolution_notes = models.TextField(blank=True)
    timestamp = models.DateTimeField(auto_now_add=True)

    class Meta:
        verbose_name = "Security Incident"
        verbose_name_plural = "Security Incidents"
        indexes = [
            models.Index(fields=['incident_type', 'timestamp']),
            models.Index(fields=['severity', 'timestamp']),
            models.Index(fields=['ip_address', 'timestamp']),
            models.Index(fields=['resolved', 'timestamp']),
        ]

    def __str__(self):
        return f"{self.incident_type} - {self.severity} - {self.ip_address}"


