#!/usr/bin/env python3
"""
Test script to verify API endpoints work correctly with image uploads.
"""

import os
import sys
import django
from django.core.files.uploadedfile import SimpleUploadedFile
from PIL import Image
import io
import requests
import json

# Add the project directory to Python path
sys.path.insert(0, '/Users/<USER>/Documents/code/shash_b')

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'shashtrarth.settings')
django.setup()

# Override ALLOWED_HOSTS for testing
from django.conf import settings
if 'testserver' not in settings.ALLOWED_HOSTS:
    settings.ALLOWED_HOSTS.append('testserver')

from django.contrib.auth.models import User
from django.test import Client
from rest_framework.test import APIClient
from rest_framework import status
from questions.models import Question, Option, MasterQuestion, MasterOption, Subject, Topic, SubTopic, Course, SubCourse
from contributor.models import ContributorProfile

def create_test_image(name='test_image.jpg', format='JPEG'):
    """Create a test image file."""
    image = Image.new('RGB', (100, 100), color='red')
    image_file = io.BytesIO()
    image.save(image_file, format=format)
    image_file.seek(0)
    return SimpleUploadedFile(
        name=name,
        content=image_file.getvalue(),
        content_type=f'image/{format.lower()}'
    )

def test_question_api_with_images():
    """Test question creation via API with image attachments."""
    
    print("🔍 Testing Question API with Images...")
    
    # Set up API client
    client = APIClient()
    
    # Create test user and contributor
    user, created = User.objects.get_or_create(
        username='apitest',
        defaults={
            'email': '<EMAIL>',
            'first_name': 'API',
            'last_name': 'Test'
        }
    )
    
    contributor, created = ContributorProfile.objects.get_or_create(
        user=user,
        defaults={'role': 'contributor'}
    )
    
    # Create test data
    subject, created = Subject.objects.get_or_create(name='API Test Subject')
    topic, created = Topic.objects.get_or_create(subject=subject, name='API Test Topic')
    subtopic, created = SubTopic.objects.get_or_create(topic=topic, name='API Test SubTopic')
    course, created = Course.objects.get_or_create(name='API Test Course')
    subcourse, created = SubCourse.objects.get_or_create(course=course, name='API Test SubCourse')
    
    # Authenticate user
    client.force_authenticate(user=user)
    
    # Create test images
    question_image = create_test_image('api_question.jpg')
    explanation_image = create_test_image('api_explanation.jpg')
    reason_image = create_test_image('api_reason.jpg')
    
    # Prepare question data
    question_data = {
        'content': 'API Test question with image attachments',
        'difficulty': 1,
        'author': contributor.id,
        'subject': [subject.subject_id],
        'topic': [topic.topic_id],
        'sub_topic': [subtopic.subtopic_id],
        'course': [course.course_id],
        'subcourse': [subcourse.subcourse_id],
        'explanation': 'API Test explanation with image',
        'reason': 'API Test reason with image',
        'attachments': question_image,
        'explanation_attachment': explanation_image,
        'reason_document': reason_image,
    }
    
    # Test question creation
    response = client.post('/api/questions/questions/', question_data, format='multipart')

    if response.status_code == 201:
        print(f"✅ Question created successfully via API")
        question_data = response.data
        print(f"  📝 Question ID: {question_data.get('question_id')}")
        print(f"  📎 Attachments field present: {'attachments' in question_data}")
        print(f"  📎 Explanation attachment field present: {'explanation_attachment' in question_data}")
        print(f"  📎 Reason document field present: {'reason_document' in question_data}")

        # Test option creation for this question
        question_slug = question_data.get('slug')
        if question_slug:
            option_image = create_test_image('api_option.jpg')
            option_data = {
                'option_text': 'API Test option with image',
                'is_correct': True,
                'attachments': option_image,
            }

            option_response = client.post(f'/api/questions/{question_slug}/options/', option_data, format='multipart')

            if option_response.status_code == 201:
                print(f"✅ Option created successfully via API")
                option_data = option_response.data
                print(f"  📝 Option ID: {option_data.get('option_id')}")
                print(f"  📎 Attachments field present: {'attachments' in option_data}")
            else:
                print(f"❌ Option creation failed: {option_response.status_code}")
                try:
                    print(f"  Error: {option_response.data}")
                except:
                    print(f"  Error: {option_response.content}")

    else:
        print(f"❌ Question creation failed: {response.status_code}")
        try:
            print(f"  Error: {response.data}")
        except:
            print(f"  Error: {response.content}")

def test_master_question_api_with_images():
    """Test master question creation via API with image attachments."""
    
    print("\n🔍 Testing Master Question API with Images...")
    
    # Set up API client
    client = APIClient()
    
    # Create test user and contributor
    user, created = User.objects.get_or_create(
        username='masterapitest',
        defaults={
            'email': '<EMAIL>',
            'first_name': 'Master API',
            'last_name': 'Test'
        }
    )
    
    contributor, created = ContributorProfile.objects.get_or_create(
        user=user,
        defaults={'role': 'contributor'}
    )
    
    # Authenticate user
    client.force_authenticate(user=user)
    
    # Create test images
    master_image = create_test_image('api_master_question.jpg')
    master_reason_image = create_test_image('api_master_reason.jpg')
    
    # Prepare master question data
    master_question_data = {
        'title': 'API Test Master Question',
        'passage_content': 'This is an API test passage for master question',
        'author': contributor.id,
        'attachments': master_image,
        'reason': 'API Test reason for master question',
        'reason_document': master_reason_image,
    }
    
    # Test master question creation
    response = client.post('/api/questions/master-questions/', master_question_data, format='multipart')
    
    if response.status_code == 201:
        print(f"✅ Master Question created successfully via API")
        master_question_data = response.data
        print(f"  📝 Master Question ID: {master_question_data.get('master_question_id')}")
        print(f"  📎 Attachments field present: {'attachments' in master_question_data}")
        print(f"  📎 Reason document field present: {'reason_document' in master_question_data}")
    else:
        print(f"❌ Master Question creation failed: {response.status_code}")
        print(f"  Error: {response.data}")

def test_master_option_api_with_images():
    """Test master option creation via API with image attachments."""
    
    print("\n🔍 Testing Master Option API with Images...")
    
    # Set up API client
    client = APIClient()
    
    # Create test user and contributor
    user, created = User.objects.get_or_create(
        username='masteroptionapitest',
        defaults={
            'email': '<EMAIL>',
            'first_name': 'Master Option API',
            'last_name': 'Test'
        }
    )
    
    contributor, created = ContributorProfile.objects.get_or_create(
        user=user,
        defaults={'role': 'contributor'}
    )
    
    # Authenticate user
    client.force_authenticate(user=user)
    
    # Create test images
    master_option_image = create_test_image('api_master_option.jpg')
    master_option_reason_image = create_test_image('api_master_option_reason.jpg')
    
    # Prepare master option data
    master_option_data = {
        'title': 'API Test Master Option',
        'option_content': 'This is an API test option content for master option',
        'conditions': 'API Test conditions for master option',
        'author': contributor.id,
        'attachments': master_option_image,
        'reason': 'API Test reason for master option',
        'reason_document': master_option_reason_image,
    }
    
    # Test master option creation
    response = client.post('/api/questions/master-options/', master_option_data, format='multipart')
    
    if response.status_code == 201:
        print(f"✅ Master Option created successfully via API")
        master_option_data = response.data
        print(f"  📝 Master Option ID: {master_option_data.get('master_option_id')}")
        print(f"  📎 Attachments field present: {'attachments' in master_option_data}")
        print(f"  📎 Reason document field present: {'reason_document' in master_option_data}")
    else:
        print(f"❌ Master Option creation failed: {response.status_code}")
        print(f"  Error: {response.data}")

def test_serializer_response_format():
    """Test that API responses include image URLs properly formatted."""
    
    print("\n🔍 Testing Serializer Response Format...")
    
    # Set up API client
    client = APIClient()
    
    # Get an existing question with attachments
    questions_with_attachments = Question.objects.filter(attachments__isnull=False)[:1]
    
    if questions_with_attachments:
        question = questions_with_attachments[0]
        
        # Test question detail API
        response = client.get(f'/api/questions/questions/{question.slug}/')

        if response.status_code == 200:
            question_data = response.data
            print(f"✅ Question detail API response successful")

            # Check if attachment URLs are properly formatted
            if 'attachments' in question_data and question_data['attachments']:
                print(f"  📎 Question attachment URL: {question_data['attachments']}")

            if 'explanation_attachment' in question_data and question_data['explanation_attachment']:
                print(f"  📎 Explanation attachment URL: {question_data['explanation_attachment']}")

            if 'reason_document' in question_data and question_data['reason_document']:
                print(f"  📎 Reason document URL: {question_data['reason_document']}")

            # Test options for this question
            options_response = client.get(f'/api/questions/{question.slug}/options/')
            if options_response.status_code == 200:
                options_data = options_response.data
                print(f"✅ Options API response successful")

                for option in options_data:
                    if 'attachments' in option and option['attachments']:
                        print(f"  📎 Option attachment URL: {option['attachments']}")
        else:
            print(f"❌ Question detail API failed: {response.status_code}")
    else:
        print("ℹ️ No questions with attachments found for testing")

if __name__ == "__main__":
    print("🚀 Starting API Image Upload Tests...")
    print("=" * 60)
    
    test_question_api_with_images()
    test_master_question_api_with_images()
    test_master_option_api_with_images()
    test_serializer_response_format()
    
    print("\n" + "=" * 60)
    print("✅ API Image Upload Tests Completed!")
