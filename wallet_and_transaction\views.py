from django.db.models import Sum, Count
from rest_framework.decorators import api_view, permission_classes
from rest_framework.permissions import IsA<PERSON><PERSON>icated, IsAdminUser
from rest_framework.response import Response
from .models import Wallet, WithdrawalRequest
from .serializers import WithdrawalRequestSerializer
from django.shortcuts import get_object_or_404
from customrcare.permissions import IsCustomrcareUserOnly
# Create your views here.


@api_view(['GET'])
@permission_classes([IsAuthenticated])
def wallet_dashboard_stats(request):
    # Pending withdrawals
    pending_withdrawals = WithdrawalRequest.objects.filter(status='Pending')
    pending_withdrawals_count = pending_withdrawals.count()
    pending_withdrawals_amount = pending_withdrawals.aggregate(Sum('amount'))['amount__sum'] or 0

    # Settled withdrawals (Approved ones)
    settled_withdrawals = WithdrawalRequest.objects.filter(status='Approved')
    settled_withdrawals_count = settled_withdrawals.count()
    settled_withdrawals_amount = settled_withdrawals.aggregate(Sum('amount'))['amount__sum'] or 0

    # Total wallet balance across all users
    total_wallet_balance = Wallet.objects.aggregate(Sum('balance'))['balance__sum'] or 0

    return Response({
        "pending_withdrawals_count": pending_withdrawals_count,
        "pending_withdrawals_amount": pending_withdrawals_amount,
        "settled_withdrawals_count": settled_withdrawals_count,
        "settled_withdrawals_amount": settled_withdrawals_amount,
        "total_wallet_balance": total_wallet_balance
    })


@api_view(["GET"])
@permission_classes([IsCustomrcareUserOnly])
def pending_withdrawals(request):
    withdrawals = WithdrawalRequest.objects.filter(status="Pending").order_by("-created_at")
    serializer = WithdrawalRequestSerializer(withdrawals, many=True)
    return Response(serializer.data)

@api_view(["POST"])
@permission_classes([IsCustomrcareUserOnly])
def update_withdrawal_status(request, request_id):
    from .models import Transaction, Wallet
    from django.utils import timezone

    status = request.data.get("status")  # Approved or Rejected
    txn_id = request.data.get("transaction_id")  # Optional but useful
    note = request.data.get("note", "Manual UPI Transfer")

    withdrawal = get_object_or_404(WithdrawalRequest, id=request_id)

    if status not in ["Approved", "Rejected"]:
        return Response({"error": "Invalid status"}, status=400)

    if withdrawal.status != "Pending":
        return Response({"error": "Withdrawal already processed"}, status=400)

    if status == "Approved":
        # Create a transaction entry
        txn = Transaction.objects.create(
            wallet=withdrawal.wallet,
            amount=withdrawal.amount,
            is_debit=True,
            note=note,
            status="Completed",
            transaction_id=txn_id or "MANUAL-UPI-" + timezone.now().strftime("%Y%m%d%H%M%S"),
        )
        withdrawal.status = "Approved"
        withdrawal.wallet.save()  # Save the wallet balance
        withdrawal.approved_at = timezone.now()
        withdrawal.transaction = txn
        withdrawal.save()

   
    else:
        withdrawal.status = "Rejected"
        withdrawal.save()

    return Response({"success": f"Withdrawal {status}"})


from django.utils import timezone
@api_view(["POST"])
@permission_classes([IsAuthenticated])
def raise_withdrawal(request):
    user = request.user
    amount = request.data.get("amount")
    upi_id = request.data.get("upi_id")

    if not amount or not upi_id:
        return Response({"error": "Amount and UPI ID are required"}, status=400)

    try:
        amount = int(amount)
    except ValueError:
        return Response({"error": "Amount must be an integer"}, status=400)

    if amount <= 0:
        return Response({"error": "Amount must be greater than 0"}, status=400)

    wallet = Wallet.objects.get(user=user)

    if wallet.balance < amount:
        return Response({"error": "Insufficient wallet balance"}, status=400)

    # Check if there's already a pending request
    if WithdrawalRequest.objects.filter(wallet=wallet, status="Pending").exists():
        return Response({"error": "You already have a pending withdrawal request"}, status=400)

    # Lock balance (optional: subtract upfront if needed)
    wallet.balance -= amount
    wallet.save()
    withdrawal = WithdrawalRequest.objects.create(
        wallet=wallet,
        amount=amount,
        upi_id=upi_id,
        created_at=timezone.now()
    )

    return Response({
        "message": "Withdrawal request submitted",
        "request_id": withdrawal.id,
        "status": withdrawal.status
    }, status=201)