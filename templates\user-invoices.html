<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <title>Invoice</title>
  <style>
    body {
      font-family: 'Segoe UI', sans-serif;
      margin: 0;
      padding: 20px;
      background-color: #f9f9f9;
    }
    .invoice-box {
      max-width: 800px;
      margin: auto;
      background: white;
      border: 1px solid #eee;
      padding: 30px;
      box-shadow: 0 0 10px rgba(0, 0, 0, 0.15);
    }
    h1 {
      font-size: 24px;
      color: #333;
    }
    table {
      width: 100%;
      line-height: 1.6;
      text-align: left;
    }
    table td {
      padding: 5px;
    }
    .heading {
      background: #eee;
      font-weight: bold;
    }
    .total {
      font-size: 18px;
      font-weight: bold;
    }
    .right {
      text-align: right;
    }
    .mb-20 {
      margin-bottom: 20px;
    }
  </style>
</head>
<body>
  <div class="invoice-box">
    <h1>Tax Invoice</h1>

    <p class="mb-20"><strong>Invoice #: </strong>{{ invoice.invoice_number }}<br>
    <strong>Issue Date:</strong> {{ invoice.issue_date|date:"d M Y" }}</p>

    <table>
      <tr>
        <td><strong>Student Name:</strong> {{ invoice.student.user.get_full_name }}</td>
        <td><strong>Email:</strong> {{ invoice.student.user.email }}</td>
      </tr>
      <tr>
        <td><strong>Subscription Package:</strong> {{ invoice.subscription.package.name }}</td>
        <td><strong>Duration:</strong> {{ invoice.subscription.package.duration_months }} months</td>
      </tr>
    </table>

    <hr class="mb-20" />

    <table>
      <tr class="heading">
        <td>Description</td>
        <td class="right">Amount</td>
      </tr>
      <tr>
        <td>Package Price</td>
        <td class="right">₹{{ invoice.subscription.package.price }}</td>
      </tr>
      <tr>
        <td>Tax</td>
        <td class="right">₹{{ invoice.tax_amount }}</td>
      </tr>
      <tr class="total">
        <td>Total</td>
        <td class="right">₹{{ invoice.total_amount }}</td>
      </tr>
    </table>

    <p class="mb-20"><strong>GSTIN:</strong> {{ invoice.gstin|default:"N/A" }}<br>
    <strong>Status:</strong> {{ invoice.is_paid|yesno:"Paid,Unpaid" }}</p>
  </div>
</body>
</html>
