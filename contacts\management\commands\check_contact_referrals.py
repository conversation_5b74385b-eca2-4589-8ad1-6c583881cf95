from django.core.management.base import BaseCommand
from django.utils import timezone
from contacts.models import Contact
from students.models import Student, Referral
import logging

logger = logging.getLogger(__name__)


class Command(BaseCommand):
    help = 'Clean up unmatched contacts and process referrals'

    def add_arguments(self, parser):
        parser.add_argument(
            '--dry-run',
            action='store_true',
            help='Run without making any changes to the database',
        )
        parser.add_argument(
            '--hours',
            type=int,
            default=24,
            help='Delete unmatched contacts older than this many hours (default: 24)',
        )
        parser.add_argument(
            '--process-referrals',
            action='store_true',
            help='Also process referrals for matched contacts',
        )

    def handle(self, *args, **options):
        dry_run = options['dry_run']
        hours = options['hours']
        process_referrals = options['process_referrals']

        self.stdout.write(
            self.style.SUCCESS(
                f'Starting contact cleanup {"(DRY RUN)" if dry_run else ""}'
            )
        )

        # 1. Clean up unmatched contacts
        cutoff_time = timezone.now() - timezone.timedelta(hours=hours)
        unmatched_contacts = Contact.objects.filter(
            is_matched=False,
            synced_at__lt=cutoff_time
        )

        unmatched_count = unmatched_contacts.count()
        self.stdout.write(f'Found {unmatched_count} unmatched contacts older than {hours} hours')

        if not dry_run and unmatched_count > 0:
            deleted_count = Contact.cleanup_unmatched_contacts(hours)
            self.stdout.write(
                self.style.SUCCESS(f'Deleted {deleted_count} unmatched contacts')
            )
        elif dry_run and unmatched_count > 0:
            self.stdout.write(f'Would delete {unmatched_count} unmatched contacts')

        # 2. Process referrals if requested
        referrals_created = 0
        if process_referrals:
            self.stdout.write('Processing referrals for matched contacts...')

            matched_contacts = Contact.objects.filter(is_matched=True)
            total_matched = matched_contacts.count()

            self.stdout.write(f'Found {total_matched} matched contacts to check for referrals')

            for contact in matched_contacts:
                try:
                    if not dry_run:
                        referrals_created += self.process_referral_for_contact(contact)
                    else:
                        self.stdout.write(f'Would check referrals for: {contact.name} -> {contact.related_user.username}')

                except Exception as e:
                    self.stdout.write(
                        self.style.ERROR(f'Error processing referral for contact {contact.id}: {str(e)}')
                    )

        # Summary
        self.stdout.write(
            self.style.SUCCESS(
                f'Cleanup completed! '
                f'{"Would delete" if dry_run else "Deleted"} {unmatched_count} unmatched contacts. '
                f'{"Would create" if dry_run else "Created"} {referrals_created} referrals.'
            )
        )

        if not dry_run:
            logger.info(f'Contact cleanup completed. Deleted {unmatched_count} contacts, created {referrals_created} referrals.')

    def process_referral_for_contact(self, contact):
        """Process referral for a matched contact"""
        if not contact.is_matched or not contact.related_user:
            return 0

        try:
            # Get the referred student
            referred_student = Student.objects.get(user=contact.related_user)

            # Get the referrer student
            referrer_student = Student.objects.get(user=contact.user)

            # Check if referral already exists
            existing_referral = Referral.objects.filter(
                referrer=referrer_student,
                referred=referred_student
            ).first()

            if not existing_referral:
                # Create new referral
                Referral.objects.create(
                    referrer=referrer_student,
                    referred=referred_student,
                    referral_code=referrer_student.referral_code or f"REF{referrer_student.id}"
                )

                # Update referrer's count
                referrer_student.refferred_count += 1
                referrer_student.save()

                self.stdout.write(
                    self.style.SUCCESS(
                        f'Created referral: {referrer_student.user.username} -> {referred_student.user.username}'
                    )
                )

                return 1

        except Student.DoesNotExist:
            pass

        return 0


