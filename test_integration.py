#!/usr/bin/env python3
"""
Integration Testing Script for Course-Related Django Components
This script performs comprehensive integration testing between models, views, serializers, and URLs.
"""

import os
import sys
import django
import traceback
import json

# Add the project directory to Python path
sys.path.insert(0, '/Users/<USER>/Documents/code/shash_b')

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'shashtrarth.settings')
django.setup()

# Override ALLOWED_HOSTS for testing
from django.conf import settings
if 'testserver' not in settings.ALLOWED_HOSTS:
    settings.ALLOWED_HOSTS.append('testserver')

# Import after Django setup
from django.contrib.auth.models import User
from django.test import Client
from rest_framework.test import APIClient
from rest_framework import status
from questions.models import (
    Course, SubCourse, Tier, Paper, Section, Module, Subject, Topic, SubTopic,
    MasterQuestion, MasterOption, Question, Option, PreviousYearQuestion
)
from questions.serializers import (
    CourseSerializer, SubjectSerializer, TopicSerializer, QuestionSerializer
)
from contributor.models import ContributorProfile


class TestResults:
    """Class to track test results."""
    
    def __init__(self):
        self.passed = 0
        self.failed = 0
        self.errors = []
    
    def add_pass(self, test_name):
        self.passed += 1
        print(f"✅ PASS: {test_name}")
    
    def add_fail(self, test_name, error):
        self.failed += 1
        self.errors.append(f"{test_name}: {error}")
        print(f"❌ FAIL: {test_name} - {error}")
    
    def summary(self):
        total = self.passed + self.failed
        print(f"\n{'='*60}")
        print(f"INTEGRATION TEST SUMMARY")
        print(f"{'='*60}")
        print(f"Total Tests: {total}")
        print(f"Passed: {self.passed}")
        print(f"Failed: {self.failed}")
        print(f"Success Rate: {(self.passed/total*100):.1f}%" if total > 0 else "No tests run")
        
        if self.errors:
            print(f"\n{'='*60}")
            print(f"FAILED TESTS:")
            print(f"{'='*60}")
            for error in self.errors:
                print(f"- {error}")


def test_course_creation_flow():
    """Test complete course creation flow from API to database."""
    results = TestResults()
    
    try:
        client = APIClient()
        
        # Test 1: Create course via API
        course_data = {
            'name': 'Integration Test Course',
            'description': 'Course created through integration test'
        }
        response = client.post('/api/questions/courses/', data=course_data)
        
        if response.status_code == 201:
            results.add_pass("Course creation via API")
            
            response_data = response.json()
            course_slug = response_data.get('slug')
            
            # Test 2: Verify course exists in database
            if Course.objects.filter(slug=course_slug).exists():
                results.add_pass("Course saved to database")
                
                course = Course.objects.get(slug=course_slug)
                
                # Test 3: Verify serializer data matches database
                serializer = CourseSerializer(course)
                if serializer.data['name'] == course_data['name']:
                    results.add_pass("Serializer data consistency")
                else:
                    results.add_fail("Serializer data consistency", "Serializer data doesn't match")
                
                # Test 4: Retrieve course via API
                response = client.get(f'/api/questions/courses/{course_slug}/')
                if response.status_code == 200:
                    results.add_pass("Course retrieval via API")
                    
                    retrieved_data = response.json()
                    if retrieved_data['name'] == course_data['name']:
                        results.add_pass("Retrieved data consistency")
                    else:
                        results.add_fail("Retrieved data consistency", "Retrieved data doesn't match")
                else:
                    results.add_fail("Course retrieval via API", f"Status code: {response.status_code}")
                
                # Test 5: Update course via API
                update_data = {
                    'name': 'Updated Integration Test Course',
                    'description': 'Updated description'
                }
                response = client.put(f'/api/questions/courses/{course_slug}/', data=update_data)
                if response.status_code == 200:
                    results.add_pass("Course update via API")
                    
                    # Verify update in database
                    course.refresh_from_db()
                    if course.name == update_data['name']:
                        results.add_pass("Course update in database")
                    else:
                        results.add_fail("Course update in database", "Database not updated")
                else:
                    results.add_fail("Course update via API", f"Status code: {response.status_code}")
                
                # Test 6: Delete course via API
                response = client.delete(f'/api/questions/courses/{course_slug}/')
                if response.status_code == 200:
                    results.add_pass("Course deletion via API")
                    
                    # Verify deletion in database
                    if not Course.objects.filter(slug=course_slug).exists():
                        results.add_pass("Course deleted from database")
                    else:
                        results.add_fail("Course deleted from database", "Course still exists")
                else:
                    results.add_fail("Course deletion via API", f"Status code: {response.status_code}")
            else:
                results.add_fail("Course saved to database", "Course not found in database")
        else:
            results.add_fail("Course creation via API", f"Status code: {response.status_code}")
        
    except Exception as e:
        results.add_fail("Course creation flow tests", f"Exception: {str(e)}")
    
    return results


def test_subject_topic_hierarchy():
    """Test subject-topic hierarchy creation and retrieval."""
    results = TestResults()
    
    try:
        client = APIClient()
        
        # Create a user for authentication
        import uuid
        unique_username = f'testuser_{uuid.uuid4().hex[:8]}'
        user = User.objects.create_user(username=unique_username, password='test123')
        client.force_authenticate(user=user)
        
        # Test 1: Create subject via API
        subject_data = {
            'name': 'Integration Test Subject',
            'description': 'Subject for integration testing',
            'rank': 1
        }
        response = client.post('/api/questions/subjects/', data=subject_data)
        
        if response.status_code == 201:
            results.add_pass("Subject creation via API")
            
            response_data = response.json()
            subject_slug = response_data['data']['slug']
            
            # Test 2: Create topic for the subject
            topic_data = {
                'subject': subject_slug,
                'name': 'Integration Test Topic',
                'description': 'Topic for integration testing'
            }
            response = client.post('/api/questions/topics/', data=topic_data)
            
            if response.status_code == 201:
                results.add_pass("Topic creation via API")
                
                topic_response_data = response.json()
                topic_slug = topic_response_data['data']['slug']
                
                # Test 3: Create subtopic for the topic
                subtopic_data = {
                    'topic': topic_slug,
                    'name': 'Integration Test SubTopic',
                    'description': 'SubTopic for integration testing'
                }
                response = client.post('/api/questions/subtopics/', data=subtopic_data)
                
                if response.status_code == 201:
                    results.add_pass("SubTopic creation via API")
                    
                    # Test 4: Verify hierarchy in database
                    subject = Subject.objects.get(slug=subject_slug)
                    topic = Topic.objects.get(slug=topic_slug)
                    
                    if topic.subject == subject:
                        results.add_pass("Topic-Subject relationship in database")
                    else:
                        results.add_fail("Topic-Subject relationship in database", "Relationship not established")
                    
                    # Test 5: Retrieve subject with topics via API
                    response = client.get(f'/api/questions/subjects/{subject_slug}/')
                    if response.status_code == 200:
                        results.add_pass("Subject with topics retrieval")
                        
                        subject_data = response.json()
                        if 'topics' in subject_data['data'] and len(subject_data['data']['topics']) > 0:
                            results.add_pass("Subject includes topics in API response")
                        else:
                            results.add_fail("Subject includes topics in API response", "Topics not included")
                    else:
                        results.add_fail("Subject with topics retrieval", f"Status code: {response.status_code}")
                    
                    # Test 6: Retrieve topic with subtopics via API
                    response = client.get(f'/api/questions/topics/{topic_slug}/')
                    if response.status_code == 200:
                        results.add_pass("Topic with subtopics retrieval")
                        
                        topic_data = response.json()
                        if 'subtopics' in topic_data['data'] and len(topic_data['data']['subtopics']) > 0:
                            results.add_pass("Topic includes subtopics in API response")
                        else:
                            results.add_fail("Topic includes subtopics in API response", "SubTopics not included")
                    else:
                        results.add_fail("Topic with subtopics retrieval", f"Status code: {response.status_code}")
                else:
                    results.add_fail("SubTopic creation via API", f"Status code: {response.status_code}")
            else:
                results.add_fail("Topic creation via API", f"Status code: {response.status_code}")
        else:
            results.add_fail("Subject creation via API", f"Status code: {response.status_code}")
        
        # Cleanup
        SubTopic.objects.filter(name='Integration Test SubTopic').delete()
        Topic.objects.filter(name='Integration Test Topic').delete()
        Subject.objects.filter(name='Integration Test Subject').delete()
        user.delete()
        
    except Exception as e:
        results.add_fail("Subject-topic hierarchy tests", f"Exception: {str(e)}")
    
    return results


def test_question_with_options_flow():
    """Test complete question with options creation and retrieval flow."""
    results = TestResults()
    
    try:
        # Setup required data
        import uuid
        unique_username = f'testcontributor_{uuid.uuid4().hex[:8]}'
        user = User.objects.create_user(username=unique_username, password='test123')
        contributor = ContributorProfile.objects.create(user=user, role='contributor')
        
        subject = Subject.objects.create(name='Integration Question Subject')
        topic = Topic.objects.create(subject=subject, name='Integration Question Topic')
        course = Course.objects.create(name='Integration Question Course')
        
        # Test 1: Create question in database
        question = Question.objects.create(
            content='What is the integration test question?',
            difficulty=5,
            author=contributor,
            status='active'
        )
        question.subject.add(subject)
        question.topic.add(topic)
        question.course.add(course)
        
        if question.question_id:
            results.add_pass("Question creation in database")
            
            # Test 2: Create options for the question
            options_data = [
                {'option_text': 'Option A', 'is_correct': True},
                {'option_text': 'Option B', 'is_correct': False},
                {'option_text': 'Option C', 'is_correct': False},
                {'option_text': 'Option D', 'is_correct': False}
            ]
            
            options = []
            for option_data in options_data:
                option = Option.objects.create(
                    question=question,
                    **option_data
                )
                options.append(option)
            
            if len(options) == 4:
                results.add_pass("Options creation in database")
                
                # Test 3: Serialize question with options
                serializer = QuestionSerializer(question)
                serialized_data = serializer.data
                
                if 'options' in serialized_data and len(serialized_data['options']) == 4:
                    results.add_pass("Question serialization with options")
                else:
                    results.add_fail("Question serialization with options", "Options not properly serialized")
                
                # Test 4: Verify relationships in serialized data
                if ('subject_name' in serialized_data and 
                    'topic_name' in serialized_data and
                    'course_name' in serialized_data):
                    results.add_pass("Question relationships in serialized data")
                else:
                    results.add_fail("Question relationships in serialized data", "Relationships not included")
                
                # Test 5: Test question difficulty level method
                if question.get_difficulty_level() == 'Medium':
                    results.add_pass("Question difficulty level method")
                else:
                    results.add_fail("Question difficulty level method", f"Expected 'Medium', got '{question.get_difficulty_level()}'")
                
                # Test 6: Test option relationship
                question_options = question.options.all()
                if question_options.count() == 4:
                    results.add_pass("Question-Option relationship")
                    
                    correct_options = question_options.filter(is_correct=True)
                    if correct_options.count() == 1:
                        results.add_pass("Correct option identification")
                    else:
                        results.add_fail("Correct option identification", f"Expected 1 correct option, got {correct_options.count()}")
                else:
                    results.add_fail("Question-Option relationship", f"Expected 4 options, got {question_options.count()}")
            else:
                results.add_fail("Options creation in database", f"Expected 4 options, got {len(options)}")
        else:
            results.add_fail("Question creation in database", "Question ID not generated")
        
        # Cleanup
        Option.objects.filter(question=question).delete()
        question.delete()
        course.delete()
        topic.delete()
        subject.delete()
        contributor.delete()
        user.delete()
        
    except Exception as e:
        results.add_fail("Question with options flow tests", f"Exception: {str(e)}")
    
    return results


def test_courses_with_subcourses_integration():
    """Test courses with subcourses API integration."""
    results = TestResults()
    
    try:
        client = APIClient()
        
        # Test 1: Create courses and subcourses
        course1 = Course.objects.create(name='Integration Course 1')
        course2 = Course.objects.create(name='Integration Course 2')
        
        subcourse1 = SubCourse.objects.create(course=course1, name='SubCourse 1')
        subcourse2 = SubCourse.objects.create(course=course1, name='SubCourse 2')
        subcourse3 = SubCourse.objects.create(course=course2, name='SubCourse 3')
        
        # Test 2: Use courses with subcourses API
        request_data = {
            'course_slugs': [course1.slug, course2.slug]
        }
        response = client.post('/api/questions/courses-with-sub-courses/', data=request_data, format='json')
        
        if response.status_code == 200:
            results.add_pass("Courses with subcourses API call")
            
            response_data = response.json()
            if len(response_data) == 2:
                results.add_pass("Correct number of courses returned")
                
                # Test 3: Verify subcourses are included
                course1_data = next((c for c in response_data if c['slug'] == course1.slug), None)
                if course1_data and 'sub_courses' in course1_data:
                    if len(course1_data['sub_courses']) == 2:
                        results.add_pass("Course 1 subcourses included correctly")
                    else:
                        results.add_fail("Course 1 subcourses included correctly", f"Expected 2, got {len(course1_data['sub_courses'])}")
                else:
                    results.add_fail("Course 1 subcourses included correctly", "SubCourses not found")
                
                course2_data = next((c for c in response_data if c['slug'] == course2.slug), None)
                if course2_data and 'sub_courses' in course2_data:
                    if len(course2_data['sub_courses']) == 1:
                        results.add_pass("Course 2 subcourses included correctly")
                    else:
                        results.add_fail("Course 2 subcourses included correctly", f"Expected 1, got {len(course2_data['sub_courses'])}")
                else:
                    results.add_fail("Course 2 subcourses included correctly", "SubCourses not found")
                
                # Test 4: Verify serializer consistency
                course1_serializer = CourseSerializer(course1)
                if course1_serializer.data['name'] == course1_data['name']:
                    results.add_pass("Serializer consistency in API response")
                else:
                    results.add_fail("Serializer consistency in API response", "Data doesn't match")
            else:
                results.add_fail("Correct number of courses returned", f"Expected 2, got {len(response_data)}")
        else:
            results.add_fail("Courses with subcourses API call", f"Status code: {response.status_code}")
        
        # Cleanup
        SubCourse.objects.filter(course__in=[course1, course2]).delete()
        Course.objects.filter(name__in=['Integration Course 1', 'Integration Course 2']).delete()
        
    except Exception as e:
        results.add_fail("Courses with subcourses integration tests", f"Exception: {str(e)}")
    
    return results


def test_admin_api_consistency():
    """Test consistency between admin interface and API."""
    results = TestResults()
    
    try:
        # Test 1: Create course through admin interface simulation
        course = Course.objects.create(
            name='Admin API Test Course',
            description='Course created for admin-API consistency test'
        )
        
        if course.course_id:
            results.add_pass("Course creation through model (admin simulation)")
            
            # Test 2: Retrieve via API
            client = APIClient()
            response = client.get(f'/api/questions/courses/{course.slug}/')
            
            if response.status_code == 200:
                results.add_pass("Course retrieval via API after admin creation")
                
                api_data = response.json()
                if (api_data['name'] == course.name and 
                    api_data['description'] == course.description):
                    results.add_pass("Admin-API data consistency")
                else:
                    results.add_fail("Admin-API data consistency", "Data doesn't match")
            else:
                results.add_fail("Course retrieval via API after admin creation", f"Status code: {response.status_code}")
            
            # Test 3: Update through API and verify in model
            update_data = {
                'name': 'Updated Admin API Test Course',
                'description': 'Updated description'
            }
            response = client.put(f'/api/questions/courses/{course.slug}/', data=update_data)
            
            if response.status_code == 200:
                results.add_pass("Course update via API")
                
                course.refresh_from_db()
                if course.name == update_data['name']:
                    results.add_pass("API-Admin data consistency after update")
                else:
                    results.add_fail("API-Admin data consistency after update", "Model not updated")
            else:
                results.add_fail("Course update via API", f"Status code: {response.status_code}")
        else:
            results.add_fail("Course creation through model (admin simulation)", "Course ID not generated")
        
        # Cleanup
        Course.objects.filter(name__contains='Admin API Test Course').delete()
        
    except Exception as e:
        results.add_fail("Admin-API consistency tests", f"Exception: {str(e)}")
    
    return results


def main():
    """Run all integration tests."""
    print("🚀 Starting Comprehensive Integration Tests")
    print("="*60)
    
    all_results = TestResults()
    
    # Run all test functions
    test_functions = [
        test_course_creation_flow,
        test_subject_topic_hierarchy,
        test_question_with_options_flow,
        test_courses_with_subcourses_integration,
        test_admin_api_consistency
    ]
    
    for test_func in test_functions:
        print(f"\n📋 Running {test_func.__name__}...")
        try:
            result = test_func()
            all_results.passed += result.passed
            all_results.failed += result.failed
            all_results.errors.extend(result.errors)
        except Exception as e:
            all_results.add_fail(test_func.__name__, f"Test function failed: {str(e)}")
            print(f"💥 Error in {test_func.__name__}: {str(e)}")
            traceback.print_exc()
    
    # Print final summary
    all_results.summary()


if __name__ == '__main__':
    main()
