import random

from rest_framework import serializers

from contributor.models import Contributor<PERSON><PERSON><PERSON><PERSON>
from .models import TestPaper, TestPattern, ScoreTracker,SubmitTest
# from django.contrib.auth.models import Q
# from questions.serializers import TopicSerializer


from questions.models import (
    MasterOption,
    MasterQuestion,
    Module,
    Option,
    Paper,
    Question,
    Section,
    Subject,
    Tier,
    Topic,
    SubTopic
)


class SectionSerializer(serializers.Serializer):
    section_name = serializers.CharField()
    subject_ids = serializers.ListField(child=serializers.IntegerField())
    subject_names = serializers.SerializerMethodField(read_only=True)
    time_limit = serializers.IntegerField()
    number_of_questions = serializers.IntegerField()

    def get_subject_names(self, obj):
        subjects = Subject.objects.filter(subject_id__in=obj['subject_ids'])
        return [subject.name for subject in subjects]


class TestPatternSerializer(serializers.ModelSerializer):
    sections = SectionSerializer(many=True)

    class Meta:
        model = TestPattern
        fields = ['pattern_id', 'name', 'number_of_sections', 'sections', 'negative_marking', 'positive_mark', 'version', 'updated_at', 'difficulty', 'normal_question_percentage', 'is_normal_question', 'master_question_percentage', 'is_master_question', 'master_option_percentage', 'is_master_option', 'contributor']
        read_only_fields = ["contributor", "updated_at"]


    def create(self, validated_data):
        """
        Custom logic to handle the creation of a TestPattern.
        """ # Extract subject IDs
        user = self.context["request"].user

         # Extract scoring data from request
        scoring_data = self.context["request"].data.get("scoring", {})
        validated_data["positive_mark"] = scoring_data.get("positive_mark", 1.0)
        validated_data["negative_marking"] = scoring_data.get("negative_marking", 0.0)
        

        # Extract question distribution
        distribution_data = self.context["request"].data.get("question_distribution", {})
        validated_data["normal_question_percentage"] = distribution_data.get("normal_question", {}).get("percentage", 50.0)
        validated_data["is_normal_question"] = distribution_data.get("normal_question", {}).get("enabled", True)
        validated_data["master_question_percentage"] = distribution_data.get("master_question", {}).get("percentage", 30.0)
        validated_data["is_master_question"] = distribution_data.get("master_question", {}).get("enabled", False)
        validated_data["master_option_percentage"] = distribution_data.get("master_option", {}).get("percentage", 20.0)
        validated_data["is_master_option"] = distribution_data.get("master_option", {}).get("enabled", False)

        cnt=0
        for section_count in self.context["request"].data.get("sections", {}):
            cnt+=1
        validated_data['number_of_sections'] = cnt

        try:
            contributor_profile = user.contributor_profile
        except ContributorProfile.DoesNotExist:
            raise serializers.ValidationError(
                {
                    "contributor": "Contributor profile does not exist for the current user."
                }
            )
        

        validated_data["contributor"] = contributor_profile
       
        return super().create(validated_data)

    def update(self, instance, validated_data):
        """
        Custom logic to handle the update of a TestPattern.
        """
        subject_ids = validated_data.pop("subject_ids", None)
        for attr, value in validated_data.items():
            setattr(instance, attr, value)

        if subject_ids is not None:
            instance.subjects.set(subject_ids)  # Update many-to-many relationship
        instance.save()
        return instance

    def delete(self, instance):
        """
        Custom logic to handle the deletion of a TestPattern.
        """
        instance.delete()
        return {"message": "Test pattern deleted successfully"}



class MasterQuestionCustomSerializer(serializers.ModelSerializer):
    class Meta:
        model = MasterQuestion
        fields = ['title', 'passage_content']

class MasterOptionCustomSerializer(serializers.ModelSerializer):
    class Meta:
        model = MasterOption
        fields = ['title', 'option_content', "conditions"]

class TestPaperSerializer(serializers.ModelSerializer):
    questions = serializers.SerializerMethodField()
    entity_type = serializers.CharField(write_only=True, required=False)
    entity_id = serializers.IntegerField(write_only=True, required=False)
   

    class Meta:
        model = TestPaper
        fields = [  
            "paper_id",
            "subcourse",
            "student",
            "status",
            "total_marks",
            "time_limit",
            "duration_in_minutes",
            "questions",
            "entity_type",
            "entity_id",
        ]

    def get_questions(self, obj):
        return obj.questions if obj.questions else []

    def find_test_pattern(self, entity_type, entity_id):
        """
        Recursively search for test pattern in the hierarchy
        """
        if not entity_type or not entity_id:
            raise serializers.ValidationError(
                {"message": "entity_type and entity_id are required"}
            )
        if entity_type == "tier":
            try:
                tier = Tier.objects.get(tier_id=entity_id)
                if hasattr(tier, "test_pattern") and tier.test_pattern:
                    return tier.test_pattern
                return self.find_test_pattern("paper", tier.paper.id)
            except Tier.DoesNotExist:
                raise serializers.ValidationError(
                    {"message": f"Tier with id {entity_id} not found"}
                )

        elif entity_type == "module":
            try:
                paper = Module.objects.get(module_id=entity_id)
                if hasattr(paper, "test_pattern") and paper.test_pattern:
                    return paper.test_pattern
                return self.find_test_pattern("module", paper.section.id)
            except Module.DoesNotExist:
                raise serializers.ValidationError(
                    {"message": f"Module with id {entity_id} not found"}
                )

        elif entity_type == "section":
            try:
                section = Section.objects.get(section_id=entity_id)
                if hasattr(section, "test_pattern") and section.test_pattern:
                    return section.test_pattern
                return self.find_test_pattern("section", section.module.id)
            except Section.DoesNotExist:
                raise serializers.ValidationError(
                    {"message": f"Section with id {entity_id} not found"}
                )

        elif entity_type == "paper":
            try:
                paper = Paper.objects.get(paper_id=entity_id)
                if hasattr(paper, "test_pattern") and paper.test_pattern:
                    return paper.test_pattern
                raise serializers.ValidationError(
                    {"message": "No test pattern found in the entire hierarchy"}
                )
            except Paper.DoesNotExist:
                raise serializers.ValidationError(
                    {"message": f"Paper with id {entity_id} not found"}
                )

        raise serializers.ValidationError(
            {"message": f"Invalid entity_type: {entity_type}"}
        )
    
    def generate_questions_from_pattern(self, test_pattern):
        """
        Fetch and randomly pick questions based on the test pattern.
        """
        sections = test_pattern.sections  # This is a JSONField
        selected_questions = []

        for section in sections:
            try:
                subject_ids = section.get("subject_ids", [])
                if not subject_ids:
                    raise serializers.ValidationError("subject_ids cannot be empty in section")
                subject_id = subject_ids[0]
            except Exception:
                raise serializers.ValidationError("Invalid section format.") # Assuming 1 subject per section
            num_questions = section.get("number_of_questions", 0)

            # Fetch all available questions for the subject
            available_questions = Question.objects.filter(subject_id=subject_id)

            # Select random questions
            selected_questions += random.sample(
                list(available_questions), min(num_questions, len(available_questions))
            )

        # Convert question objects to JSON
        return [{"id": q.id, "text": q.text, "marks": 1} for q in selected_questions]
    
    

    def create(self, validated_data):
        entity_type = validated_data.pop("entity_type", None)
        entity_id = validated_data.pop("entity_id", None)

        test_pattern = self.find_test_pattern(entity_type, entity_id)
        if not test_pattern:
            raise serializers.ValidationError(
                {"message": f"No test pattern found in the {entity_type} hierarchy."}
            )

        question_data = []
        subcourse = validated_data.get("subcourse")
        student = validated_data.get("student")
        
        if not student or not subcourse:
            raise serializers.ValidationError({"message": "Student and Subcourse are required."})

        total_marks = 0
        overall_time_limit = 0
        assigned_master_questions = set()
        assigned_master_options = set()
        assigned_normal_questions = set()
        section_manage  = test_pattern.question_distribution(test_pattern.total_questions())
        section_subjects = Subject.objects.filter(subject_id__in=test_pattern.total_subjects())
        base_query = Question.objects.filter(subject__in=section_subjects, status="active", difficulty__in=test_pattern.fetch_difficulty_level())
        full_section =[]       
        for index,section in enumerate(test_pattern.sections):
            overall_time_limit += float(section["time_limit"])
            subject_ids = section["subject_ids"]
            num_questions = section["number_of_questions"]

            total_section_questions = int(num_questions)
      
            master_questions = list(
                base_query.filter(is_master=True).distinct()
            )
            distribution = section_manage[index]
            print(distribution)
            master_question_objects = []
            for master_q_id in master_questions:
                master_q = master_q_id.master_question
                if master_q and master_q not in assigned_master_questions:
                    related_questions = list(base_query.filter(master_question=master_q))
                    if related_questions:
                        master_question_objects.append({"question": master_q, "related_questions": related_questions})

            master_questions_needed =  distribution["master_questions"]
            selected_questions = []

            if master_question_objects:
                selected_master_groups = random.sample(
                    master_question_objects, min(master_questions_needed, len(master_question_objects))
                )
                
                selected_questions.extend(selected_master_groups)
                assigned_master_questions.update(q["question"] for q in selected_master_groups)
                remaining_questions = total_section_questions - len(selected_questions)
            else:
                remaining_questions = total_section_questions
            
            # Master Option Questions
            if remaining_questions > 0:
                master_option_questions = list(
                    base_query.filter(is_master_option=True, master_option__isnull=False).distinct()
                    
                )

                master_option_objects = []
                for master_opt_id in master_option_questions:
                    master_opt = master_opt_id.master_option
                    if master_opt and master_opt not in assigned_master_options:
                        related_questions = list(base_query.filter(master_option=master_opt))
                        if related_questions:
                            master_option_objects.append({"question": master_opt, "related_questions": related_questions})
                
                master_options_needed = distribution[ "master_options"]
                
                if master_option_objects:
                    selected_master_option_groups = random.sample(
                        master_option_objects, min(master_options_needed, len(master_option_objects))
                    )
                    # for group in selected_master_option_groups:
                    selected_questions.extend(selected_master_option_groups)
                    assigned_master_options.update(q["question"] for q in selected_master_option_groups)

                    remaining_questions = total_section_questions - len(selected_questions)

            # Normal Questions
            if remaining_questions > 0:
                normal_questions = [q for q in base_query.filter(master_question__isnull=True, master_option__isnull=True) if q not in assigned_normal_questions]

                if len(normal_questions) < remaining_questions:
                    raise serializers.ValidationError(
                        {
                            "message": f"Not enough normal questions in section {section.get('name', '')}. "
                            f"Required: {remaining_questions}, Available: {len(normal_questions)}"
                        }
                    )

                selected_normal_questions = random.sample(normal_questions, remaining_questions)
                assigned_normal_questions.update(selected_normal_questions)
                selected_questions.append({"question":Question, "related_questions":selected_normal_questions,})


            transformed_question={}
            
            transformed_question["section_name"] = section['section_name']
            transformed_question["section_time"] = section['time_limit']
            question_data = []
            for question in selected_questions:
                section_data = {}
                if isinstance(question['question'], MasterQuestion):
                    sub_questions = []
                    section_data['master_question_id'] = question['question'].master_question_id
                    section_data['question_type'] ="master_question"
                    section_data['title'] = question['question'].title
                    section_data['passage_content'] = question['question'].passage_content
                    for sub_question in question['related_questions']:
                        
                        options = list(Option.objects.filter(question=sub_question))
                        shuffled_options = random.sample(options, len(options)) if options else []
                        sub_question_obj = {
                            "question_id": sub_question.question_id,
                            "content": sub_question.content,
                            "difficulty": sub_question.get_difficulty_level(),
                            "marks": test_pattern.positive_mark,
                            "negative_marks": test_pattern.negative_marking,
                            "options": [
                                {"option_id": opt.option_id, "option_text": opt.option_text, "is_correct": opt.is_correct}
                                for opt in shuffled_options
                            ],
                        }
                        sub_questions.append(sub_question_obj)
                    section_data['subquestions'] = sub_questions
                    question_data.append(section_data)

                elif isinstance(question['question'], MasterOption):
                    sub_questions = []
                    section_data['master_option_id'] = question['question'].master_option_id
                    section_data['question_type'] ="master_option"
                    section_data['title'] = question['question'].title
                    section_data['passage_content'] = question['question'].option_content
                    
                    section_data['conditions'] = question['question'].conditions
                    for sub_question in question['related_questions']:
                        
                        options = list(Option.objects.filter(question=sub_question))
                        shuffled_options = random.sample(options, len(options)) if options else []
                        sub_question_obj = {
                            "question_id": sub_question.question_id,
                            "content": sub_question.content,
                            "difficulty": sub_question.get_difficulty_level(),
                            "marks": test_pattern.positive_mark,
                            "negative_marks": test_pattern.negative_marking,
                            "options": [
                                {"option_id": opt.option_id, "option_text": opt.option_text, "is_correct": opt.is_correct}
                                for opt in shuffled_options
                            ],
                        }
                        sub_questions.append(sub_question_obj)
                    section_data['subquestions'] = sub_questions
                    question_data.append(section_data)
                else:
                    sub_questions = []
                    section_data['question_type'] ="normal_question"
                    for sub_question in question['related_questions']:
                        ''
                        options = list(Option.objects.filter(question=sub_question))
                        shuffled_options = random.sample(options, len(options)) if options else []
                        sub_question_obj = {
                            "question_id": sub_question.question_id,
                            "content": sub_question.content,
                            "difficulty": sub_question.get_difficulty_level(),
                            "marks": test_pattern.positive_mark,
                            "negative_marks": test_pattern.negative_marking,
                            "options": [
                                {"option_id": opt.option_id, "option_text": opt.option_text, "is_correct": opt.is_correct}
                                for opt in shuffled_options
                            ],
                        }
                        sub_questions.append(sub_question_obj)
                    section_data['subquestions'] = sub_questions
                    question_data.append(section_data)
            
            transformed_question["questions"] = question_data 
            full_section.append(transformed_question)

            # section_data.update(transformed_question)

        # **Create the test paper object**
        test_paper = TestPaper.objects.create(
            subcourse=subcourse,
            student=student,
            status="generated",
            total_marks=total_marks,
            time_limit=overall_time_limit,
            duration_in_minutes=overall_time_limit,
            questions=full_section,  # JSON data is correctly assigned
        )

        # **Explicitly save test_paper**
        all_data = {
            "sections": full_section,
            "details":{
                        "paper_id":test_paper.paper_id,
                        "student": student.id,
                        "subcourse" :subcourse.subcourse_id,
                        "subcourse_name":subcourse.name,
                        "status": "generated",
                        "total_marks" : total_marks,
                        "total_time_duartion":overall_time_limit,
                    }
            }
        
        
        return all_data

    

class TestSubmissionSerializer(serializers.ModelSerializer):
    class Meta:
        model = SubmitTest
        fields = '__all__'

class ScoreTrackerSerializer(serializers.ModelSerializer):
    class Meta:
        model = ScoreTracker
        fields = '__all__'


class SectionSummarySerializer(serializers.Serializer):
    section_name = serializers.CharField()
    subject_names = serializers.SerializerMethodField()

    def get_subject_names(self, obj):
        subject_ids = obj.get('subject_ids', [])
        subjects = Subject.objects.filter(subject_id__in=subject_ids)
        
        # Get unique subject names
        subject_names = list(set(subject.name for subject in subjects))
        
        return subject_names
    



class SubTopicSerializer(serializers.ModelSerializer):
    class Meta:
        model = SubTopic
        fields = ["subtopic_id", "slug", "topic", "name", "description"]


class TopicSerializer(serializers.ModelSerializer):
    subtopics = SubTopicSerializer(many=True, read_only=True)

    class Meta:
        model = Topic
        fields = ["topic_id", "slug", "subject", "name", "description", "subtopics"]
        
class SectionSummarySerializer(serializers.Serializer):
    section_name = serializers.CharField()
    subject_names = serializers.SerializerMethodField()
    topics = serializers.SerializerMethodField()

    def get_subject_names(self, obj):
        subject_ids = obj.get('subject_ids', [])
        subjects = Subject.objects.filter(subject_id__in=subject_ids)
        
        # Get unique subject names
        subject_names = list(set(subject.name for subject in subjects))
        
        return subject_names

    def get_topics(self, obj):
        subject_ids = obj.get('subject_ids', [])
        topics_data = []

        # Get topics associated with all subject_ids
        topics = Topic.objects.filter(subject_id__in=subject_ids).prefetch_related('subtopics')
        
        for topic in topics:
            topic_data = {
                "topic_id": topic.topic_id,
                "name": topic.name,
                "subtopics": [{"subtopic_id": sub.subtopic_id, "name": sub.name} for sub in topic.subtopics.all()]
            }
            topics_data.append(topic_data)

        return topics_data

