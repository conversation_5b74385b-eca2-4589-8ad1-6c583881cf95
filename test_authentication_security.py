#!/usr/bin/env python3
"""
Authentication Security Testing Script
This script tests the authentication security fix to ensure customer care and contributor
systems are properly isolated and cannot cross-authenticate.
"""

import os
import sys
import django
import traceback

# Add the project directory to Python path
sys.path.insert(0, '/Users/<USER>/Documents/code/shash_b')

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'shashtrarth.settings')
django.setup()

# Override ALLOWED_HOSTS for testing
from django.conf import settings
if 'testserver' not in settings.ALLOWED_HOSTS:
    settings.ALLOWED_HOSTS.append('testserver')

# Import after Django setup
from django.contrib.auth.models import User
from rest_framework.test import APIClient
from rest_framework import status
from customrcare.models import CustomrcareProfile
from contributor.models import ContributorProfile


class TestResults:
    """Class to track test results."""
    
    def __init__(self):
        self.passed = 0
        self.failed = 0
        self.errors = []
    
    def add_pass(self, test_name):
        self.passed += 1
        print(f"✅ PASS: {test_name}")
    
    def add_fail(self, test_name, error):
        self.failed += 1
        self.errors.append(f"{test_name}: {error}")
        print(f"❌ FAIL: {test_name} - {error}")
    
    def summary(self):
        total = self.passed + self.failed
        print(f"\n{'='*60}")
        print(f"AUTHENTICATION SECURITY TEST SUMMARY")
        print(f"{'='*60}")
        print(f"Total Tests: {total}")
        print(f"Passed: {self.passed}")
        print(f"Failed: {self.failed}")
        print(f"Success Rate: {(self.passed/total*100):.1f}%" if total > 0 else "No tests run")
        
        if self.errors:
            print(f"\n{'='*60}")
            print(f"FAILED TESTS:")
            print(f"{'='*60}")
            for error in self.errors:
                print(f"- {error}")


def test_customer_care_authentication():
    """Test customer care authentication security."""
    results = TestResults()
    
    try:
        # Create test users
        import uuid
        
        # 1. Create a legitimate customer care user
        cc_username = f'cc_user_{uuid.uuid4().hex[:8]}'
        cc_user = User.objects.create_user(
            username=cc_username,
            password='testpass123',
            email='<EMAIL>'
        )
        cc_profile = CustomrcareProfile.objects.create(
            user=cc_user,
            contact=1234567890,
            role='customrcare'
        )
        
        # 2. Create a contributor user (should NOT be able to login to customer care)
        contrib_username = f'contrib_user_{uuid.uuid4().hex[:8]}'
        contrib_user = User.objects.create_user(
            username=contrib_username,
            password='testpass123',
            email='<EMAIL>'
        )
        contrib_profile = ContributorProfile.objects.create(
            user=contrib_user,
            role='contributor'
        )
        
        # 3. Create a regular user with no profiles
        regular_username = f'regular_user_{uuid.uuid4().hex[:8]}'
        regular_user = User.objects.create_user(
            username=regular_username,
            password='testpass123',
            email='<EMAIL>'
        )
        
        client = APIClient()
        
        # Test 1: Legitimate customer care user should be able to login
        login_data = {
            'username': cc_username,
            'password': 'testpass123'
        }
        response = client.post('/api/customrcare/login/', data=login_data)
        if response.status_code == 200:
            results.add_pass("Legitimate customer care user can login")
            
            response_data = response.json()
            if response_data.get('role') == 'customrcare':
                results.add_pass("Customer care user gets correct role")
            else:
                results.add_fail("Customer care user gets correct role", f"Got role: {response_data.get('role')}")
        else:
            results.add_fail("Legitimate customer care user can login", f"Status: {response.status_code}")
        
        # Test 2: Contributor user should NOT be able to login to customer care
        login_data = {
            'username': contrib_username,
            'password': 'testpass123'
        }
        response = client.post('/api/customrcare/login/', data=login_data)
        if response.status_code == 401:
            results.add_pass("Contributor user blocked from customer care login")
            
            response_data = response.json()
            if 'not authorized for customer care access' in str(response_data):
                results.add_pass("Correct error message for unauthorized access")
            else:
                results.add_fail("Correct error message for unauthorized access", f"Got: {response_data}")
        else:
            results.add_fail("Contributor user blocked from customer care login", f"Status: {response.status_code}")
        
        # Test 3: Regular user with no profile should NOT be able to login
        login_data = {
            'username': regular_username,
            'password': 'testpass123'
        }
        response = client.post('/api/customrcare/login/', data=login_data)
        if response.status_code == 401:
            results.add_pass("Regular user blocked from customer care login")
        else:
            results.add_fail("Regular user blocked from customer care login", f"Status: {response.status_code}")
        
        # Test 4: Verify no automatic profile creation occurred
        if not hasattr(contrib_user, 'customrcare_profile'):
            results.add_pass("No automatic customer care profile created for contributor")
        else:
            results.add_fail("No automatic customer care profile created for contributor", "Profile was created")
        
        if not hasattr(regular_user, 'customrcare_profile'):
            results.add_pass("No automatic customer care profile created for regular user")
        else:
            results.add_fail("No automatic customer care profile created for regular user", "Profile was created")
        
        # Cleanup
        cc_profile.delete()
        cc_user.delete()
        contrib_profile.delete()
        contrib_user.delete()
        regular_user.delete()
        
    except Exception as e:
        results.add_fail("Customer care authentication tests", f"Exception: {str(e)}")
        traceback.print_exc()
    
    return results


def test_contributor_authentication():
    """Test contributor authentication security."""
    results = TestResults()
    
    try:
        # Create test users
        import uuid
        
        # 1. Create a legitimate contributor user
        contrib_username = f'contrib_user_{uuid.uuid4().hex[:8]}'
        contrib_user = User.objects.create_user(
            username=contrib_username,
            password='testpass123',
            email='<EMAIL>'
        )
        contrib_profile = ContributorProfile.objects.create(
            user=contrib_user,
            role='contributor'
        )
        
        # 2. Create a customer care user (should NOT be able to login to contributor)
        cc_username = f'cc_user_{uuid.uuid4().hex[:8]}'
        cc_user = User.objects.create_user(
            username=cc_username,
            password='testpass123',
            email='<EMAIL>'
        )
        cc_profile = CustomrcareProfile.objects.create(
            user=cc_user,
            contact=1234567890,
            role='customrcare'
        )
        
        # 3. Create a regular user with no profiles
        regular_username = f'regular_user_{uuid.uuid4().hex[:8]}'
        regular_user = User.objects.create_user(
            username=regular_username,
            password='testpass123',
            email='<EMAIL>'
        )
        
        client = APIClient()
        
        # Test 1: Legitimate contributor user should be able to login
        login_data = {
            'username': contrib_username,
            'password': 'testpass123'
        }
        response = client.post('/api/contributor/login/', data=login_data)
        if response.status_code == 200:
            results.add_pass("Legitimate contributor user can login")
            
            response_data = response.json()
            if response_data.get('role') == 'contributor':
                results.add_pass("Contributor user gets correct role")
            else:
                results.add_fail("Contributor user gets correct role", f"Got role: {response_data.get('role')}")
        else:
            results.add_fail("Legitimate contributor user can login", f"Status: {response.status_code}")
        
        # Test 2: Customer care user should NOT be able to login to contributor
        login_data = {
            'username': cc_username,
            'password': 'testpass123'
        }
        response = client.post('/api/contributor/login/', data=login_data)
        if response.status_code == 401:
            results.add_pass("Customer care user blocked from contributor login")
            
            response_data = response.json()
            if 'not authorized for contributor access' in str(response_data):
                results.add_pass("Correct error message for unauthorized access")
            else:
                results.add_fail("Correct error message for unauthorized access", f"Got: {response_data}")
        else:
            results.add_fail("Customer care user blocked from contributor login", f"Status: {response.status_code}")
        
        # Test 3: Regular user with no profile should NOT be able to login
        login_data = {
            'username': regular_username,
            'password': 'testpass123'
        }
        response = client.post('/api/contributor/login/', data=login_data)
        if response.status_code == 401:
            results.add_pass("Regular user blocked from contributor login")
        else:
            results.add_fail("Regular user blocked from contributor login", f"Status: {response.status_code}")
        
        # Test 4: Verify no automatic profile creation occurred
        if not hasattr(cc_user, 'contributor_profile'):
            results.add_pass("No automatic contributor profile created for customer care")
        else:
            results.add_fail("No automatic contributor profile created for customer care", "Profile was created")
        
        if not hasattr(regular_user, 'contributor_profile'):
            results.add_pass("No automatic contributor profile created for regular user")
        else:
            results.add_fail("No automatic contributor profile created for regular user", "Profile was created")
        
        # Cleanup
        contrib_profile.delete()
        contrib_user.delete()
        cc_profile.delete()
        cc_user.delete()
        regular_user.delete()
        
    except Exception as e:
        results.add_fail("Contributor authentication tests", f"Exception: {str(e)}")
        traceback.print_exc()
    
    return results


def test_cross_authentication_prevention():
    """Test that users cannot cross-authenticate between systems."""
    results = TestResults()
    
    try:
        import uuid
        
        # Create a user with both profiles (edge case)
        dual_username = f'dual_user_{uuid.uuid4().hex[:8]}'
        dual_user = User.objects.create_user(
            username=dual_username,
            password='testpass123',
            email='<EMAIL>'
        )
        
        # Create both profiles for the same user
        cc_profile = CustomrcareProfile.objects.create(
            user=dual_user,
            contact=1234567890,
            role='customrcare'
        )
        contrib_profile = ContributorProfile.objects.create(
            user=dual_user,
            role='contributor'
        )
        
        client = APIClient()
        
        # Test 1: User should be able to login to customer care with customer care role
        login_data = {
            'username': dual_username,
            'password': 'testpass123'
        }
        response = client.post('/api/customrcare/login/', data=login_data)
        if response.status_code == 200:
            results.add_pass("Dual user can login to customer care")
            
            response_data = response.json()
            if response_data.get('role') == 'customrcare':
                results.add_pass("Dual user gets customer care role when logging into customer care")
            else:
                results.add_fail("Dual user gets customer care role when logging into customer care", f"Got: {response_data.get('role')}")
        else:
            results.add_fail("Dual user can login to customer care", f"Status: {response.status_code}")
        
        # Test 2: User should be able to login to contributor with contributor role
        response = client.post('/api/contributor/login/', data=login_data)
        if response.status_code == 200:
            results.add_pass("Dual user can login to contributor")
            
            response_data = response.json()
            if response_data.get('role') == 'contributor':
                results.add_pass("Dual user gets contributor role when logging into contributor")
            else:
                results.add_fail("Dual user gets contributor role when logging into contributor", f"Got: {response_data.get('role')}")
        else:
            results.add_fail("Dual user can login to contributor", f"Status: {response.status_code}")
        
        # Cleanup
        cc_profile.delete()
        contrib_profile.delete()
        dual_user.delete()
        
    except Exception as e:
        results.add_fail("Cross authentication prevention tests", f"Exception: {str(e)}")
        traceback.print_exc()
    
    return results


def main():
    """Run all authentication security tests."""
    print("🔒 Starting Authentication Security Tests")
    print("="*60)
    print("Testing the fix for customer care/contributor cross-authentication issue")
    print("="*60)
    
    all_results = TestResults()
    
    # Run all test functions
    test_functions = [
        test_customer_care_authentication,
        test_contributor_authentication,
        test_cross_authentication_prevention
    ]
    
    for test_func in test_functions:
        print(f"\n📋 Running {test_func.__name__}...")
        try:
            result = test_func()
            all_results.passed += result.passed
            all_results.failed += result.failed
            all_results.errors.extend(result.errors)
        except Exception as e:
            all_results.add_fail(test_func.__name__, f"Test function failed: {str(e)}")
            print(f"💥 Error in {test_func.__name__}: {str(e)}")
            traceback.print_exc()
    
    # Print final summary
    all_results.summary()
    
    if all_results.failed == 0:
        print(f"\n🎉 SUCCESS: Authentication security fix is working correctly!")
        print("✅ Customer care and contributor systems are now properly isolated")
        print("✅ No automatic profile creation for unauthorized users")
        print("✅ Proper role validation is enforced")
    else:
        print(f"\n⚠️  WARNING: Some tests failed. Please review the issues above.")


if __name__ == '__main__':
    main()
