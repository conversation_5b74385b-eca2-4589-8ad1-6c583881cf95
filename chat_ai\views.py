from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework import status
from django.apps import apps
import google.generativeai as genai
from rest_framework.permissions import IsAuthenticated
import ast
import re
from .models import ChatHistory
import uuid
import datetime
import decimal


# Gemini setup
genai.configure(api_key='AIzaSyD2y8WM03f5fBDIo2uPfO-r8FiKkYuO7hQ')
gemini_model = genai.GenerativeModel('gemini-2.0-flash')



def clean_for_json(obj):
    if isinstance(obj, list):
        return [clean_for_json(item) for item in obj]
    elif isinstance(obj, dict):
        return {k: clean_for_json(v) for k, v in obj.items()}
    elif isinstance(obj, datetime.timedelta):
        return str(obj)
    elif isinstance(obj, (datetime.datetime, datetime.date)):
        return obj.isoformat()
    elif isinstance(obj, decimal.Decimal):
        return float(obj)
    else:
        return obj

EXCLUDED_FIELDS = {
    'phone', 'user', 'student_id', 'address', 'state', 'gender',
    'created_date', 'updated_date', 'is_active', 'is_deleted',
    'is_verified', 'is_subscribed', 'is_approved', 'is_published',
    'image_url', 'qr_code', 'id', 'created_at', 'updated_at', "expiry_date"
    "pg_order_id", "pg_payment_id", "pg_transaction_id", "price", "amount", 
    "pg_signature", "transaction_id", "referror", "gift_card_used", "discount_price",
}

def get_all_model_metadata():
    metadata = {}
    for model in apps.get_models():
        model_name = model.__name__
        field_names = [field.name for field in model._meta.fields]
        metadata[model_name] = {
            'model': model,
            'fields': field_names
        }
    return metadata

def sanitize_explanation(text):
    return re.sub(r'\b\d{10}\b', '[hidden]', text)

def is_programming_query(query):
    keywords = [
        "python", "code", "snippet", "program", "algorithm", "function", 
        "class", "loop", "recursion", "data structure", "debug", "error",
        "compile", "exception", "method", "variable", "object-oriented",
        "django", "api", "framework", "react", "java", "c++", "html", "css",
        "javascript", "typescript"
    ]
    return any(word in query.lower() for word in keywords)

# Inside your view class
class AIChatbotAPIView(APIView):
    permission_classes = [IsAuthenticated]

    def post(self, request):
        user = request.user if request.user.is_authenticated else None
        query = request.data.get('query', '')

        if not query:
            return Response({'error': 'Query not provided'}, status=status.HTTP_400_BAD_REQUEST)
        
        is_prog_query = is_programming_query(query)

        if is_prog_query:
            short_reply = (
                "Programming-related queries are not supported here. "
                "Please try a different type of question. 🙏"
            )
            
            if user:
                ChatHistory.objects.create(
                    user=user,
                    query=query,
                    response={"explanation": short_reply}
                )
            
            return Response({"results": [{"explanation": short_reply}]})

        model_metadata = get_all_model_metadata()
        all_model_names = list(model_metadata.keys())

        model_list_prompt = f"""
        User asked: "{query}"

        Available models in the system: {all_model_names}

        Based on the query, which model(s) do you think the user is referring to? 
        Provide only a list of relevant model names (like 'Course', 'SubCourse'). 
        If unsure, return 'No models found'.
        """

        try:
            model_list_response = gemini_model.generate_content(model_list_prompt).text.strip()
            model_names = ast.literal_eval(model_list_response)
        except Exception:
            model_names = "No models found"

        final_response = []

        # If no project models matched, fallback to general Gemini response
        if model_names == 'No models found' or not model_names:
            try:
                fallback_answer = gemini_model.generate_content(query).text
                fallback_answer = sanitize_explanation(fallback_answer)
            except Exception as e:
                return Response({
                    'error': 'Gemini quota exceeded or fallback error.',
                    'details': str(e)
                }, status=status.HTTP_429_TOO_MANY_REQUESTS)

            if user:
                ChatHistory.objects.create(
                    user=user,
                    query=query,
                    response={"explanation": fallback_answer}
                )

            return Response({
                "results": [{"explanation": fallback_answer}]
            })

        # Else process relevant models
        for model_name in model_names:
            model_info = model_metadata.get(model_name)
            if not model_info:
                continue

            model_class = model_info['model']
            fields = model_info['fields']
            is_count_query = any(word in query.lower() for word in ['kitne', 'how many', 'total', 'count'])

            model_data = {}

            if is_count_query:
                count = model_class.objects.count()
                model_data['explanation'] = f"Total **{count} {model_class.__name__}s** found in the system! ✅"
            else:
                filter_prompt = f"""
                User's query: "{query}"
                Model: {model_class.__name__}
                Fields: {fields}

                Generate a Python dictionary that can be used for Django ORM filtering based on this query. 
                Only return the dictionary (no explanations).
                """

                try:
                    filter_response = gemini_model.generate_content(filter_prompt)
                    filters = ast.literal_eval(filter_response.text.strip())
                except Exception:
                    filters = {}

                try:
                    data_qs = model_class.objects.filter(**filters)[:5]
                except:
                    data_qs = model_class.objects.all()[:5]

                safe_fields = [f for f in fields if f not in EXCLUDED_FIELDS]
                data_list = list(data_qs.values(*safe_fields))

                explanation_prompt = f"""
                User's query: "{query}"
                Here's the data found in the model **{model_class.__name__}**:
                {data_list}

                Provide a friendly explanation of this data, including any important links (if 'url' exists).
                """

                try:
                    explanation = gemini_model.generate_content(explanation_prompt).text
                    explanation = sanitize_explanation(explanation)
                except Exception:
                    explanation = (
                        "Could not generate explanation due to Gemini API quota limit or error. "
                        "Please try again later. ⚠️"
                    )

                model_data['explanation'] = explanation

            final_response.append(model_data)

        if user:
            cleaned_response = clean_for_json(final_response)
            ChatHistory.objects.create(
                user=user,
                query=query,
                response=cleaned_response
            )   

        if not final_response:
            return Response({'error': 'No relevant data found.'}, status=status.HTTP_404_NOT_FOUND)

        return Response({'results': final_response})


    def get(self, request):
        chats = ChatHistory.objects.filter(user=request.user).order_by('-created_at')[:20]
        return Response({
            "history": [
                {
                    "query": chat.query,
                    "response": chat.response,
                    "created_at": chat.created_at,
                } for chat in chats
            ]
        })