from django.urls import path
from .views import (
    ContactSyncView,
    UserContactsView,
    MatchedContactsView,
    ContactSearchView,
    ContactStatsView,
    AdminContactsView
)

app_name = 'contacts'

urlpatterns = [
    # Contact sync from Android app
    path('sync/', ContactSyncView.as_view(), name='contact_sync'),

    # User's synced contacts
    path('my-contacts/', UserContactsView.as_view(), name='user_contacts'),

    # Matched contacts (who from my contacts is on the app)
    path('matched/', MatchedContactsView.as_view(), name='matched_contacts'),

    # Contact search
    path('search/', ContactSearchView.as_view(), name='search'),

    # Contact statistics
    path('stats/', ContactStatsView.as_view(), name='stats'),

    # Admin-only endpoints
    path('admin/contacts/', AdminContactsView.as_view(), name='admin_contacts'),
]
