from django.utils.text import slugify
from unidecode import unidecode

def check_unique_slug(model_class, title):
    # Convert Hindi or any unicode to Latin
    raw_title = unidecode(title)
    slug = slugify(raw_title)
    return model_class.objects.filter(slug=slug).exists()

def generate_unique_slug(model_class, title, max_length=30):
    """
    Generates a unique slug for the given model_class based on the title.
    Supports Hindi/non-English by converting to Latin using unidecode.
    """
    # Convert to Latin first
    raw_title = unidecode(title)
    slug = slugify(raw_title)

    if len(slug) > max_length:
        slug = slug[:max_length].rstrip('-')

    unique_slug = slug
    count = 1

    while model_class.objects.filter(slug=unique_slug).exists():
        unique_slug = f"{slug[:max_length - len(str(count)) - 1]}-{count}"
        count += 1

    return unique_slug
