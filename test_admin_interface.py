#!/usr/bin/env python3
"""
Admin Interface Testing Script for Course-Related Django Components
This script performs comprehensive testing of Django admin interface for all course-related models.
"""

import os
import sys
import django
import traceback

# Add the project directory to Python path
sys.path.insert(0, '/Users/<USER>/Documents/code/shash_b')

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'shashtrarth.settings')
django.setup()

# Override ALLOWED_HOSTS for testing
from django.conf import settings
if 'testserver' not in settings.ALLOWED_HOSTS:
    settings.ALLOWED_HOSTS.append('testserver')

# Import after Django setup
from django.contrib import admin
from django.contrib.auth.models import User
from django.test import Client
from questions.models import (
    Course, SubCourse, Tier, Paper, Section, Module, Subject, Topic, SubTopic,
    MasterQuestion, MasterOption, Question, Option, PreviousYearQuestion
)
from contributor.models import ContributorProfile


class TestResults:
    """Class to track test results."""
    
    def __init__(self):
        self.passed = 0
        self.failed = 0
        self.errors = []
    
    def add_pass(self, test_name):
        self.passed += 1
        print(f"✅ PASS: {test_name}")
    
    def add_fail(self, test_name, error):
        self.failed += 1
        self.errors.append(f"{test_name}: {error}")
        print(f"❌ FAIL: {test_name} - {error}")
    
    def summary(self):
        total = self.passed + self.failed
        print(f"\n{'='*60}")
        print(f"ADMIN INTERFACE TEST SUMMARY")
        print(f"{'='*60}")
        print(f"Total Tests: {total}")
        print(f"Passed: {self.passed}")
        print(f"Failed: {self.failed}")
        print(f"Success Rate: {(self.passed/total*100):.1f}%" if total > 0 else "No tests run")
        
        if self.errors:
            print(f"\n{'='*60}")
            print(f"FAILED TESTS:")
            print(f"{'='*60}")
            for error in self.errors:
                print(f"- {error}")


def test_admin_model_registration():
    """Test that all course-related models are registered in admin."""
    results = TestResults()
    
    try:
        # List of models that should be registered in admin
        expected_models = [
            Course,
            SubCourse,
            Tier,
            Paper,
            Section,
            Module,
            Subject,
            Topic,
            SubTopic,
            MasterQuestion,
            MasterOption,
            Question,
            Option,
            PreviousYearQuestion
        ]
        
        for model in expected_models:
            if model in admin.site._registry:
                results.add_pass(f"Admin registration: {model.__name__}")
            else:
                results.add_fail(f"Admin registration: {model.__name__}", "Model not registered in admin")
        
    except Exception as e:
        results.add_fail("Admin model registration tests", f"Exception: {str(e)}")
    
    return results


def test_admin_model_admin_classes():
    """Test admin classes for course-related models."""
    results = TestResults()
    
    try:
        # Test that registered models have proper admin classes
        registered_models = [
            Course,
            SubCourse,
            Tier,
            Paper,
            Section,
            Module,
            Subject,
            Topic,
            SubTopic,
            MasterQuestion,
            MasterOption,
            Question,
            Option,
            PreviousYearQuestion
        ]
        
        for model in registered_models:
            if model in admin.site._registry:
                admin_class = admin.site._registry[model]
                
                # Test that admin class exists
                if admin_class:
                    results.add_pass(f"Admin class exists: {model.__name__}")
                else:
                    results.add_fail(f"Admin class exists: {model.__name__}", "No admin class found")
                
                # Test that admin class has basic attributes
                if hasattr(admin_class, 'list_display') or hasattr(admin_class, 'fields'):
                    results.add_pass(f"Admin class has configuration: {model.__name__}")
                else:
                    # This is not necessarily a failure as Django provides defaults
                    results.add_pass(f"Admin class uses defaults: {model.__name__}")
            else:
                results.add_fail(f"Admin class test: {model.__name__}", "Model not registered")
        
    except Exception as e:
        results.add_fail("Admin model admin classes tests", f"Exception: {str(e)}")
    
    return results


def test_admin_interface_accessibility():
    """Test admin interface accessibility."""
    results = TestResults()
    
    try:
        # Create a superuser for testing
        import uuid
        unique_username = f'admin_{uuid.uuid4().hex[:8]}'
        admin_user = User.objects.create_superuser(
            username=unique_username,
            email='<EMAIL>',
            password='admin123'
        )
        
        client = Client()
        
        # Test 1: Admin login
        login_response = client.login(username=unique_username, password='admin123')
        if login_response:
            results.add_pass("Admin login successful")
        else:
            results.add_fail("Admin login successful", "Login failed")
            return results
        
        # Test 2: Admin index page
        response = client.get('/admin/')
        if response.status_code == 200:
            results.add_pass("Admin index page accessible")
        else:
            results.add_fail("Admin index page accessible", f"Status code: {response.status_code}")
        
        # Test 3: Questions app in admin
        response = client.get('/admin/questions/')
        if response.status_code == 200:
            results.add_pass("Questions app admin page accessible")
        else:
            results.add_fail("Questions app admin page accessible", f"Status code: {response.status_code}")
        
        # Test 4: Course model admin
        response = client.get('/admin/questions/course/')
        if response.status_code == 200:
            results.add_pass("Course model admin page accessible")
        else:
            results.add_fail("Course model admin page accessible", f"Status code: {response.status_code}")
        
        # Test 5: Subject model admin
        response = client.get('/admin/questions/subject/')
        if response.status_code == 200:
            results.add_pass("Subject model admin page accessible")
        else:
            results.add_fail("Subject model admin page accessible", f"Status code: {response.status_code}")
        
        # Test 6: Question model admin
        response = client.get('/admin/questions/question/')
        if response.status_code == 200:
            results.add_pass("Question model admin page accessible")
        else:
            results.add_fail("Question model admin page accessible", f"Status code: {response.status_code}")
        
        # Cleanup
        admin_user.delete()
        
    except Exception as e:
        results.add_fail("Admin interface accessibility tests", f"Exception: {str(e)}")
    
    return results


def test_admin_model_operations():
    """Test basic CRUD operations through admin interface."""
    results = TestResults()
    
    try:
        # Create a superuser for testing
        import uuid
        unique_username = f'admin_{uuid.uuid4().hex[:8]}'
        admin_user = User.objects.create_superuser(
            username=unique_username,
            email='<EMAIL>',
            password='admin123'
        )
        
        client = Client()
        client.login(username=unique_username, password='admin123')
        
        # Test 1: Course add page
        response = client.get('/admin/questions/course/add/')
        if response.status_code == 200:
            results.add_pass("Course add page accessible")
        else:
            results.add_fail("Course add page accessible", f"Status code: {response.status_code}")
        
        # Test 2: Subject add page
        response = client.get('/admin/questions/subject/add/')
        if response.status_code == 200:
            results.add_pass("Subject add page accessible")
        else:
            results.add_fail("Subject add page accessible", f"Status code: {response.status_code}")
        
        # Test 3: Create a course through admin (if possible)
        course_data = {
            'name': 'Admin Test Course',
            'description': 'Course created through admin interface'
        }
        response = client.post('/admin/questions/course/add/', data=course_data)
        if response.status_code in [200, 302]:  # 302 for redirect after successful creation
            results.add_pass("Course creation through admin")
            
            # Check if course was actually created
            if Course.objects.filter(name='Admin Test Course').exists():
                results.add_pass("Course actually created through admin")
                
                # Test course edit page
                course = Course.objects.get(name='Admin Test Course')
                response = client.get(f'/admin/questions/course/{course.course_id}/change/')
                if response.status_code == 200:
                    results.add_pass("Course edit page accessible")
                else:
                    results.add_fail("Course edit page accessible", f"Status code: {response.status_code}")
                
                # Cleanup
                course.delete()
            else:
                results.add_fail("Course actually created through admin", "Course not found in database")
        else:
            results.add_fail("Course creation through admin", f"Status code: {response.status_code}")
        
        # Test 4: Create a subject through admin
        subject_data = {
            'name': 'Admin Test Subject',
            'description': 'Subject created through admin interface',
            'rank': 1
        }
        response = client.post('/admin/questions/subject/add/', data=subject_data)
        if response.status_code in [200, 302]:
            results.add_pass("Subject creation through admin")
            
            # Check if subject was actually created
            if Subject.objects.filter(name='Admin Test Subject').exists():
                results.add_pass("Subject actually created through admin")
                
                # Cleanup
                Subject.objects.filter(name='Admin Test Subject').delete()
            else:
                results.add_fail("Subject actually created through admin", "Subject not found in database")
        else:
            results.add_fail("Subject creation through admin", f"Status code: {response.status_code}")
        
        # Cleanup
        admin_user.delete()
        
    except Exception as e:
        results.add_fail("Admin model operations tests", f"Exception: {str(e)}")
    
    return results


def test_admin_search_and_filters():
    """Test admin search and filter functionality."""
    results = TestResults()
    
    try:
        # Create test data
        course = Course.objects.create(name='Search Test Course')
        subject = Subject.objects.create(name='Search Test Subject', rank=1)
        
        # Create a superuser for testing
        import uuid
        unique_username = f'admin_{uuid.uuid4().hex[:8]}'
        admin_user = User.objects.create_superuser(
            username=unique_username,
            email='<EMAIL>',
            password='admin123'
        )
        
        client = Client()
        client.login(username=unique_username, password='admin123')
        
        # Test 1: Course list with search
        response = client.get('/admin/questions/course/?q=Search')
        if response.status_code == 200:
            results.add_pass("Course admin search functionality")
        else:
            results.add_fail("Course admin search functionality", f"Status code: {response.status_code}")
        
        # Test 2: Subject list with search
        response = client.get('/admin/questions/subject/?q=Search')
        if response.status_code == 200:
            results.add_pass("Subject admin search functionality")
        else:
            results.add_fail("Subject admin search functionality", f"Status code: {response.status_code}")
        
        # Test 3: Subject list with ordering
        response = client.get('/admin/questions/subject/?o=1')  # Order by first field
        if response.status_code == 200:
            results.add_pass("Subject admin ordering functionality")
        else:
            results.add_fail("Subject admin ordering functionality", f"Status code: {response.status_code}")
        
        # Cleanup
        course.delete()
        subject.delete()
        admin_user.delete()
        
    except Exception as e:
        results.add_fail("Admin search and filters tests", f"Exception: {str(e)}")
    
    return results


def main():
    """Run all admin interface tests."""
    print("🚀 Starting Comprehensive Admin Interface Tests")
    print("="*60)
    
    all_results = TestResults()
    
    # Run all test functions
    test_functions = [
        test_admin_model_registration,
        test_admin_model_admin_classes,
        test_admin_interface_accessibility,
        test_admin_model_operations,
        test_admin_search_and_filters
    ]
    
    for test_func in test_functions:
        print(f"\n📋 Running {test_func.__name__}...")
        try:
            result = test_func()
            all_results.passed += result.passed
            all_results.failed += result.failed
            all_results.errors.extend(result.errors)
        except Exception as e:
            all_results.add_fail(test_func.__name__, f"Test function failed: {str(e)}")
            print(f"💥 Error in {test_func.__name__}: {str(e)}")
            traceback.print_exc()
    
    # Print final summary
    all_results.summary()


if __name__ == '__main__':
    main()
