1. install dependencies from requirements.txt
2. add "log_admin" installed app in settings
3. add "log_admin.middleware.PerformanceMonitorMiddleware" into middleware settings
4. install reddis on server and start it 
5. add 
    CACHES = {
        "default": {
            "BACKEND": "django_redis.cache.RedisCache",
            "LOCATION": "redis://127.0.0.1:6379/1", 
            "OPTIONS": {
                "CLIENT_CLASS": "django_redis.client.DefaultClient",
            }
        }
    }
    this into settings 

6.  run makemigrations & migrate 