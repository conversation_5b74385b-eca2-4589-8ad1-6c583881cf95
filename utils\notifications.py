import requests
import json
from django.http import JsonResponse
from django.views.decorators.csrf import csrf_exempt

def send_sms_bulk(phone, message, temp_id):
    """
    Function to send SMS using BulkSMSPlans API.
    
    Args:
        phone_number (str): Recipient's phone number.
        message (str): SMS message content.

    Returns:
        dict: Response from the SMS API.
    """
    # Define the SMS payload
    sms_data = {
        'api_id': 'APIm4H3ebTY132362',  # Replace with actual API credentials
        'api_password': 'o9TpHXI1',
        'sms_type': 'TEXT',  # or other types like OTP
        'sms_encoding': 'Plain',
        'sender': 'SASTRH',  # Replace with your sender ID
        'number': phone,
        'message': message,
        'template_id': temp_id # If required
    }

    # Convert data to JSON
    sms_payload = json.dumps(sms_data)

    # API URL
    url = 'https://www.bulksmsplans.com/api/send_sms'

    try:
        # Make the POST request
        response = requests.post(
            url,
            data=sms_payload,
            headers={'Content-Type': 'application/json'}
        )

        return response.json()  # Convert response to dictionary

    except requests.exceptions.RequestException as e:
        return {'error': str(e)}
    
