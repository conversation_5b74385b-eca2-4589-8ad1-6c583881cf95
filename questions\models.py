from django.db import models
from contributor.models import ContributorProfile
from blogs.models import BlogPost
from django.utils.text import slugify
from students.models import Student
from django.db import models
from .utils import generate_unique_slug
from django.db.models.signals import post_save
from django.dispatch import receiver
from django.core.cache import cache

class Course(models.Model):
    course_id = models.AutoField(primary_key=True)
    name = models.CharField(max_length=225, null=True, blank=True)
    description = models.TextField(null=True, blank=True)
    attachments = models.ImageField(upload_to="courses/", null=True, blank=True)
    created_date = models.DateTimeField(auto_now_add=True)
    updated_date = models.DateTimeField(auto_now=True)
    slug = models.SlugField(max_length=255, unique=True, blank=True)

    class Meta:
        app_label = "questions"

    def __str__(self):
        return self.name if self.name else "Unnamed Course"

    def save(self, *args, **kwargs):
        if not self.slug:
            self.slug = generate_unique_slug(Course, f"{self.name}")

        super().save(*args, **kwargs)


class SubCourse(models.Model):
    subcourse_id = models.AutoField(primary_key=True)
    course = models.ForeignKey(
        Course,
        on_delete=models.CASCADE,
        related_name="sub_courses",
        null=True,
        blank=True,
    )
    name = models.CharField(max_length=225, null=True, blank=True)
    description = models.TextField(null=True, blank=True)
    created_date = models.DateTimeField(auto_now_add=True)
    updated_date = models.DateTimeField(auto_now=True)
    slug = models.SlugField(unique=True, blank=True)

    class Meta:
        app_label = "questions"

    def save(self, *args, **kwargs):
        if not self.slug:
            self.slug = generate_unique_slug(SubCourse, f"{self.course.name}-{self.name}")
        super().save(*args, **kwargs)

    def __str__(self):
        return (
            f"{self.name} ({self.course.name})"
            if self.name and self.course
            else "Unnamed SubCourse"
        )


class Tier(models.Model):
    tier_id = models.AutoField(primary_key=True)
    subcourse = models.ForeignKey(
        SubCourse, on_delete=models.CASCADE, related_name="tiers", null=True, blank=True
    )
    test_pattern = models.ForeignKey(
        "paper_engine.TestPattern",
        verbose_name="Test Pattern",
        on_delete=models.CASCADE,
        related_name="tier",
        null=True,
        blank=True,
    )
    name = models.CharField(max_length=225, null=True, blank=True)
    description = models.TextField(null=True, blank=True)
    created_date = models.DateTimeField(auto_now_add=True)
    updated_date = models.DateTimeField(auto_now=True)
    slug = models.SlugField(unique=True, blank=True)

    def save(self, *args, **kwargs):
        if not self.slug:
            self.slug = generate_unique_slug(Tier, f"{self.subcourse.name}-{self.name}")
        super().save(*args, **kwargs)

    def __str__(self):
        return (
            f"{self.name} ({self.subcourse.name})"
            if self.name and self.subcourse
            else "Unnamed Tier"
        )


class Paper(models.Model):
    paper_id = models.AutoField(primary_key=True)
    tier = models.ForeignKey(
        Tier, on_delete=models.CASCADE, related_name="papers", null=True, blank=True
    )
    test_pattern = models.ForeignKey(
        "paper_engine.TestPattern",
        verbose_name="Test Pattern",
        on_delete=models.CASCADE,
        related_name="paper",
        null=True,
        blank=True,
    )
    name = models.CharField(max_length=225, null=True, blank=True)
    student = models.ForeignKey(Student, on_delete=models.CASCADE ,related_name="student_paper", null=True, blank=True)
    description = models.TextField(null=True, blank=True)
    max_marks = models.IntegerField(null=True, blank=True)
    duration = models.DurationField(null=True, blank=True)
    created_date = models.DateTimeField(auto_now_add=True)
    updated_date = models.DateTimeField(auto_now=True)
    slug = models.SlugField(unique=True, blank=True)


    def save(self, *args, **kwargs):
        if not self.slug:
            self.slug = generate_unique_slug(Paper, f"{self.tier.name}-{self.name}")
        super().save(*args, **kwargs)

    def __str__(self):
        return (
            f"{self.name} ({self.tier.name})"
            if self.name and self.tier
            else "Unnamed Paper"
        )


class Section(models.Model):
    section_id = models.AutoField(primary_key=True)
    paper = models.ForeignKey(
        Paper, on_delete=models.CASCADE, related_name="sections", null=True, blank=True
    )
    test_pattern = models.ForeignKey(
        "paper_engine.TestPattern",
        verbose_name="Test Pattern",
        on_delete=models.CASCADE,
        related_name="section",
        null=True,
        blank=True,
    )
    name = models.CharField(max_length=225, null=True, blank=True)
    description = models.TextField(null=True, blank=True)
    max_marks = models.IntegerField(null=True, blank=True)
    number_of_questions = models.IntegerField(null=True, blank=True)
    created_date = models.DateTimeField(auto_now_add=True)
    updated_date = models.DateTimeField(auto_now=True)
    slug = models.SlugField(unique=True, blank=True)

    def save(self, *args, **kwargs):
        if not self.slug:
            self.slug = generate_unique_slug(Section, f"{self.paper.name}-{self.name}")
        super().save(*args, **kwargs)

    def __str__(self):
        return (
            f"{self.name} ({self.paper.name})"
            if self.name and self.paper
            else "Unnamed Section"
        )


class Module(models.Model):
    module_id = models.AutoField(primary_key=True)
    section = models.ForeignKey(
        Section, on_delete=models.CASCADE, related_name="modules", null=True, blank=True
    )
    test_pattern = models.ForeignKey(
        "paper_engine.TestPattern",
        verbose_name="Test Pattern",
        on_delete=models.CASCADE,
        related_name="sub_courses",
        null=True,
        blank=True,
    )
    name = models.CharField(max_length=225, null=True, blank=True)
    description = models.TextField(null=True, blank=True)
    created_date = models.DateTimeField(auto_now_add=True)
    updated_date = models.DateTimeField(auto_now=True)
    slug = models.SlugField(unique=True, blank=True)

    def save(self, *args, **kwargs):
        if not self.slug:
            self.slug = generate_unique_slug(Module, self.name)
        super().save(*args, **kwargs)

    def __str__(self):
        return (
            f"{self.name} ({self.section.name})"
            if self.name and self.section
            else "Unnamed Module"
        )


class Subject(models.Model):
    subject_id = models.AutoField(primary_key=True)
    name = models.CharField(max_length=225)
    description = models.TextField(null=True, blank=True)
    created_date = models.DateTimeField(auto_now_add=True)
    updated_date = models.DateTimeField(auto_now=True)
    slug = models.SlugField(unique=True, blank=True)
    rank = models.IntegerField(default=0)  # Add a rank field to the Subject model
    def save(self, *args, **kwargs):
        if not self.slug:
            self.slug = generate_unique_slug(Subject, self.name)
        super().save(*args, **kwargs)

    def __str__(self):
        return self.name


class Topic(models.Model):
    topic_id = models.AutoField(primary_key=True)
    subject = models.ForeignKey(
        Subject, on_delete=models.CASCADE, related_name="topics"
    )
    name = models.CharField(max_length=225)
    description = models.TextField(null=True, blank=True)
    created_date = models.DateTimeField(auto_now_add=True)
    updated_date = models.DateTimeField(auto_now=True)
    slug = models.SlugField(unique=True, blank=True)

    def save(self, *args, **kwargs):
        if not self.slug:
            self.slug = generate_unique_slug(Topic, f"{self.subject.name}-{self.name}")
        super().save(*args, **kwargs)

    def __str__(self):
        return f"{self.name} ({self.subject.name})"


class SubTopic(models.Model):
    subtopic_id = models.AutoField(primary_key=True)
    topic = models.ForeignKey(Topic, on_delete=models.CASCADE, related_name="subtopics")
    name = models.CharField(max_length=225)
    description = models.TextField(null=True, blank=True)
    created_date = models.DateTimeField(auto_now_add=True)
    updated_date = models.DateTimeField(auto_now=True)
    slug = models.SlugField(unique=True, blank=True)

    def save(self, *args, **kwargs):
        if not self.slug:
            self.slug = generate_unique_slug(SubTopic, f"{self.topic.name}-{self.name}")
        super().save(*args, **kwargs)

    def __str__(self):
        return f"{self.name} ({self.topic.name})"


class MasterQuestion(models.Model):
    """
    Model to represent a master question for unseen passages, which can have multiple questions.
    """

    master_question_id = models.AutoField(primary_key=True)
    author = models.ForeignKey(ContributorProfile, on_delete=models.CASCADE)
    title = models.CharField(max_length=225, null=True, blank=True)
    passage_content = models.TextField(
        help_text="Content of the passage for related questions."
    )
    current_affairs = models.ForeignKey(
        "blogs.BlogPost",
        verbose_name="current_affairs",
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name="master_question",
        help_text="Blogs that this question is related to.",
    )
    attachments = models.ImageField(upload_to="masterquestion/", null=True, blank=True)
    is_current_affairs = models.BooleanField(default=False)
    approval_status = models.CharField(max_length=10, default="pending")
    created_at = models.DateTimeField(auto_now_add=True)
    reason = models.CharField(max_length=500, null=True, blank=True)
    reason_document = models.ImageField(upload_to="reason/", null=True, blank=True)
    updated_by_custmorcare = models.ForeignKey(
        "customrcare.CustomrcareProfile",
        on_delete=models.CASCADE,
        null=True,
        blank=True,
    )
    updated_at = models.DateTimeField(auto_now=True)
    slug = models.SlugField(unique=True, blank=True)

    class Meta:
        app_label = "questions"

    def save(self, *args, **kwargs):
        if not self.slug:
            self.slug = generate_unique_slug(MasterQuestion, self.title)
        super().save(*args, **kwargs)

    def __str__(self):
        return f"MasterQuestion: {self.title or 'Untitled'}"


class MasterOption(models.Model):
    """
    Model to represent a master option for unseen passages, which can have multiple options.
    """

    master_option_id = models.AutoField(primary_key=True)
    author = models.ForeignKey(ContributorProfile, on_delete=models.CASCADE)
    title = models.CharField(max_length=225, null=True, blank=True)
    option_content = models.TextField(
        help_text="Content of the option for related questions."
    )
    conditions = models.TextField(
        help_text="Conditions for displaying this option.", null=True, blank=True
    )
    current_affairs = models.ForeignKey(
        "blogs.BlogPost",
        verbose_name="current_affairs",
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name="master_option",
        help_text="Blogs that this question is related to.",
    )
    attachments = models.ImageField(upload_to="masteroption/", null=True, blank=True)
    is_current_affairs = models.BooleanField(default=False)
    approval_status = models.CharField(max_length=10, default="pending")
    created_at = models.DateTimeField(auto_now_add=True)
    updated_by_custmorcare = models.ForeignKey(
        "customrcare.CustomrcareProfile",
        on_delete=models.CASCADE,
        null=True,
        blank=True,
    )
    reason = models.CharField(max_length=500, null=True, blank=True)
    reason_document = models.ImageField(upload_to="reason/", null=True, blank=True)
    updated_at = models.DateTimeField(auto_now=True)
    slug = models.SlugField(unique=True, blank=True)

    class Meta:
        app_label = "questions"

    def save(self, *args, **kwargs):
        if not self.slug:
            self.slug = generate_unique_slug(MasterOption,self.title)
        super().save(*args, **kwargs)

    def __str__(self):
        return f"MasterOption: {self.title or 'Untitled'}"


class Question(models.Model):
    question_id = models.AutoField(primary_key=True)
    subject = models.ManyToManyField(Subject)
    subject_name = models.TextField(null=True, blank=True)
    topic = models.ManyToManyField(Topic)
    topic_name = models.TextField(null=True, blank=True)
    sub_topic = models.ManyToManyField(SubTopic)
    sub_topic_name = models.TextField(null=True, blank=True)
    content = models.TextField(null=True, blank=True)
    difficulty = models.IntegerField(default=1)
    author = models.ForeignKey(ContributorProfile, on_delete=models.CASCADE)
    current_affairs = models.ForeignKey(
        "blogs.BlogPost",
        verbose_name="current_affairs",
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name="questions",
        help_text="Blogs that this question is related to.",
    )
    is_current_affairs = models.BooleanField(default=False)
    created_at = models.DateTimeField(auto_now_add=True)
    status = models.CharField(max_length=10, default="inactive")
    approval_status = models.CharField(max_length=10, default="pending")
    updated_by_custmorcare = models.ForeignKey(
        "customrcare.CustomrcareProfile",
        on_delete=models.CASCADE,
        null=True,
        blank=True,
    )
    updated_at = models.DateTimeField(auto_now=True)
    average_score = models.FloatField(null=True, blank=True)
    times_attempted = models.IntegerField(null=True, blank=True)
    attachments = models.ImageField(upload_to="questions/", null=True, blank=True)
    language = models.CharField(max_length=225, default="english")
    course = models.ManyToManyField(Course, db_index=True)
    subcourse = models.ManyToManyField(SubCourse)
    paper = models.ForeignKey(
        Paper,
        on_delete=models.CASCADE,
        null=True,
        blank=True,
        )
    master_question = models.ForeignKey(
        MasterQuestion,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name="questions",
        help_text="Select a master question if this is part of an unseen passage.",
    )
    is_master = models.BooleanField(
        default=False, help_text="Indicates if this question is a master question."
    )
    master_option = models.ForeignKey(
        MasterOption,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name="related_questions",
        help_text="Select a master option if this is part of an unseen passage.",
    )
    is_master_option = models.BooleanField(
        default=False, help_text="Indicates if this question is a master option."
    )
    slug = models.SlugField(unique=True, blank=True)
    reason = models.CharField(max_length=500, null=True, blank=True)
    reason_document = models.ImageField(upload_to="reason/", null=True, blank=True)
    explanation = models.TextField(null=True, blank=True)
    explanation_attachment = models.ImageField(
        upload_to="explanation/", null=True, blank=True
    )
    class Meta:
        app_label = "questions"

    def save(self, *args, **kwargs):
        if not self.slug:
            self.slug= generate_unique_slug(Question,f"{self.content}")

        super().save(*args, **kwargs)
      

    def __str__(self):
        return f"{self.question_id} | {self.status}"

    def get_difficulty_level(self):
        if 1 <= self.difficulty <= 4:
            return "Easy"
        elif 5 <= self.difficulty <= 7:
            return "Medium"
        elif 8 <= self.difficulty <= 9:
            return "Hard"
        elif self.difficulty == 10:
            return "Extremely Hard"
        else:
            return "Invalid difficulty level"


class Option(models.Model):
    option_id = models.AutoField(primary_key=True)
    question = models.ForeignKey(
        Question, on_delete=models.CASCADE, related_name="options"
    )
    option_text = models.TextField()
    is_correct = models.BooleanField(default=False)
    created_at = models.DateTimeField(auto_now_add=True)
    attachments = models.ImageField(upload_to="options/", null=True, blank=True)
    created_date = models.DateTimeField(auto_now_add=True)
    updated_date = models.DateTimeField(auto_now=True)
    slug = models.SlugField(unique=True, blank=True)

    class Meta:
        app_label = "questions"

    def save(self, *args, **kwargs):
        if not self.slug:
            self.slug = generate_unique_slug(Option, self.option_text)
        super().save(*args, **kwargs)

    def __str__(self):
        return self.option_text


class PreviousYearQuestion(models.Model):
    question = models.ForeignKey(
        "Question",
        on_delete=models.CASCADE,
        related_name="previous_year_questions",
        help_text="Reference to the original question.",
    )
    year = models.IntegerField(help_text="Year of the previous question.")
    month = models.CharField(
        max_length=255, help_text="Month of the previous question."
    )
    course = models.ForeignKey(
        Course, verbose_name=("course"), on_delete=models.CASCADE
    )
    exams = models.ForeignKey(
        SubCourse, verbose_name=("subcourse"), on_delete=models.CASCADE
    )
    status = models.CharField(
        max_length=15,
        choices=(("active", "Active"), ("discontinued", "Discontinued")),
        default="active",
        help_text="Status of the question for the current year.",
    )
    approval_status = models.CharField(max_length=10, default="pending")
    note = models.TextField(
        null=True,
        blank=True,
        help_text="Additional notes related to the previous year question.",
    )
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    slug = models.SlugField(unique=True, blank=True)

    class Meta:
        app_label = "questions"
        ordering = ["-year", "-month"]
        verbose_name = "Previous Year Question"
        verbose_name_plural = "Previous Year Questions"

    def save(self, *args, **kwargs):
        if not self.slug:
            self.slug = generate_unique_slug(Option, f"{self.question.content}-{self.year}")
        super().save(*args, **kwargs)

    def __str__(self):
        return f"{self.question.content}-{self.year}"


