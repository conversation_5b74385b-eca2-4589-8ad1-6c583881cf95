from django.urls import path, include
from rest_framework.routers import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
from .views import (
    health_check, PerformanceLogListView, ErrorLogListView,
    UserActivityListView, APIAccessLogListView, DatabaseQueryLogListView,
    AuthenticationLogListView, SecurityIncidentListView, SystemHealthLogListView,
    LogAnalyticsView, LogDashboardView, LogConfigView, LogConfigListView,
    LogConfigActivateView, LogConfigTestView, LogCleanupView, LogRetentionPolicyView,
    LogSearchView
)

# API URL patterns
urlpatterns = [
    # Health check
    path("health/", health_check, name="health-check"),

    # Log list endpoints
    path("performance/", PerformanceLogListView.as_view(), name="performance-logs"),
    path("errors/", ErrorLogListView.as_view(), name="error-logs"),
    path("activities/", UserActivityListView.as_view(), name="user-activities"),
    path("api-access/", APIAccessLogListView.as_view(), name="api-access-logs"),
    path("database-queries/", DatabaseQueryLogListView.as_view(), name="database-query-logs"),
    path("authentication/", AuthenticationLogListView.as_view(), name="authentication-logs"),
    path("security-incidents/", SecurityIncidentListView.as_view(), name="security-incidents"),
    path("system-health/", SystemHealthLogListView.as_view(), name="system-health-logs"),

    # Analytics and dashboard
    path("analytics/", LogAnalyticsView.as_view(), name="log-analytics"),
    path("dashboard/", LogDashboardView.as_view(), name="log-dashboard"),

    # Configuration and management
    path("config/", LogConfigView.as_view(), name="log-config"),
    path("config/list/", LogConfigListView.as_view(), name="log-config-list"),
    path("config/<int:config_id>/activate/", LogConfigActivateView.as_view(), name="log-config-activate"),
    path("config/test/", LogConfigTestView.as_view(), name="log-config-test"),
    path("cleanup/", LogCleanupView.as_view(), name="log-cleanup"),
    path("retention/", LogRetentionPolicyView.as_view(), name="log-retention"),
    path("search/", LogSearchView.as_view(), name="log-search"),
]
