from rest_framework import serializers
from django.contrib.auth.models import User
from django.core.cache import cache


class PasswordResetRequestSerializer(serializers.Serializer):
    email = serializers.EmailField()

    def validate_email(self, value):
        try:
            # Check if the email exists in the system
            user = User.objects.get(email=value)
        except User.DoesNotExist:
            raise serializers.ValidationError("Email not found.")
        return value


class OTPVerificationSerializer(serializers.Serializer):
    email = serializers.EmailField()
    otp = serializers.CharField(max_length=6)
    new_password = serializers.CharField(write_only=True)
    confirm_password = serializers.CharField(write_only=True)

    def validate(self, data):
        if data["new_password"] != data["confirm_password"]:
            raise serializers.ValidationError("Passwords do not match.")
        return data

    def validate_otp(self, value):
        email = self.initial_data.get("email")
        stored_otp = cache.get(email)

        if stored_otp != value:
            raise serializers.ValidationError(f"Invalid OTP.{stored_otp}")
        return value

    def save(self):
        email = self.validated_data["email"]
        user = User.objects.get(email=email)
        user.set_password(self.validated_data["new_password"])
        user.save()

        # Delete OTP after successful password reset
        cache.delete(email)
        return user
