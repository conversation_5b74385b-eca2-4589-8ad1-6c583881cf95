"""
Serializers for log_admin models
"""

from rest_framework import serializers
from django.contrib.auth.models import User
from .models import (
    LogConfig, PerformanceLog, ErrorLog, UserActivity,
    APIAccessLog, DatabaseQueryLog, AuthenticationLog,
    SecurityIncident, SystemHealthLog
)


class UserBasicSerializer(serializers.ModelSerializer):
    """Basic user serializer for log entries"""
    class Meta:
        model = User
        fields = ['id', 'username', 'email', 'first_name', 'last_name']


class LogConfigSerializer(serializers.ModelSerializer):
    """Enhanced serializer for logging configuration"""
    created_by = UserBasicSerializer(read_only=True)
    updated_by = UserBasicSerializer(read_only=True)
    alert_emails = serializers.SerializerMethodField()

    class Meta:
        model = LogConfig
        fields = [
            'id', 'name', 'description', 'is_active',
            'level', 'enable_file_logging', 'enable_console_logging', 'enable_database_logging',
            'enable_performance_logging', 'enable_error_logging', 'enable_activity_logging',
            'enable_api_logging', 'enable_db_logging', 'enable_auth_logging',
            'enable_security_logging', 'enable_health_logging',
            'max_log_entries', 'batch_size', 'flush_interval',
            'log_retention_days', 'error_retention_days', 'security_retention_days',
            'enable_email_alerts', 'alert_email_addresses', 'critical_error_threshold',
            'alert_cooldown_minutes', 'enable_log_compression', 'enable_log_rotation',
            'max_log_file_size_mb', 'enable_sampling', 'sampling_rate',
            'created_at', 'updated_at', 'created_by', 'updated_by', 'alert_emails'
        ]
        read_only_fields = ['id', 'created_at', 'updated_at', 'created_by', 'updated_by']

    def get_alert_emails(self, obj):
        """Get parsed alert email addresses"""
        return obj.get_alert_emails()

    def validate_alert_email_addresses(self, value):
        """Validate email addresses format"""
        if value:
            emails = [email.strip() for email in value.split(',') if email.strip()]
            from django.core.validators import validate_email
            from django.core.exceptions import ValidationError as DjangoValidationError

            for email in emails:
                try:
                    validate_email(email)
                except DjangoValidationError:
                    raise serializers.ValidationError(f"Invalid email address: {email}")

        return value

    def validate(self, data):
        """Validate configuration data"""
        # Ensure sampling rate is valid when sampling is enabled
        if data.get('enable_sampling', False):
            sampling_rate = data.get('sampling_rate', 1.0)
            if not (0.01 <= sampling_rate <= 1.0):
                raise serializers.ValidationError({
                    'sampling_rate': 'Sampling rate must be between 0.01 and 1.0'
                })

        # Validate retention days
        retention_days = data.get('log_retention_days', 30)
        error_retention = data.get('error_retention_days', 90)
        security_retention = data.get('security_retention_days', 180)

        if error_retention < retention_days:
            raise serializers.ValidationError({
                'error_retention_days': 'Error retention should be >= general log retention'
            })

        if security_retention < error_retention:
            raise serializers.ValidationError({
                'security_retention_days': 'Security retention should be >= error retention'
            })

        return data


class LogConfigSummarySerializer(serializers.ModelSerializer):
    """Simplified serializer for configuration summaries"""
    alert_count = serializers.SerializerMethodField()

    class Meta:
        model = LogConfig
        fields = [
            'id', 'name', 'description', 'is_active', 'level',
            'created_at', 'updated_at', 'alert_count'
        ]

    def get_alert_count(self, obj):
        """Get count of alert email addresses"""
        return len(obj.get_alert_emails())


class PerformanceLogSerializer(serializers.ModelSerializer):
    """Serializer for performance logs"""
    user = UserBasicSerializer(read_only=True)
    
    class Meta:
        model = PerformanceLog
        fields = [
            'id', 'path', 'method', 'duration', 'memory_usage', 'cpu_usage',
            'db_queries_count', 'db_queries_time', 'user', 'user_agent',
            'ip_address', 'response_size', 'status_code', 'created_at'
        ]


class ErrorLogSerializer(serializers.ModelSerializer):
    """Serializer for error logs"""
    user = UserBasicSerializer(read_only=True)
    resolved_by = UserBasicSerializer(read_only=True)
    
    class Meta:
        model = ErrorLog
        fields = [
            'id', 'view_name', 'error_type', 'severity', 'error_message',
            'stack_trace', 'request_data', 'response_data', 'user',
            'ip_address', 'user_agent', 'path', 'method', 'status_code',
            'resolved', 'resolved_by', 'resolved_at', 'resolution_notes',
            'timestamp'
        ]


class UserActivitySerializer(serializers.ModelSerializer):
    """Serializer for user activities"""
    user = UserBasicSerializer(read_only=True)
    
    class Meta:
        model = UserActivity
        fields = [
            'id', 'user', 'activity_type', 'action', 'description',
            'ip_address', 'user_agent', 'session_key', 'path', 'method',
            'success', 'metadata', 'timestamp'
        ]


class APIAccessLogSerializer(serializers.ModelSerializer):
    """Serializer for API access logs"""
    user = UserBasicSerializer(read_only=True)
    
    class Meta:
        model = APIAccessLog
        fields = [
            'id', 'endpoint', 'method', 'user', 'ip_address', 'user_agent',
            'request_headers', 'request_body', 'response_status', 'response_size',
            'response_time', 'api_key', 'rate_limited', 'cached_response',
            'timestamp'
        ]


class DatabaseQueryLogSerializer(serializers.ModelSerializer):
    """Serializer for database query logs"""
    user = UserBasicSerializer(read_only=True)
    
    class Meta:
        model = DatabaseQueryLog
        fields = [
            'id', 'query_type', 'table_name', 'query_hash', 'execution_time',
            'rows_affected', 'query_plan', 'user', 'view_name', 'slow_query',
            'timestamp'
        ]


class AuthenticationLogSerializer(serializers.ModelSerializer):
    """Serializer for authentication logs"""
    user = UserBasicSerializer(read_only=True)
    
    class Meta:
        model = AuthenticationLog
        fields = [
            'id', 'user', 'username_attempted', 'event_type', 'ip_address',
            'user_agent', 'session_key', 'success', 'failure_reason',
            'additional_data', 'timestamp'
        ]


class SecurityIncidentSerializer(serializers.ModelSerializer):
    """Serializer for security incidents"""
    user = UserBasicSerializer(read_only=True)
    resolved_by = UserBasicSerializer(read_only=True)
    
    class Meta:
        model = SecurityIncident
        fields = [
            'id', 'incident_type', 'severity', 'description', 'ip_address',
            'user_agent', 'user', 'path', 'request_data', 'blocked',
            'resolved', 'resolved_by', 'resolved_at', 'resolution_notes',
            'timestamp'
        ]


class SystemHealthLogSerializer(serializers.ModelSerializer):
    """Serializer for system health logs"""
    
    class Meta:
        model = SystemHealthLog
        fields = [
            'id', 'metric_type', 'value', 'unit', 'threshold_warning',
            'threshold_critical', 'status', 'additional_info', 'timestamp'
        ]


class LogSummarySerializer(serializers.Serializer):
    """Serializer for log summary data"""
    total_errors = serializers.IntegerField()
    total_requests = serializers.IntegerField()
    unique_users = serializers.IntegerField()
    avg_response_time = serializers.FloatField()
    critical_errors = serializers.IntegerField()
    unresolved_incidents = serializers.IntegerField()


class DashboardMetricsSerializer(serializers.Serializer):
    """Serializer for dashboard metrics"""
    timestamp = serializers.DateTimeField()
    real_time_metrics = serializers.DictField()
    daily_metrics = serializers.DictField()
    system_status = serializers.DictField()


class AnalyticsDataSerializer(serializers.Serializer):
    """Serializer for analytics data"""
    period_days = serializers.IntegerField()
    generated_at = serializers.DateTimeField()
    error_summary = serializers.DictField()
    performance_summary = serializers.DictField()
    user_activity_summary = serializers.DictField()
    security_summary = serializers.DictField()
