from django.forms import ValidationError
from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework import status
from rest_framework.permissions import IsAuthenticated

from contributor.permissions import IsContributorUser
from questions.models import Module, Paper, Section, SubCourse, Tier, Question, Option
from students.models import Student
from students.permissions import IsStudentUser
from .models import TestPattern, TestPaper, SubmitTest, PracticeRecord
from .serializers import TestPatternSerializer, TestPaperSerializer, SectionSummarySerializer

from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework import status
from rest_framework.generics import get_object_or_404
from rest_framework.exceptions import NotFound
from django.utils import timezone
import json
from django.core.cache import cache


class TestPatternAPIView(APIView):
    permission_classes = (IsContributorUser,)

    def get(self, request, pk=None):
        """
        Retrieve a specific test pattern by pk for the logged-in user,
        or retrieve all test patterns if no pk is provided.
        """
        if pk:
            # Fetch a specific test pattern by pk
            try:
                test_pattern = TestPattern.objects.get(
                    pk=pk, contributor__user=request.user
                )
            except TestPattern.DoesNotExist:
                raise NotFound(
                    f"TestPattern with id {pk} not found for the current user."
                )
            serializer = TestPatternSerializer(test_pattern)
            return Response(serializer.data, status=status.HTTP_200_OK)
        else:
            # inside else (fetch all patterns for current user)
            cache_key = f"test_patterns_user_{request.user.id}"
            cached_patterns = cache.get(cache_key)

            if cached_patterns is not None:
                return Response(cached_patterns, status=status.HTTP_200_OK)
            # Fetch all test patterns for the current user
            test_patterns = TestPattern.objects.filter(contributor__user=request.user)[
                ::-1
            ]
            serializer = TestPatternSerializer(test_patterns, many=True)
            return Response(serializer.data, status=status.HTTP_200_OK)

    def post(self, request):
        """
        Create a new test pattern.
        """

        serializer = TestPatternSerializer(
            data=request.data, context={"request": request}
        )
        if serializer.is_valid():
            serializer.save()
            cache.delete(f"test_patterns_user_{request.user.id}")
            return Response(serializer.data, status=status.HTTP_201_CREATED)
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

    def put(self, request, pk):
        """
        Update an existing test pattern by ID.
        """
        try:
            test_pattern = TestPattern.objects.get(
                pk=pk, contributor__user=request.user
            )
        except TestPattern.DoesNotExist:
            return Response(
                {"error": "Test pattern not found or not accessible"},
                status=status.HTTP_404_NOT_FOUND,
            )

        serializer = TestPatternSerializer(
            test_pattern, data=request.data, partial=True, context={"request": request}
        )
        if serializer.is_valid():
            serializer.save()
            return Response(serializer.data, status=status.HTTP_200_OK)
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

    def delete(self, request, pk):
        """
        Delete a specific test pattern by ID.
        """
        try:
            test_pattern = TestPattern.objects.get(
                pk=pk, contributor__user=request.user
            )
        except TestPattern.DoesNotExist:
            return Response(
                {"error": "Test pattern not found or not accessible"},
                status=status.HTTP_404_NOT_FOUND,
            )

        # Logic handled in serializer
        serializer = TestPatternSerializer(instance=test_pattern)
        delete_response = serializer.delete(test_pattern)
        cache.delete(f"test_patterns_user_{request.user.id}")
        return Response(delete_response, status=status.HTTP_204_NO_CONTENT)





class GeneratePaper(APIView):
    permission_classes = (IsStudentUser,)

    def post(self, request):
        """
        Generate paper based on entity type (tier/paper/section/module) and ID
        """
        
        entity_type = request.data.get("entity_type")
        entity_id = request.data.get("entity_id")
        student_id = request.data.get("student_id")
       

        if not all([entity_type, entity_id, student_id]):
            return Response(
                {"message": "Missing required fields"},
                status=status.HTTP_400_BAD_REQUEST,
            )

        try:
            
            student = Student.objects.get(id=student_id)
            
            # Get subcourse based on entity type
            if entity_type == "tier":
                cache_key = f"tier_subcourse_{entity_id}"
                subcourse = cache.get(cache_key)
                if not subcourse:
                    tier = Tier.objects.get(tier_id=entity_id)
                    subcourse = tier.subcourse
                    cache.set(cache_key, subcourse, 300)
            elif entity_type == "paper":
                cache_key = f"paper_subcourse_{entity_id}"
                subcourse = cache.get(cache_key)
                if not subcourse:
                    paper = Paper.objects.get(paper_id=entity_id)
                    paper.student =student
                    paper.save()
                    subcourse = paper.tier.subcourse
                    cache.set(cache_key, subcourse, 300)
                
            elif entity_type == "section":
                section = Section.objects.get(section_id=entity_id)
                subcourse = section.paper.tier.subcourse
            elif entity_type == "module":
                module = Module.objects.get(module_id=entity_id)
                subcourse = module.section.paper.tier.subcourse
            else:
                return Response(
                    {"message": "Invalid entity type"},
                    status=status.HTTP_400_BAD_REQUEST,
                )
        except (
            Student.DoesNotExist,
            Tier.DoesNotExist,
            Paper.DoesNotExist,
            Section.DoesNotExist,
            Module.DoesNotExist,
        ) as e:
            return Response(
                {"message": f"{str(e.__class__.__name__)} not found"},
                status=status.HTTP_404_NOT_FOUND,
            )
        
        data = {
            "subcourse": subcourse.subcourse_id,
            "student": student.id,
            "status": "generated",
            "entity_type": entity_type,
            "entity_id": entity_id,
            "total_marks": 0,
            "time_limit": 60,
            "duration_in_minutes": 60,
        }

        existing_paper = TestPaper.objects.filter(
            entity_type=entity_type,
            entity_id=entity_id,
            student=student,
            status="generated"
        ).first()

        if existing_paper:
            serializer = TestPaperSerializer(existing_paper)
            return Response(serializer.data, status=status.HTTP_200_OK)

        serializer = TestPaperSerializer(data=data)
        

        if serializer.is_valid():
            paper = serializer.save()
            # transformed_paper = transform_paper_format(paper)
            return Response(paper, status=status.HTTP_201_CREATED)
           
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)
    
    
    def get(self, request, student_id=None):
        try:
            student = Student.objects.get(id=student_id)
            test_pat = TestPaper.objects.get(student=student)
        except (
            Student.DoesNotExist,
            TestPaper.DoesNotExist,
      
        ) as e:
            return Response(
                {"message": f"{str(e.__class__.__name__)} not found"},
                status=status.HTTP_404_NOT_FOUND,
            )
        
        serializer = TestPaperSerializer(test_pat)
        transformed_response= serializer.data
        return Response(transformed_response, status=status.HTTP_200_OK)
    
from datetime import datetime, timedelta
from .utils import *
from django.db.models import Q
from django.db.models import Avg, Count

def calculate_student_score(test):
    # Normalize out of 100
    accuracy = test.accuracy  # already in %
    section_avg = sum(test.section_scores.values()) / len(test.section_scores) if test.section_scores else 0
    practice_streak = PracticeRecord.get_continuous_practice(test.student.user)

    # Heuristic scoring formula
    score = (accuracy * 0.5) + (section_avg * 0.3) + (practice_streak * 2)
    return round(score, 2)

from .models import ExamTier
def map_score_to_exam(score):
    from .models import ExamTier  # wherever your model is

    tier = ExamTier.objects.filter(min_score__lte=score, max_score__gte=score).first()
    if tier:
        return tier.name, tier.exams
    return "Unclassified", []
    
from collections import defaultdict
from django.db import transaction, IntegrityError
from rest_framework.exceptions import APIException

class TestTimeoutError(APIException):
    status_code = 408
    default_detail = 'Test submission window has expired'
    default_code = 'test_timeout'

class TestInProgressError(APIException):
    status_code = 409
    default_detail = 'Another test is already in progress'
    default_code = 'test_in_progress'

class SubmitTestView(APIView):
    
    @transaction.atomic
    def post(self, request, paper_id):
        """
        Handles test submission by evaluating answers and calculating total marks.
        """
        try:
            with transaction.atomic():
                test_paper = get_object_or_404(TestPaper, paper_id=paper_id)

                # Check for existing in-progress tests
                in_progress_tests = SubmitTest.objects.filter(
                    student=request.user.student_profile,
                    status="in_progress"
                ).exclude(test_paper_id=test_paper)

                if in_progress_tests.exists():
                    raise TestInProgressError()

                # Enhanced timeout check
                if timezone.now() > test_paper.expires_at:
                    test_paper.status = "timeout"
                    test_paper.save()
                    return Response({"error": "Time expired. Test not submitted."}, status=400)

                if test_paper.status == "Completed":
                    return Response({"error": "Test already submitted."}, status=status.HTTP_400_BAD_REQUEST)

                if timezone.now() - test_paper.created_at > timedelta(hours=2):
                    return Response({"error": "Test window expired."}, status=400)

                submitted_answers = request.data.get("answers")
                if not submitted_answers:
                    return Response({"error": "No answers submitted."}, status=status.HTTP_400_BAD_REQUEST)

                # Validate answers
                question_ids_seen = set()
                for i, answer in enumerate(submitted_answers):
                    if not isinstance(answer, dict):
                        return Response({"error": f"Answer at index {i} must be a dictionary."}, status=400)
                    required_keys = ["question_id", "selected_option", "marks", "negative_marks", "section"]
                    missing_keys = [key for key in required_keys if key not in answer]
                    if missing_keys:
                        return Response({"error": f"Missing keys in answer at index {i}: {missing_keys}"}, status=400)

                    if not isinstance(answer["question_id"], int):
                        return Response({"error": f"question_id at index {i} must be an integer."}, status=400)
                    if answer["question_id"] in question_ids_seen:
                        return Response({"error": f"Duplicate question_id {answer['question_id']} at index {i}."}, status=400)
                    question_ids_seen.add(answer["question_id"])

                    if not isinstance(answer["selected_option"], int):
                        return Response({"error": f"selected_option at index {i} must be an integer."}, status=400)

                    if not isinstance(answer["marks"], (int, float)) or answer["marks"] < 0:
                        return Response({"error": f"Invalid marks at index {i}."}, status=400)
                    if not isinstance(answer["negative_marks"], (int, float)) or answer["negative_marks"] < 0:
                        return Response({"error": f"Invalid negative_marks at index {i}."}, status=400)
                    if not isinstance(answer["section"], str):
                        return Response({"error": f"section must be a string at index {i}."}, status=400)

                # Evaluate answers
                attempet_count = 0
                total_score = 0
                negative_score = 0
                correct_answers = 0
                incorrect_answers = 0
                section_scores = {}
                section_wise_stats = defaultdict(lambda: {"correct": 0, "attempt": 0})
                section_wise_attempted_or_not_question = defaultdict(lambda: {"attempted": 0, "not_attempted": 0})
                paper = test_paper.questions

                for answer in submitted_answers:
                    attempet_count += 1
                    question_id = answer.get("question_id")
                    selected_option = answer.get("selected_option")
                    section_name = answer.get("section")

                    try:
                        question = Question.objects.get(question_id=question_id)
                    except Question.DoesNotExist:
                        return Response({"error": f"Question with id {question_id} not found."}, status=status.HTTP_404_NOT_FOUND)

                    option = Option.objects.filter(option_id=selected_option).first()
                    section_wise_stats[section_name]["attempt"] += 1
                    section_wise_attempted_or_not_question[section_name]["attempted"] += 1

                    if option and option.is_correct:
                        total_score += answer["marks"]
                        correct_answers += 1
                        section_wise_stats[section_name]["correct"] += 1
                        section_scores[section_name] = section_scores.get(section_name, 0) + answer["marks"]
                    else:
                        negative_score += answer["negative_marks"]
                        incorrect_answers += 1
                        section_scores[section_name] = section_scores.get(section_name, 0) - answer["negative_marks"]

                final_score = total_score - negative_score
                total_questions = sum(len(q["subquestions"]) for p in paper for q in p["questions"])
                accuracy = (correct_answers / total_questions) * 100 if total_questions > 0 else 0

                # Save test paper results
                test_paper.total_marks = final_score
                test_paper.save()

                test_score = SubmitTest.objects.create(
                    student=request.user.student_profile,
                    test_paper_id=test_paper,
                    date=timezone.now(),
                    total_score=final_score,
                    correct_answers=correct_answers,
                    incorrect_answers=incorrect_answers,
                    negative_marks=negative_score,
                    accuracy=accuracy,
                    section_scores=section_scores,
                )

                points, section_results = calculate_points(test_score)
                final_points = apply_consistency_bonus(request.user, points, test_score)

                tracker, _ = ScoreTracker.objects.get_or_create(user=request.user)
                tracker.points += final_points
                tracker.last_test_date = test_score.date
                tracker.save()

                daily_tracker, _ = PracticeRecord.objects.get_or_create(user=request.user, date=datetime.today())
                daily_tracker.questions_practiced += total_questions
                daily_tracker.save()

                section_wise_subquestion_count = defaultdict(int)
                for pg in paper:
                    section_wise_subquestion_count[pg['section_name']] = sum(len(q.get("subquestions", [])) for q in pg["questions"])

                section_accuracy = {
                    section: round((stats["correct"] / total_questions) * 100, 2) if attempet_count > 0 else 0.0
                    for section, stats in section_wise_stats.items()
                }

                for section, stats in section_wise_stats.items():
                    total_section_questions = section_wise_subquestion_count[section]
                    section_wise_attempted_or_not_question[section]["not_attempted"] = total_section_questions - stats["attempt"]

                section_correct_question = {
                    section: stats["correct"] if attempet_count > 0 else 0
                    for section, stats in section_wise_stats.items()
                }

                context = {
                    "chart_data": {
                        "labels": ["Answered", "Not Answered"],
                        "datasets": [{
                            "label": "Number of Questions",
                            "data": [attempet_count, total_questions - attempet_count],
                            "backgroundColor": ['#28a745', '#dc3545'],
                        }]
                    },
                    "total_questions": total_questions,
                    "not_attempted": total_questions - attempet_count,
                    "message": "Test submitted successfully!",
                    "total_score": final_score,
                    "correct_answers": correct_answers,
                    "incorrect_answers": incorrect_answers,
                    "negative_marks": negative_score,
                    "accuracy_percentage": accuracy,
                    "section_accuracy_percentage": section_accuracy,
                    "section_score": section_scores,
                    "section_questions": section_wise_subquestion_count,
                    "section_correct_questions": section_correct_question,
                    "section_wise_attempted_or_not_question": section_wise_attempted_or_not_question,
                    "points": points
                }

                # Add scoring and exam recommendations
                score = calculate_student_score(test_score)
                tier, suggested_exams = map_score_to_exam(score)
                test_score.recommended_tier = tier
                test_score.save()

                context.update({
                    "student_score": score,
                    "recommended_tier": tier,
                    "suggested_exams": suggested_exams,
                })

                return Response(context, status=status.HTTP_200_OK)

        except (IntegrityError, APIException) as e:
            # Rollback on known exceptions
            transaction.set_rollback(True)
            return Response({"error": str(e)}, status=status.HTTP_400_BAD_REQUEST)
        except Exception as e:
            # Catch-all for unhandled errors
            transaction.set_rollback(True)
            return Response({"error": "Unexpected server error", "details": str(e)}, status=500)
        
    # def post(self, request, paper_id):
    #     """
    #     Handles test submission by evaluating answers and calculating total marks.
    #     """
        
    #     test_paper = get_object_or_404(TestPaper, paper_id=paper_id)

    #      # Check for existing in-progress tests
    #     in_progress_tests = SubmitTest.objects.filter(
    #         student=request.user.student_profile,
    #         status="in_progress"
    #     ).exclude(test_paper_id=test_paper)
        
    #     if in_progress_tests.exists():
    #         raise TestInProgressError()
        
    #     # Enhanced timeout check
    #     time_elapsed = timezone.now() - test_paper.created_at
    #     if time_elapsed > timedelta(hours=2):
    #         test_paper.status = "timeout"
    #         test_paper.save()
    #         raise TestTimeoutError()
        
    #     paper = test_paper.questions

    #     if test_paper.status == "Completed":
    #         return Response({"error": "Test already submitted."}, status=status.HTTP_400_BAD_REQUEST)
        
    #     if datetime.now() - test_paper.created_at > timedelta(hours=2):
    #         return Response({"error": "Test window expired."}, status=400)

    #     submitted_answers = request.data.get("answers")

    #     if not submitted_answers:
    #         return Response({"error": "No answers submitted."}, status=status.HTTP_400_BAD_REQUEST)
        
    #     # Validate all submitted answers
    #     question_ids_seen = set()
    #     for i, answer in enumerate(submitted_answers):
    #         if not isinstance(answer, dict):
    #             return Response({"error": f"Answer at index {i} must be a dictionary."}, status=400)

    #         required_keys = ["question_id", "selected_option", "marks", "negative_marks", "section"]
    #         missing_keys = [key for key in required_keys if key not in answer]
    #         if missing_keys:
    #             return Response({"error": f"Missing keys in answer at index {i}: {missing_keys}"}, status=400)

    #         if not isinstance(answer["question_id"], int):
    #             return Response({"error": f"question_id at index {i} must be an integer."}, status=400)
            
    #         if answer["question_id"] in question_ids_seen:
    #             return Response({"error": f"Duplicate question_id {answer['question_id']} at index {i}."}, status=400)
    #         question_ids_seen.add(answer["question_id"])

    #         if not isinstance(answer["selected_option"], int):
    #             return Response({"error": f"selected_option at index {i} must be an integer."}, status=400)

    #         if not isinstance(answer["marks"], (int, float)) or answer["marks"] < 0:
    #             return Response({"error": f"Invalid marks at index {i}."}, status=400)

    #         if not isinstance(answer["negative_marks"], (int, float)) or answer["negative_marks"] < 0:
    #             return Response({"error": f"Invalid negative_marks at index {i}."}, status=400)

    #         if not isinstance(answer["section"], str):
    #             return Response({"error": f"section must be a string at index {i}."}, status=400)


    #     attempet_count = 0
    #     total_score = 0
    #     negative_score = 0
    #     correct_answers = 0
    #     incorrect_answers = 0
    #     section_scores = {}
    #     section_wise_stats = defaultdict(lambda: {"correct": 0,"attempt": 0})# 
    #     section_wise_attempted_or_not_question = defaultdict(lambda: {"attempted": 0, "not_attempted": 0})
    #     for answer in submitted_answers:
    #         attempet_count += 1
            
    #         question_id = answer.get("question_id")
    #         selected_option = answer.get("selected_option")  # Assume option is an ID

    #         try:
    #             question = Question.objects.get(question_id=question_id)
    #         except Question.DoesNotExist:
    #             return Response({"error": f"Question with id {question_id} not found."}, status=status.HTTP_404_NOT_FOUND)

    #         options = Option.objects.filter(option_id=selected_option).first()
    #         section_name = answer.get("section")
    #         section_wise_stats[section_name]["attempt"] += 1
    #         section_wise_attempted_or_not_question[section_name]["attempted"] += 1
    #         if options:
    #             if options.is_correct:
    #                 total_score += answer.get("marks")  # Add positive marks
    #                 correct_answers += 1
    #                 section_wise_stats[section_name]["correct"] += 1
    #                 section_scores[section_name] = section_scores.get(section_name, 0) + answer.get("marks")
    #             else:
    #                 negative_score += answer.get("negative_marks")  # Apply negative marking
    #                 incorrect_answers += 1
    #                 section_scores[section_name] = section_scores.get(section_name, 0) - answer.get("negative_marks")
    #         else:
    #             negative_score += answer.get("negative_marks")  # Apply negative marking
    #             incorrect_answers += 1
    #             section_scores[section_name] = section_scores.get(section_name, 0) - answer.get("negative_marks")

    #     final_score = total_score - negative_score  # Calculate final score
    #     total_questions=0
    #     for pgr in paper:
    #         total_questions += sum(len(q["subquestions"]) for q in pgr["questions"])

    #     accuracy = (correct_answers / total_questions) * 100 if total_questions > 0 else 0

    #     # Update the TestPaper status
    #     test_paper.total_marks = final_score
    #     # test_paper.status = "Completed"
    #     test_paper.save()

    #     test_score = SubmitTest.objects.create(
    #         student=Student.objects.get(user=request.user),
    #         test_paper_id=test_paper,
    #         date=datetime.now(),
    #         total_score=final_score,
    #         correct_answers=correct_answers,
    #         incorrect_answers=incorrect_answers,
    #         negative_marks=negative_score,
    #         accuracy=accuracy,
    #         section_scores=section_scores,
    #     )

    #     points, section_results = calculate_points(test_score)
    #     final_points = apply_consistency_bonus(request.user, points, test_score)

    #     tracker, _ = ScoreTracker.objects.get_or_create(user=request.user)
    #     tracker.points += final_points
    #     tracker.last_test_date = test_score.date
    #     tracker.save()

    #     daily_tracker, _ = PracticeRecord.objects.get_or_create(user=request.user, date=datetime.today())
    #     daily_tracker.questions_practiced += total_questions
    #     daily_tracker.save()

    #             # Dictionary to store section-wise subquestion counts
    #     section_wise_subquestion_count = defaultdict(int)
    #     # Count subquestions
    #     for pg in paper:
    #         section_wise_subquestion_count[pg['section_name']] = sum(len(q.get("subquestions", [])) for q in pg["questions"])

    #     section_accuracy = {
    #         section: round((stats["correct"] / total_questions) * 100, 2) if attempet_count > 0 else 0.0
    #         for section, stats in section_wise_stats.items()
    #     }

    #     for section, stats in section_wise_stats.items():
    #         total_section_questions = section_wise_subquestion_count[section]
    #         t = (total_section_questions - stats["attempt"])
    #         section_wise_attempted_or_not_question[section]["not_attempted"] = t 

    #     section_correct_question = {
    #         section: stats["correct"] if attempet_count > 0 else 0.0
           
    #         for section, stats in section_wise_stats.items()
    #     }
    #     context = {
    #          "chart_data": {
    #                 "labels": ["Answered", "Not Answered"],
    #             "datasets":[
    #                 {
    #                     "label": "Number of Questions",
    #                     "data" : [attempet_count, total_questions-attempet_count],
    #                     'backgroundColor': ['#28a745', '#dc3545'],
    #                 }
    #             ],
    #         },
            
    #         "total_questions": total_questions,
    #         "not_attempted": total_questions-attempet_count,
    #         "message": "Test submitted successfully!",
    #         "total_score": final_score,
    #         "correct_answers": correct_answers,
    #         "incorrect_answers": incorrect_answers,
    #         "negative_marks": negative_score,
    #         "accuracy_percentage": accuracy,
    #         "section_accuracy_percentage": section_accuracy,
    #         "section_score": section_scores,
    #         "section_questions": section_wise_subquestion_count,
    #         "section_correct_questions": section_correct_question,
    #         "section_wise_attempted_or_not_question":section_wise_attempted_or_not_question,
    #         "points": points
    #     }

    #     # Save the test score to the database
    #     score = calculate_student_score(test_score)
    #     tier, suggested_exams = map_score_to_exam(score)
    #     test_score.recommended_tier = tier
    #     test_score.save()

    #     context.update({
    #         "student_score": score,
    #         "recommended_tier": tier,
    #         "suggested_exams": suggested_exams,
    #     })

    #     return Response(
    #         context,
    #         status=status.HTTP_200_OK,
    #     )

from rest_framework.decorators import api_view
from django.db.models import Max
from questions.models import Topic

@api_view(['GET'])
def get_distinct_sections_with_rank(request):
    # Get all TestPattern instances ordered by rank (descending)
    test_patterns = TestPattern.objects.all().order_by('-rank')

    # Aggregate sections by section_name
    section_data = defaultdict(lambda: {"topics": [], "rank": 0})

    for pattern in test_patterns:
        sections = pattern.sections
        for section in sections:
            section_name = section.get('section_name')

            # Add rank for the section if it's higher than the current one
            section_data[section_name]["rank"] = max(section_data[section_name]["rank"], pattern.rank)

            # Get topics and subtopics
            subject_ids = section.get('subject_ids', [])
            topics = Topic.objects.filter(subject_id__in=subject_ids).prefetch_related('subtopics')

            for topic in topics:
                topic_data = {
                    "topic_id": topic.topic_id,
                    "name": topic.name,
                    "subtopics": [
                        {
                            "subtopic_id": subtopic.subtopic_id,
                            "name": subtopic.name
                        }
                        for subtopic in topic.subtopics.all()[::-1]
                    ]
                }

                # Avoid duplicate topics
                if topic_data not in section_data[section_name]["topics"]:
                    section_data[section_name]["topics"].append(topic_data)

    # Prepare final list
    src =1
    all_sections = []
    for section_name, data in section_data.items():
        all_sections.append({
            "section_id": src,
            "section_name": section_name,
            "rank": data["rank"],
            "topics": data["topics"]
        })
        src+=1

    # Sort by rank in descending order
    all_sections = sorted(all_sections, key=lambda x: x['rank'], reverse=True)

    return Response(all_sections)


@api_view(['POST'])
def increment_section_rank(request):
    section_name = request.data.get('section_name')

    if not section_name:
        return Response({"error": "section_name is required"}, status=400)

    # Get all TestPattern instances
    test_patterns = TestPattern.objects.all()

    # Find the first matching pattern
    matched_pattern = None
    for pattern in test_patterns:
        sections = pattern.sections
        for section in sections:
            if section.get('section_name') == section_name:
                matched_pattern = pattern
                break
        if matched_pattern:
            break  # Stop after the first match

    if not matched_pattern:
        return Response({"error": "Section not found"}, status=404)

    # Increment the rank of the first matched pattern
    matched_pattern.rank += 1
    matched_pattern.save()

    return Response({"success": f"Rank of '{section_name}' incremented successfully"})

class StudentExamRecommendation(APIView):
    permission_classes = [IsAuthenticated]

    def get(self, request, student_id):
        student = get_object_or_404(Student, id=student_id)
        latest_test = SubmitTest.objects.filter(student=student).order_by("-date").first()
        if not latest_test:
            return Response({"message": "No test found for student."}, status=404)

        score = calculate_student_score(latest_test)
        tier, exams = map_score_to_exam(score)

        return Response({
            "student_score": score,
            "recommended_tier": tier,
            "suggested_exams": exams,
        }, status=200)

class StartTestView(APIView):
    def post(self, request, paper_id):
        test_paper = get_object_or_404(TestPaper, paper_id=paper_id, student=request.user.student_profile)

        if test_paper.status == "Completed":
            return Response({"error": "Test already submitted."}, status=400)

        if test_paper.started_at:
            return Response({"message": "Test already started."}, status=200)

        now = timezone.now()
        test_paper.started_at = now
        test_paper.expires_at = now + timedelta(minutes=test_paper.time_limit or 120)
        test_paper.status = "in_progress"
        test_paper.save()

        return Response({
            "message": "Test started.",
            "expires_at": test_paper.expires_at
        }, status=200)

class ResumeTestView(APIView):
    def get(self, request, paper_id):
        test_paper = get_object_or_404(TestPaper, paper_id=paper_id, student=request.user.student_profile)
        if not test_paper.started_at:
            return Response({"error": "Test not started yet."}, status=400)

        if timezone.now() > test_paper.expires_at:
            return Response({"error": "Test time expired."}, status=400)

        return Response({
            "status": test_paper.status,
            "expires_at": test_paper.expires_at
        }, status=200)
